$(function(){
	talkmgr.load();
	_admin_params.paging = _paging;

	talkmgr.initable('.js-data-table', {
        paging: {
			..._admin_params.paging,
			sizeChange: function (p) {
				_admin_params.paging = p;
				talkmgr.submit();
            },
            pageChange: function (p) {
				_admin_params.paging = p;
				talkmgr.submit();
            },
			all:1,
        },
    });

	// Function to format the child row content
	function formatChildRow(executionData) {
		return '<div class="child-row-content">' +
			'<!-- Execution Results Group -->' +
			'<div class="info-group">' +
				'<h4 class="info-group-header">' +
					'<i class="fa fa-bar-chart"></i> 実行結果' +
				'</h4>' +
				'<div class="info-group-body">' +
					'<div class="info-row">' +
						'<span class="info-label">実行時間:</span>' +
						'<span class="info-value">' + (executionData.duration_formatted || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">終了コード:</span>' +
						'<span class="info-value">' + (executionData.exit_code || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">処理件数:</span>' +
						'<span class="info-value">' +
							'処理: ' + (executionData.records_processed ? parseInt(executionData.records_processed).toLocaleString() : '0') + ' / ' +
							'成功: ' + (executionData.records_success ? parseInt(executionData.records_success).toLocaleString() : '0') + ' / ' +
							'失敗: ' + (executionData.records_failed ? parseInt(executionData.records_failed).toLocaleString() : '0') +
						'</span>' +
					'</div>' +
					(executionData.error_message ? 
						'<div class="info-row">' +
							'<span class="info-label">エラー情報:</span>' +
							'<span class="info-value error-message">' +
								'[' + (executionData.error_code || '') + '] ' + executionData.error_message +
							'</span>' +
						'</div>' : '') +
				'</div>' +
			'</div>' +
			
			'<!-- Retry Control Group -->' +
			'<div class="info-group">' +
				'<h4 class="info-group-header">' +
					'<i class="fa fa-repeat"></i> リトライ制御' +
				'</h4>' +
				'<div class="info-group-body">' +
					'<div class="info-row">' +
						'<span class="info-label">リトライ回数:</span>' +
						'<span class="info-value">' +
							(executionData.retry_count ? parseInt(executionData.retry_count).toLocaleString() : '0') + ' / ' +
							(executionData.max_retries ? parseInt(executionData.max_retries).toLocaleString() : '0') +
						'</span>' +
					'</div>' +
					(executionData.next_retry_time ? 
						'<div class="info-row">' +
							'<span class="info-label">次回リトライ:</span>' +
							'<span class="info-value">' + executionData.next_retry_time + '</span>' +
						'</div>' : '') +
					(executionData.retry_reason ? 
						'<div class="info-row">' +
							'<span class="info-label">リトライ理由:</span>' +
							'<span class="info-value">' + executionData.retry_reason + '</span>' +
						'</div>' : '') +
					'<div class="info-row">' +
						'<span class="info-label">当日成功回数:</span>' +
						'<span class="info-value">' + (executionData.daily_success_count ? parseInt(executionData.daily_success_count).toLocaleString() : '0') + '</span>' +
					'</div>' +
				'</div>' +
			'</div>' +
			
			'<!-- Timestamp Group -->' +
			'<div class="info-group">' +
				'<h4 class="info-group-header">' +
					'<i class="fa fa-clock-o"></i> タイムスタンプ' +
				'</h4>' +
				'<div class="info-group-body">' +
					'<div class="info-row">' +
						'<span class="info-label">予定時刻:</span>' +
						'<span class="info-value">' + (executionData.scheduled_time || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">開始時刻:</span>' +
						'<span class="info-value">' + (executionData.actual_start_time || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">終了時刻:</span>' +
						'<span class="info-value">' + (executionData.actual_end_time || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">作成日時:</span>' +
						'<span class="info-value">' + (executionData.created_at || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">更新日時:</span>' +
						'<span class="info-value">' + (executionData.updated_at || '-') + '</span>' +
					'</div>' +
				'</div>' +
			'</div>' +
			
			'<!-- System Information Group -->' +
			'<div class="info-group">' +
				'<h4 class="info-group-header">' +
					'<i class="fa fa-cog"></i> システム情報' +
				'</h4>' +
				'<div class="info-group-body">' +
					'<div class="info-row">' +
						'<span class="info-label">実行タイプ:</span>' +
						'<span class="info-value">' + (executionData.type_label || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">実行サーバー:</span>' +
						'<span class="info-value">' + (executionData.execution_server || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">プロセスID:</span>' +
						'<span class="info-value">' + (executionData.process_id || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">作成者:</span>' +
						'<span class="info-value">' + (executionData.created_by || '-') + '</span>' +
					'</div>' +
					'<div class="info-row">' +
						'<span class="info-label">更新者:</span>' +
						'<span class="info-value">' + (executionData.updated_by || '-') + '</span>' +
					'</div>' +
				'</div>' +
			'</div>' +
		'</div>';
	}
	
	$('#searchButton').click(function() {
		talkmgr.submit();
	});

	$('#manualExecuteButton').click(function() {
		var batchTypeSelect = $('#manual_batch_type');
		if (!batchTypeSelect.val()) {
			alert('バッチを選択してください。');
			return;
		}
		
		var selectedOption = batchTypeSelect.find('option:selected');
		if (confirm('以下バッチの手動実行を開始しますか？' + selectedOption.text())) {
			talkmgr.submit('manual_execute');
		}
	});

	$(document).on('click', '.toggle-details', function(e) {
		e.preventDefault();
		
		var $button = $(this);
		var $icon = $button.find('i');
		var $row = $button.closest('tr');
		var executionId = $row.attr('data-execution-id');
		var $existingDetailRow = $row.next('.child-detail-row');
		
		// Close all other open detail rows
		$('.child-detail-row').each(function() {
			var $detailRow = $(this);
			var $parentRow = $detailRow.prev('.execution-row');
			if ($parentRow.attr('data-execution-id') !== executionId) {
				$detailRow.remove();
				$parentRow.find('.toggle-details i').removeClass('fa-minus').addClass('fa-plus');
			}
		});
		
		// Toggle the clicked row's child row
		if ($existingDetailRow.length) {
			// Hide child row
			$existingDetailRow.remove();
			$icon.removeClass('fa-minus').addClass('fa-plus');
			$button.blur();
		} else {
			// Show child row
			try {
				var executionData = JSON.parse($row.attr('data-execution-details'));
				var childContent = formatChildRow(executionData);
				
				// Create child row element
				var $childRow = $('<tr class="child-detail-row">');
				$childRow.append('<td colspan="6">' + childContent + '</td>');
				
				// Insert after current row
				$row.after($childRow);
				$icon.removeClass('fa-plus').addClass('fa-minus');
			} catch (e) {
				console.error('Error parsing execution data:', e);
				
				// Create error child row
				var $errorRow = $('<tr class="child-detail-row">');
				$errorRow.append('<td colspan="6"><div class="child-row-content"><p>データの読み込みに失敗しました。</p></div></td>');
				$row.after($errorRow);
				$icon.removeClass('fa-plus').addClass('fa-minus');
			}
		}
	});

	// Running status notification and auto-refresh
	if ($('.status-running').length > 0) {
		// Show notification
		var notification = $('<div>')
			.css({
				'position': 'fixed',
				'top': '20px',
				'right': '20px',
				'background': '#17a2b8',
				'color': 'white',
				'padding': '10px 15px',
				'border-radius': '5px',
				'z-index': '1000',
				'box-shadow': '0 2px 10px rgba(0,0,0,0.1)'
			})
			.text('実行中のバッチがあります。30秒後に自動更新されます。')
			.appendTo('body');
		
		// Hide notification after 3 seconds
		setTimeout(function() {
			notification.fadeOut(500);
		}, 3000);
		
		// Auto-refresh after 30 seconds
		setTimeout(function() {
			if (confirm('実行中のバッチがあります。ページを更新しますか？')) {
				location.reload();
			}
		}, 30000);
	}
});
