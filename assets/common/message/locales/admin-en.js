const TalkappiMessageAdmin = function () {
    return {
        common:{
            message:{
                info: 'Notification',
                success: 'Success',
                error: 'Error',
                error_text: 'Cannot be displayed due to an error',
                no_data: 'No data', 
                no_find: 'Data not found',
                no_matching:'No data matching the criteria',
                delete: 'Are you sure you want to delete?',
                delete_confirm: 'This operation cannot be undone.',
                delete_file: 'Are you sure you want to delete this file?',
                infoDisplaying: (total,start,end) => `Displaying results ${start}-${end} out of ${total}`,
                infoFiltered: (num) => `（Total of ${num} results）`,
                pages: ' Pages',
                copy_content: 'Are you sure you want to duplicate?',
                delete_content: 'Are you sure you want to delete?',
                set_default_content: 'Are you sure you want to reset to default?',
                confirm_import_csv: 'Are you sure you want to register the data in the CSV file?',
                error_no_csv: 'CSV file registration is required',
                csv_export_confirm_title: "Export to CSV file?",
                save_confirm: 'Are you sure you want to save?',
                save_confirm_detail: 'Changes will take effect immediately.<br>Please check again to make sure the changes are correct.',
                multilingual_copy_confirm: 'Are you sure you want to reflect in multiple languages?',
                multilingual_copy_confirm_detail: 'All previously entered data will be overwritten',
                service_need_apply:'To use the selection feature, an application or prior setup is required. For more details, please contact our Customer Success representative.',
                success:{
                    clipboard_copy_success: 'Copied to clipboard',
                    url_copy_success: 'URL copied',
                    content_copy_success: 'Content copied',
                    sort_success: 'Updated sort order.',
                    save_success: 'Updated successfully.',
                },
                error:{
                    clipboard_copy_error:'Copy to clipboard failed',
                    content_copy_error: 'This type of content cannot be copied',
                    unexpected_error: 'An unexpected error has occurred. Please try again.',
                    unexpected_error_stop: 'An unexpected error has occurred. Please stop your operation and contact the developer.',
                    operation_failed: 'The operation failed.',
                    required_fields_empty: 'Required fields have not been filled in.',
                    skill_update_failed: 'SKILL update failed.',
                    upload_jpg_or_png_photo: 'Please upload JPG/PNG photo files.',
                    upload_5MB_or_less_photo: 'Please upload with a photo file size of 5 MB or less.',
                    upload_your_mp4_video : 'Please upload your MP4 video file.',
                    upload_20MB_or_less_video : 'Please upload photos with a video file size of 20 MB or less.',
                    submit_error : 'Save failed.',
                    validation_maxlength :'You have exceeded the number of characters allowed.',
                    required_number: 'Please input a positive number.',
                    required_question: 'Please enter a question',
                    required_answer: 'Please enter an answer',
                    required_classification: 'Please add classification.',
                    nothing_updated: 'No changes have been made.',
                    peried_error: 'Invalid Period',
                    translate_unselected: 'Please choose a multilingual reflection method',
                    preferred_language_unselected: 'Please choose preferred language',
                    listing_range_incorrect: 'Please enter both the start date and the end date, or leave both fields empty',
                    latitude_error: 'Please enter a valid latitude',
                    longitude_error: 'Please enter a valid longitude',
                    date_range: 'Please enter the date range',
                    upper_limit: 'You have reached the limit',
                    code_check_error: 'Error: The code input can only contain letters, numbers, underscores (_), hyphens (-), and dots (.), and ({}).',
                    email_format_error: 'The email address format is incorrect',
                }
            },
            label:{
                all: 'All',
                all_long: 'All',
                list: 'List',
                page: 'Page',
                back: 'Back',
                next: 'Next',
                display: 'Display',
                send: 'Send',
                edit: 'Edit',
                cancel: 'Cancel',
                confirm: "OK",
                reset: 'Reset',
                facility:'Facility:',
                search_placeholder: 'Enter keyword',
                message_title_info: 'Info',
                message_title_confirm: 'Confirm',
                message_title_warning: 'Warning',
                message_title_error: 'Error',
                message_match: 'Match',
                set: 'Set',
                not_set: 'Not set',
                online: "Online",
                offline: "Offline",
                away: "Away",
                original: "Original",
                mail: "Mail",
                collapse_section: 'Collapse section',
                delete_section: 'Delete section',
                untitled: 'Untitled',
                multilingual_settings: 'Multilingual settings',
                price: 'Price',
                delete: "Delete",
                classification_tags: 'All Classification',
                week: 'Week',
                day: 'Day',
                title: 'Title',
                description: 'Description',
                delete_confirm_caption: 'Delete',
                content: 'Content',
                add: 'add',
                text: 'Text',
                delete: 'Delete',
                delete_btn: 'Delete',
                btn_name: 'Button name',
                normal: 'Default',
                customize: 'Customize',
                img: 'Image',
                mov: 'Video',
                add: 'Add',
                mon: 'Mon',
                tue: 'Tue',
                wed: 'Wed',
                thurs: 'Thurs',
                fri: 'Fri',
                sat: 'Sat',
                sun: 'Sun',
                holiday: 'Holiday',
                subject: 'Subject',
                page_title: 'Page title',
                csv_export: 'CSV export',
                encode: 'Encode',
                output_format: 'Output format',
                for_system_link: 'Raw csv',
                for_excel_view: 'View with Excel',
                rule: 'Rule',
                paste: 'Paste',
                scene_cd: 'Scene code',
                other: 'Other',
                unavailable: 'Unavailable',
                background_img: 'Background image',
                logo_img    : 'Logo image',
                template: 'Template',
                mode: 'Mode',
                ui_version: 'New UI (Option)',
            },
            lang:{
                ja:'Japanese',
                en:'English',
                cn:'Chinese (Simpl.)',
                tw:'Chinese (Trad.)',
                kr:'Korean',
            },
        },
        batch: {
            label: {
                page_title: 'Batch Execution Status List',
                execution_date_range: 'Execution Date Range',
                batch_type: 'Batch Type',
                execution_status: 'Execution Status',
                batch_select: 'Select Batch',
                manual_execute: 'Manual Execute',
                execution_date: 'Execution Date',
                batch_name: 'Batch',
                type: 'Type',
                status: 'Status',
                priority: 'Priority',
                details: 'Details',
                status_pending: 'Pending',
                status_running: 'Running',
                status_success: 'Success',
                status_failed: 'Failed',
                status_skipped: 'Skipped',
                status_unknown: 'Unknown',
                priority_high: 'High',
                priority_normal: 'Normal',
                priority_low: 'Low',
                type_auto: 'Auto Execution',
                type_manual: 'Manual Execution',
                type_retry: 'Retry Execution',
                execution_results: 'Execution Results',
                execution_time: 'Execution Time',
                exit_code: 'Exit Code',
                records_processed: 'Records Processed',
                processed: 'Processed',
                success: 'Success',
                failed: 'Failed',
                error_info: 'Error Information',
                retry_control: 'Retry Control',
                retry_count: 'Retry Count',
                next_retry: 'Next Retry',
                retry_reason: 'Retry Reason',
                daily_success_count: 'Daily Success Count',
                timestamp: 'Timestamp',
                scheduled_time: 'Scheduled Time',
                start_time: 'Start Time',
                end_time: 'End Time',
                created_at: 'Created At',
                updated_at: 'Updated At',
                system_info: 'System Information',
                execution_type: 'Execution Type',
                execution_server: 'Execution Server',
                process_id: 'Process ID',
                created_by: 'Created By',
                updated_by: 'Updated By',
                keyword_placeholder: 'Batch name, type, etc.',
                data_load_failed: 'Failed to load data.',
                duration_hours: 'hours',
                duration_minutes: 'minutes',
                duration_seconds: 'seconds'
            },
            message: {
                error: {
                    select_batch_required: 'Please select a batch.',
                    invalid_batch: 'Invalid batch specified.',
                    manual_execution_not_allowed: 'This batch cannot be executed manually.',
                    start_date_format_error: 'Start date format is incorrect.',
                    end_date_format_error: 'End date format is incorrect.',
                    date_range_error: 'Start date must be before end date.',
                    hotel_config_error: 'Failed to get related hotel information.',
                    system_error: 'An error occurred while adding manual execution task. Please contact system administrator.'
                },
                success: {
                    manual_execute_added: 'Manual execution task added, will start in next execution.',
                    manual_execute_added_with_id: (id) => `Manual execution task added, will start in next execution. Execution ID: ${id}`
                },
                confirm: {
                    manual_execute_title: 'Batch Manual Execution Confirmation',
                    manual_execute_detail: 'Do you want to start manual execution of the following batch：',
                    running_batch_refresh_title: 'Page Refresh Confirmation',
                    running_batch_refresh_detail: 'There are running batches. Do you want to refresh the page?'
                },
                notification: {
                    running_batch_auto_refresh: 'There are running batches. Auto-refresh in 30 seconds.'
                }
            }
        }
    }
}();
