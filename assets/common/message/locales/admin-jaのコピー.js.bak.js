const TalkappiMessageAdmin = function () {
    return {
        common:{
            message:{
                info: '通知',
                success: '成功',
                error: 'エラー',
                error_text: 'エラーにより表示できません',
                no_data: 'データがありません。', 
                no_find: 'データがありません',
                no_matching:'条件に合致するデータがありません',
                delete: '削除しますか？',
                delete_confirm: 'この操作は取り消せません。',
                delete_file: 'このファイルを削除してよろしいですか？',
                infoDisplaying: (total,start,end) => `${total} 件中 ${start} から ${end} まで表示`,
                infoFiltered: (num) => `（全 ${num} 件より抽出）`,
                pages: 'ページ',
                copy_content: 'コンテンツをコピーしますか？',
                delete_content: 'コンテンツを削除しますか？',
                set_default_content: 'コンテンツをデフォルトに戻しますか？',
                confirm_import_csv: 'CSVファイルのデータを一括登録してよろしいですか？',
                error_no_csv: 'CSVファイルの登録が必要です',
                csv_export_confirm_title: 'CSVファイルを出力しますか?',
                save_confirm: '編集した内容を保存しますか？',
                save_confirm_detail: '保存後直ちに反映されます。<br>編集内容に間違いがないかを再度確認してください。',
                multilingual_copy_confirm: '多言語に反映してよろしいですか？',
                multilingual_copy_confirm_detail: '以前入力したデータはすべて上書きされます。',
                service_need_apply:'選択機能をご利用いただくには、お申し込みまたは事前の設定が必要です。詳細につきましては、カスタマーサクセス担当者までご連絡ください。',
                success:{
                    clipboard_copy_success: 'クリップボードにコピーしました。',
                    url_copy_success: 'URLをコピーしました。',
                    content_copy_success: 'コンテンツがコピーされました。',
                    sort_success: 'ソート順更新しました。',
                    save_success: '保存しました。',
                },
                error:{
                    clipboard_copy_error: 'クリップボードにコピーが失敗しました',
                    content_copy_error: 'このタイプのコンテンツのコピー作成機能はサポートしません。',
                    unexpected_error: '予期しないエラーが発生しました。再度お試しください。',
                    unexpected_error_stop: '予期しないエラーを検出しました。操作を中断し、システム開発者に連絡してください。',
                    operation_failed: '操作に失敗しました。',
                    required_fields_empty: '必須項目が入力されていません。',
                    skill_update_failed: 'SKILL更新が失敗しました。',
                    upload_jpg_or_png_photo: 'JPG/PNGの写真ファイルをアップロードしてください。',
                    upload_5MB_or_less_photo: '写真ファイルサイズが5MB以内の写真をアップロードしてください。',
                    upload_your_mp4_video : 'MP4の動画ファイルをアップロードしてください。',
                    upload_20MB_or_less_video : '動画ファイルサイズが20MB以内の写真をアップロードしてください。',
                    submit_error : '保存に失敗しました。',
                    validation_maxlength :'入力可能な文字数を超えています。',
                    required_number: '0以上の数を入力して下さい。',
                    required_question: '質問を入力してください',
                    required_answer: '回答を入力してください',
                    required_classification: '分類を追加してください',
                    nothing_updated: '変更がありません',
                    peried_error: '期間の入力が正しくありません',
                    translate_unselected: '多言語反映を選択してください',
                    preferred_language_unselected: 'ご希望の言語を選択してください',
                    listing_range_incorrect: '表示期間は開始日と終了日の両方を入力するか、両方を空にしてください',
                    latitude_error: '緯度を正しく入力してください',
                    longitude_error: '経度を正しく入力してください',
                    date_range: '期間を正しく入力してください',
                    upper_limit: '追加上限に達しました',
                    code_check_error: 'コードに使用できる文字は英数字、符号（_-.{}）のみです。',
                    email_format_error: 'メールアドレスの形式が正しくありません',
                }
            },
            label:{
                all: '全',
                all_long: 'すべて',
                list: '件',
                page: 'ページ',
                back: '戻る',
                next: '次へ',
                display: '表示',
                send: '送信',
                cancel: 'キャンセル',
                confirm: "OK",
                reset: 'リセット',
                facility:'施設',
                search_placeholder: '検索結果より絞り込み',
                message_title_info: '情報',
                message_title_confirm: '確認',
                message_title_warning: '警告',
                message_title_error: 'エラー',
                message_match: '整合する',
                edit: '変更',
                set: '設定する',
                not_set: '設定なし',
                online: "オンライン",
                offline: "オフライン",
                away: "一時離席",
                original: "原文",
                mail: "メール",
                collapse_section: 'セクションを折り畳む',
                delete_section: 'セクションを削除する',
                untitled: '未命名',
                multilingual_settings: '多言語設定',
                price: '価格',
                delete: "削除",
                classification_tags: 'すべての分類',
                week: '週間',
                day: '一日',
                title: '名称',
                description: '概要',
                delete_confirm_caption: '削除する',
                title: 'タイトル',
                content: '内容',
                add: '追加',
                text: 'テキスト',
                delete: '削除する',
                delete_btn: '削除',
                btn_name: 'ボタン名',
                normal: '標準',
                customize: 'カスタマイズ',
                img: '画像',
                mov: '動画',
                add: '追加する',
                mon: '月',
                tue: '火',
                wed: '水',
                thurs: '木',
                fri: '金',
                sat: '土',
                sun: '日',
                holiday: '祝日',
                subject: '件名',
                page_title: 'ページタイトル',
                csv_export: 'CSV出力',
                encode: '文字コード',
                output_format: '出力形式',
                for_system_link: 'システム連携用',
                for_excel_view: 'Excel閲覧用',
                rule: 'ルール',
                paste: 'ペーストする',
                scene_cd: 'ユーザー導線',
                other: 'その他',
                unavailable: '利用不可',
                background_img: '背景画像',
                logo_img    : 'ロゴ画像',
                template: 'テンプレート',
                mode: 'モード',
            },
            lang:{
                ja:'日本語',
                en:'英語',
                cn:'簡体字',
                tw:'繁体字',
                kr:'韓国語',
            },
        },
        top:{
            label:{
                chart1_vertical_axis: '新規ユーザー数',
                chart1_horizontal_axis: '日時',
            },
            message:{
                tiktok_logo_permission_confirm: 'TikTokのロゴの利用許可を申請済みですか？',
                tiktok_logo_permission_detail: 'TikTokのロゴは、TikTokとの事前の書面による許可なしに使用することはできません。詳細はTikTokのビジネスセンターにお問い合わせください。<br><br>すでに申請済みの場合のみ、VERY上でロゴを利用できます。'
            }
        },
        item:{
            label: {
                version: {
                    current: '現在のバージョン',
                    temporary_save: '一時保存',
                }
            },
            message:{
                required_start_and_end: '例年表示(月日)を利用する場合、開始月日と終了月日両方とも入力必要です。',
                item_not_public: '該当コンテンツ未公開です。',
                public_confirm:'該当コンテンツを公開しますか?',
                delete_confirm:'該当コンテンツを削除しますか?',
                classification_not_complete : '分類を最後まで選択してください',
                link_all_title:'すべてリンクしますか?',
                unlink_all_title:'すべてリンク解除しますか?',
                common_lang_display_update: 'コンテンツの表示言語が変更されました',
                common_lang_display_update_confirm: 'この広域コンテンツをリンクしているすべての施設のコンテンツの表示言語が更新されます。<br>この変更を保存しますか？保存には時間がかかる場合があります。',
                error: {
                    content_link_error: 'コンテンツリンクが失敗しました。',
                    content_unlink_error: 'コンテンツリンク解除が失敗しました。',
                },
                confirm_title: '編集画面に遷移しますか？',
                confirm_description: '保存していない内容は失われます',
                not_reflect: (reflect_image) => `多言語に反映しない${reflect_image ? '（画像のみ多言語に反映する）' : ''}`,
            },
        },
        marketlink:{
            label: {
                select_genre: 'ジャンル選択',
                select_area: 'エリア選択',
            }
        },
        talks:{
            message:{
                enter_faq_quesiton: '追加したい質問・対応したい言い方を入力してください。',
                faq_exist_as_content: '追加しようとしている質問は既に写真付きコンテンツとして自動応答されます。',
                add_faq_question :'FAQ追加を依頼しますか?',
                faq_request_fail: '依頼送信失敗しました。',
                correction_request_fail: '修正依頼に失敗しました。',
                question_flags_update: '誘導質問フラグを更新しました。',
                question_flags_update_fail: '誘導質問フラグを更新に失敗しました。',
                create_faq_success: 'FAQを追加しました。',
                create_faq_fail: 'FAQ追加に失敗しました'
            },
            label:{
                request_add_faq: 'FAQ追加を依頼',
                improvement_automatic_response: '自動応答の改善　＜ご依頼ください＞',
                add_faq: 'FAQページ質問追加',
                register_label: '登録する',
            },
            error: {
                need_faq_title_error: '質問を入力してください',
            }
        },
        logmembers:{
            message:{
                delete_user_history: '該当ユーザの履歴を削除しますか?',
                cannot_recover_delete_user_history: '会話履歴回復できませんので、ご注意ください。',
                designate_spam_user: '該当ユーザーをスパムユーザーとして指定しますか？',
                spam_user_cannot_chat: 'スパムユーザーはチャットできません。',
                unlock_spam_user: '該当ユーザーをスパムユーザーから解除しますか？',
                unlock_spam_user_can_chat: '解除後該当ユーザはチャットできます。'
            },
            label:{
            }
        },
        notice: {
            message: {
                new_copy_action_title: '新しいお知らせを作成します。よろしいですか？',
                new_copy_action_description: '選択したお知らせをコピーして作成します。',
                delete_confirm: '該当のお知らせを削除しますか?',
                error: {
                    required_fields_not_filled: '必須項目が入力されていません。',
                },
            }
        },
        maximums: {
            message:{
                copy_stock: (stock_name) => `「${stock_name}」を複製しますか？`,
                new_stock: '新しい枠を作成します。',
                reset_title: '該当枠をリセットしますか',
                reset_description: 'この枠の予約データがすべて削除されます。<br>データ回復はできなくなりますが、よろしいですか。',
                delete_title: '該当枠詳細を削除しますか?',
                match_title: '該当枠を残枠数を整合しますか',
                match_description: '<br>データ回復はできなくなりますが、よろしいですか。',
                save_title: '枠の設定を保存しますか？',
                save_description: '保存すると枠が作成されます。枠数が変更された場合、保存すると自動的に一括反映されます。ご注意ください。',
                plan_delete_title: '内訳・プランを削除してよろしいですか',
                plan_delete_description: '「削除する」を押下後、予約フォームの表示、お客様の予約データなど<br>この内訳・プランを参照している場合、正しく表示できなくなりす。<br>また、削除した内容は復元することはできません。',
                error: {
                    group_name: 'グループ名を入力してください。',
                    correct_number: '正しい数値を入力してください',
                    hours: '利用時間帯を入力してください。',
                    hours_shape: '「08:00」のように入力してください。',
                    category_blank: '内訳を選択してください。',
                }
            },
            label: {
                one_year: '※１年間以上の期間が設定された場合、保存に時間が掛かる場合があります',
                add_group: 'グループを追加する',
                add_time: '時間帯設定を追加する',
                time_settings: '時間帯設定',
                maximum_settings: '在庫設定',
                maximum_settings_description: '在庫の減り方は枠を参照する予約フォーム側で設定可能です。<br>例）1組4名の場合、在庫数は「1」を減らすか、人数分の「4」を減らすかはフォーム側で設定できます。',
                number_of_stock: '在庫数/枠',
                per_one_stock: '1組あたり',
                remaining_stock: '残在庫数/枠',
                only_a_few_left: '以下の場合「残りわずか」と表示（空欄に設定した場合、「残りわずか」は非表示）',
                price_settings: '内訳・プラン',
                base_price: '同一料金',
                add_price_breakdown: '内訳・プラン追加',
                no_single_reservations: '単独予約不可',
                spot: '名(個)',
                spot_counter: '個',
                stock: '組',
                per_stock: '名／組',
                yen_per_stock: '円',
                yen_per_spot: '円/名',
                maximum_num: '利用枠数',
                reservation_stock: '予約枠数',
                remaining: '残',
                reserved: '予約',
                stock_total: '全体枠数',
                number_per_stock: '個数/枠',
                thresholds: '残り僅か閾値',
                selling: '販売中',
                stop_selling: '販売中止',
                finish_selling: '販売終了',
                stop: '中止',
                some_stop_selling: '一部販売中止',
                batch_setting: '一括設定',
                sold_out: '売切',
                select_operation: '操作内容を選択してください',
                view_lists: '予約一覧を見る',
                adjust_inventory: '在庫を調整する',
            }
        },
        maximum: {
            message: {
                error: {
                    comment_required: 'コメントを入力してください',
                    comment_max_length: 'コメントは100文字以内で入力してください',
                }
            }
        },
        maximumcalendar: {
            message: {
                type_warning: '※枠タイプを枠追加後に変更した場合は、選んだ枠が削除されるのをご了承ください。',
                copy_title: '指定した枠統計・カレンダーをコピーして、よろしいですか？',
                copy_description: '新しい枠統計・カレンダーを作成します。<br>再度確認してください。',
                success: {
                    update: '在庫数を更新しました'
                },
                error: {
                    data: 'データーの取得に失敗しました',
                    six: (num)=>`${num}個以上の枠は追加できません`,
                    title_required: 'タイトルを入力して下さい',
                    color_required: '色を選択して下さい',
                    stock_required: '枠を追加して下さい',
                    update_fail: '更新に失敗しました',
                    db_error: 'データ更新に失敗しました',
                    force_update: '強制更新しますか',
                    maximum_check_error: '設定値は在庫上限を超えています',
                }
            },
            label: {
                new_edit: '枠統計・カレンダーの新規作成・編集',
                calendar_name: '枠統計・カレンダー名前',
                stock_type: '枠タイプ',
                date: '日付期間',
                time: '時間帯',
                all: '全期間',
                add_stock: '枠追加',
                add_stock_message: '枠を追加する',
                stock: '枠',
                no_stock: '枠なし',
                one_day: '1日',
                three_days: '3日',
                five_days: '5日',
                monthly_calendar: 'カレンダー',
                monthly_timeline: 'タイムライン',
                cell_size: '行のサイズ',
                cell_large: '大',
                cell_small: '小',
                maximum_stats: '枠統計',
                maximum_name: '枠名',
                maximum_total: '総数',
                maximum_sold: '販売数',
                maximum_remaining: '残数',               
            }
        },
        inquirycalendar: {
            message: {
                copy_title: '指定した枠カレンダーをコピーして、よろしいですか？',
                copy_description: '新しい枠カレンダーを作成します。<br>再度確認してください。',
                max_reservations_hint: '最大四つまで追加できます。',
                success: {
                    update: '問合せ・予約カレンダーを更新しました'
                },
                error: {
                    data: 'データーの取得に失敗しました',
                    four: '4個以上の予約は追加できません',
                    title_required: 'タイトルを入力して下さい',
                    color_required: '色を選択して下さい',
                    reservation_required: '枠を追加して下さい',
                    update_fail: '更新に失敗しました',
                    uncreatable: '問合せ・予約申込フォーム、または枠が存在していないため、問合せ・予約カレンダーを作成できません',
                }
            },
            label: {
                new_edit: '問合せ・予約カレンダー新規作成・編集',
                calendar_title: 'カレンダータイトル',
                add_form: 'フォーム追加',
                add_form_message: 'フォームを追加する',
                display_name: '予約表示名称',
                set_display_name: '表示名称を設定する',
                reservation: '予約',
                remark_edit: '多言語設定・備考欄設定',
            }
        },
        talknew:{
            message:{
                delete: 'を削除しますか',
                delete_faq: '該当FAQを削除しますか？',
                advanced_settings_not_preview: '現在高度な設定はプレビューできません。',
                confirm_delete_answer_data: '回答内容と統計データは完全に削除されます。<br>この操作は取り消せません。削除してもよろしいですか？',
                confirm_delete_chatbot_faq: '全言語のFAQ回答が完全に削除されます。<br><strong>データを復元可能とするために、削除前に各言語のFAQを保存し、FAQの履歴を記録することをお勧めします。</strong><br>この操作は取り消せません。削除してもよろしいですか？',
                answer_limit: '回答数が上限になりました',
                button_maximum: 'ボタン数が最大になりました',
                select_group_facility:'グループ施設を選択する',
                save_edit_and_copy: '編集内容を保存してからグループ施設にコピーしてください',
            },
            label:{
                answer: '回答',
                action: 'アクション',
                message: 'メッセージ',
                link_text: 'リンク元テキスト',
                button: 'ボタン',
                execution_skill: '実行スキル',
                add_video: '動画追加',
                add_image: '画像追加',
                add_answer: '回答を追加する',
                add_menu: 'メニューを追加する',
                all_menu_answers: '全てのメニュー回答',
            }
        },
        talkreport:{
            label:{
                register_answers: '回答を登録する'
            }
        },
        maximumorders:{
            message:{
                cancel_title: '該当予約をキャンセルしますか?',
                email_title: '該当予約をキャンセルメール再送信しますか?',
                reservation_title: '該当予約完了メール再送信しますか?',
                setting_title: '一覧表示　項目設定',
                error:{
                    cancel_fail: 'キャンセルが失敗しました。',
                },
            },
        },
        surveys:{
            message:{

            },
            label:{
                search_by_code_or_survey_name:'コード・アンケート名から検索',
            }
        },
        very: {
            top: {
                content: "コンテンツ",
		        inquiry: "予約フォーム",
		        congestion: "混雑状況",
		        customize_content: "カスタマイズコンテンツ",
		        survey: "アンケート",
		        reception: "順番待ち",
                add: "追加する",
                change: "変更する",
                edit: "修正する",
                title_require: "「タイトル」は必須です。",
                url_require: "「URL」は必須です。",
                inquiry_require: "「フォームID」は必須です。",
                content_require: "「コンテンツコード」は必須です。",
                reception_require: "「順番待ちID」は必須です。", 
                congestion_require: "「コンテンツコード」は必須です。", 
                class_code_validation: "「コンテンツ」の分類を再度選択しなおしてください。",
                page_require: "「ページコード」は必須です。" ,
                market_require: "「タイプ」は必須です。" ,
                survey_require: " 「アンケートコード」は必須です。" ,
                title_duplicate: "「タイトル」が他の項目と重複しています。",
                icon_require: "「アイコン設定」は必須です。",
                untitled: "名称未設定",
                invalid_content_id: "順番待ちIDの入力内容が不正です。",
                save_title: (title) => `「${title} 」を追加しました。`,
                display_title: "タイトル",
                image: "画像",
                classification: "機能",
                inquiry_classification: "フォーム分類",
                select_classification: "分類から選ぶ",
                designation: "個別に指定する",
                code: "コンテンツコード",
                page_code: "ページコード",
                survey_code: "アンケートコード",
                comma_separated: "複数の場合はコンマ区切りで入力してください",
                select: "選択",
                select_contents : "コンテンツを選択",
                select_receptions : "順番待ちを選択",
                input_url: "URL",
                function: "機能",
                select_delete_button: "削除するボタンを選んで下さい。",
                select_reshuffle_button: "順番を入れ替えるボタンを選んで下さい。",
                basic_function: "基本機能",
                customize: "カスタム機能",
                notices_add_error:"お知らせは5つまでしか設定できません。",
                main_functions_add_error:"TOPボタンは5つまでしか設定できません。",
                basic_function_add_error:"機能が選択されていません",
                footer_add_error:"フッターは3つまでしか設定できません。",
                footer_setting_error:"追加したい項目を選んでください",
                facility_id: "施設ID",
                content_id: "コンテンツID",
                content_name: "コンテンツ名",
                reception_id: "順番待ちID",
                inquiry_id: "フォームコード",
                designation_type: "タイプ",
                content_selection: "コンテンツ",
                facility_contents: "施設コンテンツ",
                wide_area_content: "広域コンテンツ",
                all_display: "すべて表示する",
                priority_display: "表示方法",
                contents_list: "コンテンツ一覧",
                category_selection: "カテゴリ一覧",
                area_selection: "エリア選択",
                area_designate: "エリア指定",
                background: "背景",
                background_color_title: "色指定",
                background_color: "背景色",
                text_color: "文字色",
                page: "ページ",
                activity: "アクティビティ予約",
                ticket: "チケット販売",
                restaurant: "レストラン予約",
                market_type: "タイプ",
                top_paste_confirm: "VERYの設定をペーストしますか？",
                top_paste_description: "VERYの設定をペーストすると、「スタイル」「TOPボタン」「メインメニュー」のデータは上書きされます。元に戻すことはできません。",
                file_upload_memo: "※ロゴ画像を設定するとタイトルが中央に配置されます",
                main_image_url: "URL",
                main_image_save_error: 'メイン画像が設定されていません',
                main_image_delete_confirm: (index) => `「画像${index}」を削除しますか？`,
                top_save_error_title: 'データを保存できませんでした',
                top_save_error_description :'必須項目が入力、または選択されていない設定があります',
                apply_template_confirm: (template_name) => `「${template_name}」の設定を適用しますか？`,
                apply_template_note: "適用すると、すでに作成済みのものがある場合はその内容が失われ、テンプレートの内容に上書きされます。",
                top_copy_no_scene_error: 'コピー元のユーザー導線を選択してください',
                top_paste_no_scene_error: 'コピー先のユーザー導線を選択してください',
                scene_cd_duplicate_error: (scene_cd) => `設定した期間が「${scene_cd}」の導線と重複しています`,
                font_src: 'フォントのURL',
                font_family: 'フォントの名称',
                font_validate_error: 'フォントのURLと名称を入力してください',
                is_positive_integer: '旅ナカチェックの時間を正の整数で入力してください',
                icon_setting: 'アイコン設定',
                icon_change: 'アイコンを変更',
                support_language_not_set_alert_title : "施設のサポート言語が設定されてません",
                support_language_not_set_alert_description : "「基本設定」の「機能設定」タブから、サポート言語をひとつ以上選択してください",
                support_language_not_set_alert_button : "基本設定へ",
                vod_setting : "TVインフォメーション設定",
                vod_use : "TVインフォメーション利用",
                tv_top_image: "TVトップ画像",
                tv_top_image_note: "TVインフォメーションの「トップ」には、VERYで設定している「メイン画像」がそのまま表示されます。<br/>そのため、画像の解像度が低い場合はTVに粗く表示される可能性があります。（推奨解像度：1920px＊1080px、4Kの場合：3840px＊2160px）",
                change_main_image: "変更する",
                main_image_upload_memo: "＊TVインフォメーションを使用する場合は、解像度の高い画像をご利用ください。",
            },
            report: {
                chatbot: "チャットボット",
                reserve: "予約",
                faq: "FAQ",
                request: "リクエスト",
                congestion: "混雑状況",
                checkout: "チェックアウト",
                userguide: "ご利用案内",
                set_one_year: "１年以内の期間で絞り込んでください",
            },
            userguide: {
                apply_template_warning: "現在の内容が上書きされます。 テンプレートを適用しますか？"
            },
            laundry: {
                success_save: "洗濯機情報の設定が保存されました。",
                error: {
                    usage_guide_title_validation: "洗濯機利用案内のタイトルは20文字以内で入力してください。",
                    device_name_label_validation: (id) => `セクション${id}の洗濯機名を20文字以内で入力してください。`,
                    device_description_validation: (id) => `セクション${id}の説明を100文字以内で入力してください。`,
                },
            },
        },
        push: {
            set_time: '予約配信',
            send_immediately: '即時配信',
            facilities: '配信対象施設',
            send: '配信する',
            pick_member: '左のリストから選択してください',
            message: {
                repush: '同じ内容で再配信しますか？',
                new_create:  '新規配信を作成します。',
                delete: 'この配信を削除しますか？',
                send_immediately: `該当タスクを即時配信しますか。`,
                set_time: (date,time,name) => `該当タスクを${date} ${time}に${name}しますか。`,
            },
            
            error: {
                content: '配信コンテンツを指定してください。',
                time: '配信予定日時を指定してください。',
                channel: 'チャネルを選択してください',
                language: '言語を選択してください',
                channel_and_language: 'チャネルと言語を選択してください',
                count_zero: 'ターゲットの人数が0人です',
                line_limit_warning: '配信対象が、当月の配信可能件数を超えています。一部のLINEユーザーには配信されません。'
            },
        },
        chat: {
            message: {
                token_error: "該当ユーザーが別の画面でログインしたため、現在の画面を閉じます。",
                bot_switch_error: "別のボットに切り替えたため、画面をリフレッシュして再度操作をしてください。",
                network_error: "ネットワーク接続が不安定になっています。お手数ですが一度ログアウトし、ログインし直して下さい。",
                automatic_on: "有人対応画面に接続しました。",
                filtering_error: "一度に表示できるユーザー数の上限を超えているか、フィルタリング条件の設定を間違えている可能性があります。フィルタリング条件を再度設定してください。",
                message_send_error: "お客様への送信に失敗しました。",
                mail_sent: "メールを送信しました。",
                response_mode: "有人対応モードに切り替えました。",
                response_mode_change: "対応モードを切り替えました。",
                response_mode_error: "対応モードの切り替えに失敗しました。",
                other_operater_change: "このお客様は他のオペレーターが対応中です。代わりに対応しますか？",
                other_operator_current: "このお客様は他のオペレーターが対応中です。",
                operator_changed: "オペレーターを切り替えました。",
                operator_change_error: "オペレーターの切り替えに失敗しました。",
                user_info_change: "お客様への対応状況を更新しました。",
                send_request: "お客様へリクエストを送信してよろしいですか？",
                sales_mode_switch: "営業対応モードに切り替えました。",
                display_error: "お客様とのチャットの取得に失敗しました。",
                delete_message: "該当のチャットを削除しますか？(この操作は取り消せません)",
                kintone_support: "Kintoneに回答サポートを依頼しました。",
                unrecognizable_question: "すべてのAI認識不可質問を対応済みにしますか？",
                unrecognizable_question_auto: "すべてのAI認識不可質問を対応済みとし、自動応答モードに切り替えますか？",
                already_responded: "対応済みにしました。",
                exit_chat_mode: "オペレーター対応モードを終了してよろしいですか？（モードを切り替えたことはお客様に通知されます）",
                auto_translate_on: "自動翻訳モードをONにしました。",
                auto_translate_off: "自動翻訳モードをOFFにしました。",
                auto_response: "自動モードに切り替えました。",
                auto_response_error: "対応モードの切り替えに失敗しました。",
                end_chat_mode: "有人対応モードを終了し、自動モードに切り替えます",
                upload_10MB_or_less: 'ファイルサイズが10MB以内をアップロードしてください。',
                upload_location: '位置情報リクエスト',
                upload_image: '写真リクエスト',
                no_fixed_phrases: '定型文が定義されていません。'
            },
            label: {
                operator_responding: "有人対応中です",
                other_operator_reponding: "他のオペレーターが対応中です",
                select_template: "定型文選択",
                ai_unknown_question: "AI認識不可の質問",
                ai_unknown_question_clear: "クリア",
                ai_unknown_question_none: "なし"
            }
        },
        reception: {
            label: {
                ticket_detail: '受付可能時間の詳細設定',
                date_setting: '日付設定',
                start_date: '開始日',
                end_date: '終了日',
                weekdays: '曜日',
                holiday: '祝日',
                excluded_date: '除外日',
                include: '含む',
                exclude: '除く',
                not_set: "設定なし",
                placeholder: '日付、期間を入力してください。例:2021/10/11,2021/12/20-2021/12/25',
                time_setting: '時間帯設定',
                available_time: '利用時間帯',
                excluded_time: '除外時間帯',
                item_cd: 'コンテンツコード',
                item_name: 'コンテンツ名',
                capacity: '人数',
                capacity_min: '名から',
                capacity_max: '名まで',
                reserve_alone: '同伴者必須',
            },
            message: {
                language: '言語は１つ以上選んでください。',
                setting_time: '受付可能時間を設定してください。',
                required: (label) => `${label}は必須です。`,
                waiting_time: `待ち時間の設定は、「〜分/組」と「同時受付〜組」のどちらも設定してください。`,
                delete: '該当施設を削除しますか?',
                display: '該当施設を表示にしますか?',
                undisplay: '該当施設を非表示にしますか?',
                capacity: '人数を設定してください',
                printer: 'IPアドレス/ポート番号を設定してください',
                display_auto_cancel_validation: '「自動キャンセル」の時間を設定してください',
                display_must_check_items: '「ディスプレイ画面に表示する項目」を1つ以上チェックしてください'
            },
            error_message: {
                pause_error_message: '受付一時停止中',
                congestion_error_message: '混雑のため受付自動停止中',
                printer_connnect_error_message: 'プリンターとの接続に失敗しました',
                limit_error_message: '本日の受付は終了しました。',
                network_error_message: 'ネットワークエラー',
                unknown_error_message: 'エラーが発生しました',
                refresh_label: '更新',
                reconnect_label: '再接続'
            }
        },
        receptionlist: {
            message: {
                pause_title :"受付を停止します",
                pause_message : "受付を再開するまで、ユーザーからの受付を停止しますか？",
                resume_title:"受付を再開します",
                resume_message:"ユーザーからの受付を再開しますか？",
                notify_title:"ユーザーを呼出しますか？",
                notify_message:"このユーザーを呼出します",
                cancel_notification_title:"呼出を取り消しますか？",
                cancel_notification_message:"このユーザーの順番待ちはスキップされます。ユーザーの状態は「未呼出」になります。",
                complete_title:"受付を完了しますか？",
                complete_message:"このユーザーの受付を完了します。",
                not_called_message:"このユーザーは「呼出中」ではありません。このユーザーの受付を完了しますか？",
                cancel_reception_title:"キャンセルしますか？",
                cancel_reception_message:"このユーザーの順番待ちはキャンセルされます。",
                status_back_title:"受付待ちに戻しますか？",
                status_back_message:"このユーザーの状態は「未呼出」になります。",
            },
            form_message: {
                not_number: "数字を入力してください。",
                exceed_max_number: (num) => `${num}以下の数字を入力してください。`,
                exceed_min_number: (num) => `${num}以上の数字を入力してください。`,
                exceed_max_input_length: (num) => `${num}文字以下で入力してください。`,
                exceed_min_input_length: (num) => `${num}文字以上で入力してください。`,
                required: (label) => `${label}必須項目です。`,
            },
            label :{
                label_groups_ml: " 組 Groups",
                label_minutes_ml : " 分 Minutes",
                label_groups: "組",
                label_minutes : "分",
                people_num : "名",
                status_waiting: '未呼出',
                status_calling_soon: 'もうすぐ呼出',
                status_calling: '呼出中',
                status_reception_complete: '受付完了',
                status_auto_cancel: '自動キャンセル',
                status_cancel: 'キャンセル',
                
                button_status_call: '呼出',
                button_status_complete: '受付完了',
                button_status_cancel: 'キャンセル',
                button_status_cancel_call: '呼出取消',
                button_status_back_to_reception: '受付待ちに戻す',
                label_no_data: '表示できるデータがありません',
            },
            bulk_action_text: (selectedCount) => `選択された<span style="font-weight:bold"> ${selectedCount}項目 </span>を`,
        },
        inquiry_survey: {
            link_text: 'リンク元テキスト',
            add_link: '挿入する',
            default: 'デフォルト',
            item_num: '項目数',
            question: '設問',
            filter: '絞り込み',
            filter_tips: '詳細条件：先に言語を選択してください',
            expand: '展開する',
            fold: '折り畳む',
            type:{
                opt:'単一選択',
                chk:'複数選択',
                sel:'プルダウン',
                txt:'短文',
                txa:'長文',
                fup:'ファイルアップロード',
                frs:'フリースペース',
                mtx:'マトリクス',
                fleq:'よく聞く質問',
                prefecture_city:'都道府県 + 市区町村',
                maximum:'予約枠紐付け',
                name:'名前',
                address:'住所',
                payment:'支払方法',
                scr: '点数評価',
                ord: '旅ナカオーダー',
            },
            txt:{
                tel:'電話番号',
                postcode:'郵便番号',
                mail:'メールアドレス',
                date:'日付',
                number:'数字',
                text:'自由入力',
                coupon:'クーポンコード',
                select_coupon:'クーポンを選択',
                num: (num) => `${num}分ごと`,
                excluding: '除外',
                format: '形式',
                time: '時刻',
                time_zone: '時間帯',
                days: '日間',
                day: '日' ,
                months: 'ヶ月間',
                days_before: '日前',
                excluding: '除外日',
                future: '未来',
                past: '過去',
                all_period: '全期間',
                select_base_date: '基準日選択',
                dont_use: '選択しない',
                application_date: 'フォーム入力日',
                start_date: '開始日',
                end_date: '終了日',
                term_start: '期限開始',
                term_end: '期限終了',
                excluding_holidays: '祝日を除く',
                including_holidays: '祝日を含む',
                reservable_period: '予約可能時間帯',
            },
            placeholder: {
                option: '選択肢',
                title: 'タイトル',
                city: '〇〇市',
            },
            option: {
                text: '選択肢', // Option
                add: '選択肢を追加する',
                input_title: 'タイトルを入力してください',
                select: '選択してください',
                add_image: '画像追加',
                fup: 'ファイル選択',
                change_image: '変更',
                delete_image: '削除',
                limit_num_of_selections: '選択数（合計）の制限',
                limit_num_of_orders: '上限数（合計）の制限',
                select_max: '最大 未設定',
                select_min: '最低 未設定',
                max_selection: '最大',
                min_selection: '最低',
                tax_included: '税込',
                tax_service: '税・サ込',
                tax_none: '非課税',
            },
            mtx: {
                limit:'これ以上追加できません',
                limit_select: '選択数の制限',
                no_limit: '制限しない',
                select_min: '最低選択数を選択',
                select_max: '最大選択数を選択',
                min: '最低選択数',
                max: '最大選択数',
                num: (num) => `${num}つ`,
            },
            scr: {
                eval_5: '5段階評価',
                nps: 'NPS評価',
                title_row: 'タイトル（行）',
                title: 'タイトル',
                option: '選択肢（列）',
                title_add: 'タイトルを追加する',
                score: '点',
                eval_5_default: ['非常に不満','不満','普通','満足','非常に満足'],
                nps_default:['おすすめしない','おすすめする'],
                scale: '評価方法'
            },
            message: {
                delete: {
                    survey:'該当項目を削除してよろしいですか？',
                    label: (label) => `ラベル「${label}」を削除します。<br>該当ラベルの設定は解除されます。`,
                },
                error: {
                    time_format: '時間を正しいフォーマットで入力してください。',
                    time_24: '設定可能時間は24:00までです。',
                    reservation: '予約枠を紐づけてください',
                    branch_format: (num, title) => `${num}. [${title}]の分岐条件に誤りがあります。再設定してください`,
                    member_mail_template: '事前に基本設定のユーザーメールテンプレートを設定してから、メールアドレスの「お客様へ送信する」を有効にしてください。',
                    lang_type: (lang) => `${lang}のフォームを確認してください`,
                    duplicate_inquiry_res: '項目拡張の予約情報が重複しています',
                },
                confirm: {
                    translate: (text) => `${text}を元に自動翻訳します。現在の内容が上書きされます。翻訳を実行してよろしいですか。`,
                    label:'項目のラベルをタイトルから作成しますか？',
                    sort_branch : {
                        title: '分岐条件の不整合を検知しました',
                        content: (item) => `分岐条件は条件元とする項目よりも後の項目にのみ設定可能です。</br>順番変更を続けますか？</br>（${item}の該当条件は削除されます）`,
                    }
                },
                empty_reservable_time_start: '予約可能時間帯の開始時間を入力してください。',
                empty_reservable_time_end: '予約可能時間帯の終了時間を入力してください。',
            },
            spl: {
                prefecture: '都道府県',
                city: '市区町村',
                name_separate: '姓名別',
            },
            branch: {
                title: '分岐条件',
                select_answer: '回答を選択してください',
                equal: 'に等しい',
                not_equal: 'に等しくない',
                include: 'を含む',
                not_include: 'を含まない',
                greater: 'より大きい',
                smaller: 'より小さい',
                equal_or_greater: '以上',
                equal_or_smaller: '以下',
                option_include: '選択肢を含む',
                option_not_include: '選択肢を含まない',
                after: '以降',
                before: '以前',
                
                select_condition: '条件を選択',
                set: '分岐条件を設定する',
                select_title: '設問を選択してください',
                select_answer: '回答を選択してください',
                select_breakdown_type: '内訳種類を選択',
                select_max_of_reservations: '予約上限数を選択',
                select_type_of_branching_source: '分岐元の種類を選択',
                select_max_of_orders: '注文上限数を選択',
                set_by_breakdown_type: '内訳で分岐条件を設定',
                set_by_max_of_reservations: '予約上限数で分岐条件を設定',
                add_and: '分岐条件（AND）を追加してください',
                delete: '分岐条件を削除しますか？',
                add: '分岐条件を追加する',
                label_or: 'OR',
                // destination: '遷移先',
                destination: '分岐先',
                condition: '条件',
                title_placeholder: '分岐条件のタイトル',
                add_condition: '条件を追加する',
                count_of_orders: (title) => `${title}の注文数`,
                orders_count: '注文数',
                order_date: '日付',
            },
            section: {
                title: 'セクション',
                number: '設問数',
            },
            maximum: {
                stock: '枠',
                reservation: '予約枠紐付け',
                select: '枠を選択してください',
                dont_use: '枠を利用しない',
                date: '日付期間',
                all: '全体期間',
                discount: '割引設定有',
                extension: '拡張設定有',
                range: '期間設定有',
                display_format: '表示形式',
                after_zero_clock: (time) => `切り替え時刻：${time}`,
                up_to: (num) => `${num}つまで`,
                more_than: (num) => `${num}つ以上`,
                before_hours: (num) => `${num}時間前まで`,
                before_hours_limit: (num) => `${num}時まで`,
                before_hours_minute: (hours, minute) => `${hours}時間${minute}分前まで`,
                after_days: (num) => `${num}日後`,
                quantity_limit: (num) => `${num}つまで`,
                quantity_limit_min: (num) => `${num}つ以上`,
                consecutive: (num) => `連続枠：${num}`,
                cross_day: '日跨ぐ',
                unavailable: '利用不可',
                ui_version: '新版UI(複数選択)',
                not_set: '未設定',
                category_single_select: '内訳単一選択',
                remove: '枠を外す',
            },
            label:{
                text: 'ラベル',
                extension: '項目拡張',
                select: 'ラベル選択',
                add: 'ラベルを追加する',
                remove: 'ラベルを外す',
                edit: 'ラベル名編集',
                delete: 'ラベル削除',
                add_image: '画像追加',
            },
            res: {
                modal_title: '予約情報',
                select_title: '予約情報選択',
                icon_pre_title: '予約情報',
                remove: '予約情報を外す',
            },
        },
        service: {
            label: {
                unhandled: '未対応',
                handling: '対応中',
                completed: '完了',
                cancelled: 'キャンセル',
                reception_id: '受付ID:',
                all: 'すべての対応状態',
            },
            message:{
                memo_delete: '該当メモを削除しますか?',
                history_delete: '該当ユーザーの履歴を削除しますか?',
                complete : 'メモを削除しました',
                refresh_alert:'別のBotで切替のため、現在の画面を閉じます。',
                notification_title:'受付通知',
                notification_message: 'お客様の受付が来ました。',
                notification_message_fail:'お客様受付情報取得が失敗しました。',
                messageDisplaying: (last_button_name,button_name) => `対応ステータスを「${last_button_name}」から「${button_name}」に変更しますか。`,
            }
        },
        sitepages: {
            label: {
                placeholder_title: 'タイトルを入力してください。',
                type: 'タイプ',
                event_schedule: 'イベント・予定',
                regular_holiday: '定休日',
                time_zone: '時間帯',
                repetition: '繰り返し',
                weekly: '週ごと',
                monthly: 'ヶ月ごと',
                yearly: '年ごと',
                repetition: '繰り返す間隔',
                all_day: '終日',
                start_date: '開始日',
                end_date: '終了日',
                holiday: '祝日',
                excluded_date: '除外日',
                include: '含む',
                exclude: '除く',
                not_set: '設定なし',
                placeholder_exclude: '日付、期間を入力してください。例:2021/10/11,2021/12/20-2021/12/25',
                color: '色指定',
                every_month: '毎月',
                no: '第',
                weekdays: '曜日',
                day: '日',
                color: '色指定',
            },
            message: {
                copy_title: '選択したサイトページをコピーしますか？',
                copy_description: '新しいサイトページを作成します。',
                image_max_length: (num) => `画像を設定できる枚数は${num}枚までです`,
                error: 'エラー',
                valid_error: '入力内容に不備があります。',
                valid_error_startdate_empty: '開始日が入力されていません。',
                valid_error_enddate_empty: '終了日が入力されていません。',
                valid_error_invalid_enddate: '終了日は開始日より後の日付を設定してください。',
                valid_error_title_empty: 'タイトルが入力されていません。',
                valid_error_weekday_empty: '曜日は1つ以上選択してください。',
                valid_error_start_empty: '開始時刻が入力されていません。',
                valid_error_end_empty: '終了時刻が入力されていません。',
                valid_error_invalid_end: '終了時刻は開始時刻より後の時間を設定してください。日を跨ぐ場合、終了時刻は翌朝6時までにしてください。',
                valid_error_freq_and_interval: '繰り返す間隔が未入力です。',
                valid_error_monthly_pulldown: '繰り返す間隔に未選択の項目があります。',
            },
            button: {
                dialog_button_create: '作成する',
            },
        },
        inquirys: {
            label: {
                data_table_search_placeholder: 'ID・フォーム名から検索',
                class_cd_default_option: 'すべての分類',
            },
            message: {
                new_copy_action_title: '新しいフォームを作成します。よろしいですか？',
                new_copy_action_description: '選択したフォームをコピーして作成します。',
                new_version_action_title: 'フォームの新しいバージョンを作成します。よろしいですか？',
                invalid_range_setting: '期間設定を全て入力してください',
                copy_template_action_title: 'テンプレートフォームからを作成します。よろしいですか？',
            }
        },
        inquiry: {
            label: {
                delete_action_title: '該当問合わせフォームを削除しますか',
                delete_action_description: 'ユーザーの問合せデータすべて削除されます。<br>データ回復はできなくなりますが、よろしいですか。',
                delete_action_confirm: '削除',
                delete_confirm_caption: '削除する',
                confirm_title: '編集画面に遷移しますか？',
                usage_limit: '利用枠:',
                mail_recipients: '送信先',
            },
            message: {
                error: {
                    inquiry_name_error: '問合わせフォーム名を入力してください。',
                    user_in_charge_error: '担当者を選択してください。',
                    from_email_error: '正しい送信元メールを入力してください。',
                    reply_email_error: '正しい返送先メールを入力してください。',
                    limit_public_password_error: '限定公開の場合、パスワードを入力してください。',
                    member_mail_template: 'ユーザーへ送信するテンプレートを解除できません。',
                    aws_varify: 'メールアドレス不正または承認されていません。<br>Amazonからの承認メールのURLをクリックして承認してください。承認しないとメール送信されません。',
                    aws_domain_verify: '送信元ドメイン不正または承認されていません。<br>正しいドメインを入力してください。',
                    empty_domain: 'ドメインを入力してください。',
                    admin_email_error: "事前に基本設定の管理者メールテンプレートを設定してから、管理者送信のユーザーを選択してください。",
                    support_lang: 'サポート言語を1つ以上選択してください。',
                    please_input_receipt_tax_rate: '領収書の税率を入力してください',
                    please_input_pay_limit: '支払期限を入力してください',
                    mail_template_lang_error: 'メールテンプレートを設定できません',
                    pms: 'フォーム設問編集でPMS連携が設定されているため、PMS連携をOFFにできません。',
                    mail_template_error_message: (template_name) => {
                       return `このフォームの表示言語に、「${template_name}」の言語がすべて対応していません。言語別のデータを「${template_name}」に追加してください。 </br>`
                    },
                    cancel_policy_talkappipay_limit: 'キャンセルポリシーの設定はtalkappi PAYしか適用できません。',
                },
                popup: {
                    aws_mail_confirm:'Amazonからの承認メールのURLをクリックして承認してください。承認しないとメール送信されません。'
                }
            }
        },
        inquirydesc: {
            label: {
                main_picture_add: '写真追加',
                translate_confirm_title: 'を元に自動翻訳します。現在の内容が上書きされます。翻訳を実行してよろしいですか。',
                upload_image_oversize: '画像は2MB以内のファイルを選択してください。',
                delete_action_title: 'ボタンを削除しますか？',
                fold_expand: '展開する',
                fold_close: '閉じる',
                main_picture_recommend_aspect_ratio_label: '、複数枚の場合はすべて同じ比率での登録を推奨',
            }
        },
        inquiryresult: {
            label: {
                none_selected_text_support: "すべての対応ステータス",
                none_selected_text_status: "すべての受付状態",
                content_delete_confirm: '削除',
                delete_confirm_caption: '削除する',
                change_seach_conditions: '絞り込み条件を変更する',
                save_seach_conditions: '絞り込み条件を保存する',
                untitled_conditions: '名称未設定の条件',
                cancelled : 'キャンセル済み',
            },
            message: {
                reservation_cancel_title: '該当予約をキャンセルしますか',
                content_delete_title: '該当内容を削除しますか？',
                content_delete_desc: '一覧から削除されます。もとに戻れません',
                status_change_confirm: '状態にしますか？',
                user_spam_confirm_title: '該当ユーザーをスパムユーザーとして指定しますか？',
                user_spam_confirm_desc: 'スパムユーザーの問合せ内容はデフォルトで表示されません。',
                user_spam_remove_title: '該当ユーザーをスパムユーザーから解除しますか？',
                user_spam_remove_desc: '解除後問合せ内容はデフォルトで表示されます。',
                mail_resend_confirm_title: '該当完了メール再送信しますか?',
                user_history_delete_comfirm_title: '該当ユーザの履歴を削除しますか?',
                download_confirm_title: '一括ダウンロードしますか?',
                success_save_conditions:"絞り込み条件が保存されました",
                fail_save_conditions:"絞り込みの保存に失敗しました",
                delete_condition_confirm: (title) => `保存されている絞り込み条件「${title}」を削除しますか？`,
                support_memo_2_step_amount: '支払い依頼メールを送信しました。金額は「{amount}」円、期限は「{limit}」です。',
                support_memo_2_step_refuse: '予約不可メールを送信しました。該当予約キャンセルされました。',
                mail_2_step_refuse_title: '予約不可メール送信しますか?',
                inquiry_cancel_365_error: '365日経過したクレジットカード決済はキャンセルできません',
                mail_memo_member: 'お客様に再送信しました。',
                mail_memo_admin: '管理者再送信しました。',
                cancel_policy_cancel: '管理画面からのキャンセルは100%返金となる。',
                error: {
                    error_delete_fail: '削除失敗しました。',
                    mail_resend_fail: '送信失敗しました。',
                    csv_export_fail: '言語の選択が必要です。',
                    admin_modify:'変更できませんでした。'
                },
                success: {
                    status_update_success: '対応状態は更新しました。',
                    mail_resend_success: 'メール送信しました。',
                },
                admin_modify_title:'管理者として該当の予約内容を変更しますか？<br><br>「OK」を押すと予約変更画面に移動します。',
                auto_translate_fail:'自動翻訳に失敗しました。',
            }
        },
        survey: {
            message: {
                survey_name_error: 'アンケート名を入力してください。',
                support_lang_error: 'サポート言語を1つ以上選択してください。',
                user_in_charge_error: '担当者を選択してください。',
                delete_action_title: '該当アンケートを削除しますか？',
                delete_action_description: 'アンケート結果と統計データすべて削除されます。',
                mail_template_confirm_title: '編集画面に遷移しますか？',
                mail_template_confirm_description: '保存していない内容は失われます',
            },
        },
        surveydesc: {
            message: {
                section_delete_confirm: '説明セクションを削除しますか？',
            },
            label: {
                translate_confirm_title: 'を元に自動翻訳します。現在の内容が上書きされます。翻訳を実行してよろしいですか。',
            },
        },
        surveyresult: {
            label: {
                customize_title: 'グラフのカスタマイズ',
                customize_graph_title: 'タイトル',
                customize_colors: '色',
                customize_legend_position: '凡例位置',
                customize_legend_top: '上',
                customize_legend_bottom: '下',
                customize_legend_left: '左',
                customize_legend_right: '右',
                customize_slice_text: 'データの値を表示',
                customize_slice_text_percentage: '比率',
                customize_slice_text_not_show: '非表示',
                customize_cat_row: '各項目の実数',
                customize_cat_row_show: '表示する',
                customize_cat_ratio_row: '値軸の書式',
                customize_cat_ratio_row_ratio: '比率',
                customize_not_show: '非表示',
                customize_answers: '回答数',
            },
            message: {
                delete_action_description: '一覧から削除されます。データの復元はできません。',
                error: {
                    safari_csv_export_fail: 'safariではPDF出力機能をご利用できません。',
                    filter_value_required: '絞り込み項目の値を選択してください。',
                }
            }
        },
        surveyreport: {
            label: {
                bar_monthly_graph_title: '月別回答数',
                bar_daily_graph_title: '日別回答数',
                period_label: '期間',
                count_answer: '回答数',
            }
        },
        surveyentry: {
            message: {
                select_display_coupons: 'クーポンを選択してください',
                display_coupons: '回答内容をクーポンに表示',
                display_coupon_switch_out_warning: '設問に設定されていた「回答内容をクーポンに表示」の選択を解除しました。',
                display_coupon_deleted_modal_warning: 'クーポンを「回答内容をクーポンに表示」で選択している場合、その選択が解除されます。',
                display_coupons_cleared_modal_warning: '「回答内容をクーポンに表示」の選択が、すべて解除されます。',
                delete_coupon_confirm_message: 'クーポンを削除しますか？',
                delete_all_coupon_confirm_message: 'すべてのクーポンを削除しますか？',
                delete_item_confirm: '項目を削除しますか？',
                delete_section_confirm: 'セクションを削除しますか？',
                delete_option_image: '写真を削除しますか？',
                irreversible_action: 'この操作は取り消せません',
            },
            error: {
                using_deleted_coupon_error: (question_title) => `設問「${question_title}」に、使用できないクーポンが設定されています。 設定を解除してください。`
            }
        },
        msg: {
            label: {
                carousel: 'カルーセル',
                check_json: 'JSON形式をチェックする',
                false_json: 'JSON形式ではありません',
                true_json: 'JSON形式です',
                add_image: '画像を追加する',
                edit_type: '編集タイプ',
                destination_lang: '遷移先の言語',
                destination_url: '遷移先のURL',
                mobile: 'モバイル用',
                add_btn: 'ボタンを追加する',
                delete_carousel: 'カルーセルを削除',
                priority_customize_name: '※ボタン名はカスタマイズが優先されます',
                no_btn_name: '※ボタン名を設定しない場合は非表示になります',
                click_counts: 'クリック回数を計測する',
                channel: '掲載チャンネル',
                styles: 'スタイル指定',
                survey: 'アンケート',
                inquiry: '問合せ・予約フォーム',
                listing_period: '表示期間',
            },
            message: {
                error_summernote_code: '[＜／＞]を再度クリックしてコード編集を終了してください',
                error_json: 'JSON形式を修正するか、JSON形式のチェックを無効にしてください',
                error_image: '画像を登録してください',
                delete_current_lang_confirm: (lang) => `${lang}コンテンツの内容を削除しますか？`,
                delete_all_lang_confirm: 'すべての言語のコンテンツの内容を削除しますか？',
                restore_default_contents_confirm: 'コンテンツ(全言語)を削除してデフォルトの内容に戻しますか？',
                mail_template_allert: 'メールテンプレートが以下の設定で参照されているため、削除できません。',
                code_edit_fail_alert: 'メールテンプレートが以下の設定で参照されているため、コードを変更できません。',
            },
        },
        payment: {
            label: {
                bot_settings: '取引施設設定',
            },
            message: {
                paymens_delete_action_title: '該当取引を削除しますか。',
                payment_name_error: '取引先名を入力してください',
                payment_postal_code_error: '正しい郵便番号を入力してください',
                payment_tel_error: '正しい電話番号を入力してください',
                payment_account_number_error: '正しい口座番号を入力してください',
                payment_delete_action_title: '該当取引先を削除しますか',
                payment_delete_action_description: '取引詳細と月別請求記録がすべて削除されます。データの回復はできなくなりますが、よろしいですか。'
            },
            option: {
                year: "",
                backTo: "",
                jumpYears: "別の年を選ぶ",
                months: ["1月", "2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]
            },
        },
        report: {
            label: {
                user_count_by_time_period_label: '時間帯別ユーザー数',
                time_period_label: '時間帯',
                users_count_label: 'ユーザー数',
                clicks_count_label: 'クリック数',
                session_count_label: '会話件数',
                new_user_label: '新規数',
                japanese_label: '日本語',
                english_label: '英語',
                simplified_chinese_label: '中(簡)',
                traditional_chinese_label: '中(繁)',
                korean_label: '韓国語',
                time_period_settings: '期間指定',
                csv_format: '出力フォーマット',
                by_day: '日別',
                by_month: '月別',
            },
            message: {
                mail_send_confirm: '支払い通知書をメールで送付してよろしいですか？',
                mail_success: 'メールを送信しました。',
                mail_invalid_date: '日付が正しく入力されていません。',
                mail_invalid_receiver: '受信者の情報（会社名、精算担当者名、メールアドレス）を正しく登録してください。',
                mail_invalid_account_number: '金融機関名、支店名の取得に失敗しました。口座情報を正しく登録してください。',
                mail_error: 'メールの送信に失敗しました。必要な情報が登録されているかご確認ください。',
                mail_send_complete: '送付完了',
            }
        },
        schedule: {
            label: {
                week : "週",
                month: "月",
                time_setting: "時間帯設定",
                congestion: "混雑状況",
                business_hours : "営業時間",
                unset:"未設定",
                today: "今日",
                all_day: "終日",
            }
        },
        newsletter: {
            filter_tips: '絞り込み：条件を選択してください',
            message: {
                email_title_error: '件名を入力してください。',
                project_name_error: '案件名を入力してください。',
                user_in_charge_error: '担当者を選択してください。',
                task_exec_status_error: (operation) => { 
                    if (operation == 'edit') {
                        return 'メルマガを送信中または送信済みのため、編集できません。';
                    } else if (operation == 'delete') {
                        return 'メルマガを送信中または送信済みのため、削除できません。';
                    }
                },
                error: {
                    duplicated_extend_tpl_alias: '拡張項目の変数名が重複しています',
                    extend_tpl_alias_edit_fail: '拡張項目変数名の編集に失敗しました。',
                    reply_email_error: '正しい返信先アドレスを入力してください。',
                },
                members_delete_action_title: '選択された連絡先を削除しますか',
                members_delete_action_description: '連絡先および関連データがすべて削除されます。',
                members_project_link_action_description: '連絡先の関連データ(未送信タスクの連絡先選択、タグ付け、拡張項目)が削除されます。',
                tags_project_link_action_description: 'タグ付けが削除されます。',
                delete_action_title: '該当タスクを削除しますか',
                delete_action_description: 'タスクを取り消し、関連データがすべて削除されます。',
                mail_template_confirm_title: '編集画面に遷移しますか？',
                mail_template_confirm_description: '保存していない内容は失われます',
                csv_preview_warning: (undef) => `下記の列は定義されていないためデータが読み込まれません。`,
                csv_import_completed: 'CSVから連絡先登録完了',
                mail_project_link_completed: '連絡先を案件に追加完了',
                tag_project_link_completed: 'タグを案件に追加完了',
                resolve_suppression_completed: email => `${email} がサプレッションリストから外されました`,
                not_found_on_suppression_list: email => `${email} はサプレッションリストに含まれていません`,
                csv_import_status_detail: (total,imported,skipped) => { 
                    if (skipped) {
                        return `${total} 名中 ${imported} 名の情報を取り込みました(${skipped} 名重複しています)。登録を完了するには「完了」ボタンを押してください。`;
                    } else {
                        return `${total} 名中 ${imported} 名の情報を取り込みました。登録を完了するには「完了」ボタンを押してください。`;
                    }
                 },
                mail_project_link_status_detail: (total,imported,skipped) => { 
                     if (skipped) {
                         return `${total} 名中 ${imported} 名の情報を案件に追加しました(${skipped} 名重複しています)。`;
                     } else {
                         return `${total} 名中 ${imported} 名の情報を案件に追加しました。`;
                     }
                },
                tag_project_link_status_detail: (total,imported,skipped) => { 
                     if (skipped) {
                         return `${total} 個中 ${imported} 個のタグを案件に追加しました(${skipped} 個重複しています)。`;
                     } else {
                         return `${total} 個中 ${imported} 個のタグを案件に追加しました。`;
                     }
                },
                csv_import_status_detail_from_inquiry: (total,imported) => { 
                    return `合計 ${total} 名中 ${imported} 名の情報を取り込みました。（重複するメールアドレスは取り込まれません）`;
                },
                delete_tag_title: '該当タグを削除しますか',
                delete_tag_description: '該当タグを削除しますか。',
                not_unique: '拡張項目が重複しています。',
                not_selected: '未選択の項目があります。',
                copy_signature: '署名をコピーしますか？',
                delete_signature: '署名を削除してよろしいですか？',
                delete_project: '案件を削除してよろしいですか？',
                delete_project: '案件を削除してよろしいですか？',
            },
            title: {
                extend_alias_edit_panel: (operation, extend_name) => { 
                    if (operation == 'edit') {
                        return `${extend_name}のラベル名を編集する`;
                    } else if (operation == 'add') {
                        return `${extend_name}のラベル名を定義する`;
                    }
                 },
                 import_filter_modal: '絞り込み条件を選択',
                 select_project: '案件選択',
                 add_to_project: '案件に追加',
            },
            label: {
                name: '名前',
                mail: 'メールアドレス',
                tag: 'タグ',
                delete: '削除',
            },
            button: {
                insert_extend: 'パラメーター',
            },
            tooltip: {
                insert_extend: 'パラメーター追加',
            },
        },
        account: {
            message: {
                user_login_error_title: 'ユーザー登録',
                user_delete_confirm_title: '該当ユーザーを削除しますか。',
                user_unfreeze_confirm_title: '該当ユーザーを凍結解除しますか。',
                error: {
                    required_fields_not_filled: '必須項目が入力していません。',
                    email_addr_full_width_space: '全角スペースが含まれています。',
                    email_addr_cannot_register: 'ご指定のメールアドレスはユーザー登録できません。',
                    password_rule_1: 'パスワードは11桁以上、数字、英文、符号必ず1つ以上が必要です。',
                    password_rule_3: 'パスワードは一致しません。',
                },
            },
        },
        congestion: {
            label: {
                '1': '空いている',
                '2': 'やや混雑',
                '3': '非常に混雑',
                '4': '入場制限中',
            },
        },
        alert: {
            message: {
                error: {
                    required_day: 'アラートメール：アラート日時を入力してください',
                    required_mail_users: 'アラートメール：送信先を選択してください。',
                },
            },
            label: {
                previous: '前回のアラート設定',
                complete: '送信完了',
                day_time: 'アラート日時',
                mail_users: '送信先',
                remark: '備考',
            }
        },
        translate: {
            label: {
            'translate_estimate':'翻訳の目安',
            'high_estimate':'2〜3営業日',
            'normal_estimate':'10営業日以内',
            'low_estimate':'20営業日以内',
            }
        },
        member: {
            label: {
                'age_group' : '年代',
                'area' : '地域',
                'member_status' : '会員状態',
                'purpose' : '利用目的',
                'reservation' : '予約',
                'channel' : 'チャンネル',
                'about_car' : '車について',
                'all' : 'すべて',
                'all_conditions' : 'すべての条件',
                'no_filter_condition' : '絞り込み条件はありません'
            },
            message : {

            }
        },
        botscene: {
            message: {
                empty_name_error: (index) => `名称は必須項目です。セクション${index}の名称を入力してください。`,
                empty_parameter_error: (index) => `掲載パラメータは必須項目です。セクション${index}の掲載パラメータを入力してください。`,
                duplicate_name_error: (s1, s2) => `セクション${s1}とセクション${s2}の言語と接尾語の設定が重複しています。重複しないように入力してください。`,
            }
        },
        issues: {
            message: {
                'redmine_setting_error' : 'Redmine連携が正しく設定されていません',
                'redmine_user_error' : '依頼の権限が付与されていません。貴社の担当者にご相談をお願いします',
                'redirect_to_top' : 'トップページに遷移します。',
            }
        },
        disaster: {
            label: {
                'disaster_greeting_type': '吹き出し',
                'disaster_welcome_message_type': 'ウェルカムメッセージ',
                'disaster_preview': 'プレビュー',
            },
            message: {
                'not_available' : 'ご利用できません',
                'chatbot_only' : 'チャットボット利用施設専用ページです。<br>ログアウトします。',
                'toggle_display': '表示を切り替えますか？',
                'apply_immediately': '即時反映されます。',
            }
        },
        batch: {
            label: {
                page_title: 'バッチ実行状況一覧',
                execution_date_range: '実行日範囲',
                batch_type: 'バッチタイプ',
                execution_status: '実行ステータス',
                batch_select: 'バッチ選択',
                manual_execute: '手動実行',
                execution_date: '実行日',
                batch_name: 'バッチ',
                type: 'タイプ',
                status: 'ステータス',
                priority: '優先度',
                details: '詳細情報',
                status_pending: '待機中',
                status_running: '実行中',
                status_success: '成功',
                status_failed: '失敗',
                status_skipped: 'スキップ',
                status_unknown: '不明',
                priority_high: '高',
                priority_normal: '通常',
                priority_low: '低',
                type_auto: '自動実行',
                type_manual: '手動実行',
                type_retry: '再試行実行',
                execution_results: '実行結果',
                execution_time: '実行時間',
                exit_code: '終了コード',
                records_processed: '処理件数',
                processed: '処理',
                success: '成功',
                failed: '失敗',
                error_info: 'エラー情報',
                retry_control: 'リトライ制御',
                retry_count: 'リトライ回数',
                next_retry: '次回リトライ',
                retry_reason: 'リトライ理由',
                daily_success_count: '当日成功回数',
                timestamp: 'タイムスタンプ',
                scheduled_time: '予定時刻',
                start_time: '開始時刻',
                end_time: '終了時刻',
                created_at: '作成日時',
                updated_at: '更新日時',
                system_info: 'システム情報',
                execution_type: '実行タイプ',
                execution_server: '実行サーバー',
                process_id: 'プロセスID',
                created_by: '作成者',
                updated_by: '更新者',
                keyword_placeholder: 'バッチ名、タイプなど',
                data_load_failed: 'データの読み込みに失敗しました。',
                duration_hours: '時間',
                duration_minutes: '分',
                duration_seconds: '秒'
            },
            message: {
                error: {
                    select_batch_required: 'バッチを選択してください。',
                    invalid_batch: '無効なバッチが指定されました。',
                    manual_execution_not_allowed: 'このバッチは手動実行できません。',
                    start_date_format_error: '開始日の形式が正しくありません。',
                    end_date_format_error: '終了日の形式が正しくありません。',
                    date_range_error: '開始日は終了日より前に設定してください。',
                    hotel_config_error: '関連ホテル情報取得に失敗しました。',
                    system_error: '手動実行タスク追加にエラーが発生しました。システム管理者にお問い合わせください。'
                },
                success: {
                    manual_execute_added: '手動実行タスクを追加しました、次の実行に開始されます。',
                    manual_execute_added_with_id: (id) => `手動実行タスクを追加しました、次の実行に開始されます。実行ID: ${id}`
                },
                confirm: {
                    manual_execute_title: 'バッチ手動実行確認',
                    manual_execute_detail: 'バッチの手動実行を開始しますか：',
                    running_batch_refresh_title: 'ページ更新確認',
                    running_batch_refresh_detail: '実行中のバッチがあります。ページを更新しますか？'
                },
                notification: {
                    running_batch_auto_refresh: '実行中のバッチがあります。30秒後に自動更新されます。'
                }
            }
        }
    }
}();
