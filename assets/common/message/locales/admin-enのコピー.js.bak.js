const TalkappiMessageAdmin = function () {
    return {
        common:{
            message:{
                info: 'Notification',
                success: 'Success',
                error: 'Error',
                error_text: 'Cannot be displayed due to an error',
                no_data: 'No data', 
                no_find: 'Data not found',
                no_matching:'No data matching the criteria',
                delete: 'Are you sure you want to delete?',
                delete_confirm: 'This operation cannot be undone.',
                delete_file: 'Are you sure you want to delete this file?',
                infoDisplaying: (total,start,end) => `Displaying results ${start}-${end} out of ${total}`,
                infoFiltered: (num) => `（Total of ${num} results）`,
                pages: ' Pages',
                copy_content: 'Are you sure you want to duplicate?',
                delete_content: 'Are you sure you want to delete?',
                set_default_content: 'Are you sure you want to reset to default?',
                confirm_import_csv: 'Are you sure you want to register the data in the CSV file?',
                error_no_csv: 'CSV file registration is required',
                csv_export_confirm_title: "Export to CSV file?",
                save_confirm: 'Are you sure you want to save?',
                save_confirm_detail: 'Changes will take effect immediately.<br>Please check again to make sure the changes are correct.',
                multilingual_copy_confirm: 'Are you sure you want to reflect in multiple languages?',
                multilingual_copy_confirm_detail: 'All previously entered data will be overwritten',
                service_need_apply:'To use the selection feature, an application or prior setup is required. For more details, please contact our Customer Success representative.',
                success:{
                    clipboard_copy_success: 'Copied to clipboard',
                    url_copy_success: 'URL copied',
                    content_copy_success: 'Content copied',
                    sort_success: 'Updated sort order.',
                    save_success: 'Updated successfully.',
                },
                error:{
                    clipboard_copy_error:'Copy to clipboard failed',
                    content_copy_error: 'This type of content cannot be copied',
                    unexpected_error: 'An unexpected error has occurred. Please try again.',
                    unexpected_error_stop: 'An unexpected error has occurred. Please stop your operation and contact the developer.',
                    operation_failed: 'The operation failed.',
                    required_fields_empty: 'Required fields have not been filled in.',
                    skill_update_failed: 'SKILL update failed.',
                    upload_jpg_or_png_photo: 'Please upload JPG/PNG photo files.',
                    upload_5MB_or_less_photo: 'Please upload with a photo file size of 5 MB or less.',
                    upload_your_mp4_video : 'Please upload your MP4 video file.',
                    upload_20MB_or_less_video : 'Please upload photos with a video file size of 20 MB or less.',
                    submit_error : 'Save failed.',
                    validation_maxlength :'You have exceeded the number of characters allowed.',
                    required_number: 'Please input a positive number.',
                    required_question: 'Please enter a question',
                    required_answer: 'Please enter an answer',
                    required_classification: 'Please add classification.',
                    nothing_updated: 'No changes have been made.',
                    peried_error: 'Invalid Period',
                    translate_unselected: 'Please choose a multilingual reflection method',
                    preferred_language_unselected: 'Please choose preferred language',
                    listing_range_incorrect: 'Please enter both the start date and the end date, or leave both fields empty',
                    latitude_error: 'Please enter a valid latitude',
                    longitude_error: 'Please enter a valid longitude',
                    date_range: 'Please enter the date range',
                    upper_limit: 'You have reached the limit',
                    code_check_error: 'Error: The code input can only contain letters, numbers, underscores (_), hyphens (-), and dots (.), and ({}).',
                    email_format_error: 'The email address format is incorrect',
                }
            },
            label:{
                all: 'All',
                all_long: 'All',
                list: 'List',
                page: 'Page',
                back: 'Back',
                next: 'Next',
                display: 'Display',
                send: 'Send',
                edit: 'Edit',
                cancel: 'Cancel',
                confirm: "OK",
                reset: 'Reset',
                facility:'Facility:',
                search_placeholder: 'Enter keyword',
                message_title_info: 'Info',
                message_title_confirm: 'Confirm',
                message_title_warning: 'Warning',
                message_title_error: 'Error',
                message_match: 'Match',
                set: 'Set',
                not_set: 'Not set',
                online: "Online",
                offline: "Offline",
                away: "Away",
                original: "Original",
                mail: "Mail",
                collapse_section: 'Collapse section',
                delete_section: 'Delete section',
                untitled: 'Untitled',
                multilingual_settings: 'Multilingual settings',
                price: 'Price',
                delete: "Delete",
                classification_tags: 'All Classification',
                week: 'Week',
                day: 'Day',
                title: 'Title',
                description: 'Description',
                delete_confirm_caption: 'Delete',
                content: 'Content',
                add: 'add',
                text: 'Text',
                delete: 'Delete',
                delete_btn: 'Delete',
                btn_name: 'Button name',
                normal: 'Default',
                customize: 'Customize',
                img: 'Image',
                mov: 'Video',
                add: 'Add',
                mon: 'Mon',
                tue: 'Tue',
                wed: 'Wed',
                thurs: 'Thurs',
                fri: 'Fri',
                sat: 'Sat',
                sun: 'Sun',
                holiday: 'Holiday',
                subject: 'Subject',
                page_title: 'Page title',
                csv_export: 'CSV export',
                encode: 'Encode',
                output_format: 'Output format',
                for_system_link: 'Raw csv',
                for_excel_view: 'View with Excel',
                rule: 'Rule',
                paste: 'Paste',
                scene_cd: 'Scene code',
                other: 'Other',
                unavailable: 'Unavailable',
                background_img: 'Background image',
                logo_img    : 'Logo image',
                template: 'Template',
                mode: 'Mode',
                ui_version: 'New UI (Option)',
            },
            lang:{
                ja:'Japanese',
                en:'English',
                cn:'Chinese (Simpl.)',
                tw:'Chinese (Trad.)',
                kr:'Korean',
            },
        },
        top:{
            message:{
                tiktok_logo_permission_confirm: 'Have you applied for permission to use the TikTok logo?',
                tiktok_logo_permission_detail: 'The TikTok logo cannot be used without prior written permission from TikTok. For details, please contact TikTok Business Center.<br><br>You can only use the logo on VERY if you have already applied.'
            },
            label:{
                chart1_vertical_axis: 'New user',
                chart2_horizontal_axis: 'Date',
            },
        },
        item:{
            label: {
                version: {
                    current: 'Current version',
                    temporary_save: 'Temporary save',
                }
            },
            message:{
                required_start_and_end: 'When using the annual display (month and day), both the start month and day and the end month and day must be entered.',
                item_not_public: 'The content has not yet been published.',
                public_confirm:'Do you want to publish the content?',
                delete_confirm:'Do you want to delete the content?',
                classification_not_complete : 'Please complete the classification selection.',
                link_all_title:'Do you want to link all of contents?',
                unlink_all_title:'Do you want to delete the link of all contents?',
                common_lang_display_update: 'The language display has been updated',
                common_lang_display_update_confirm: 'The language display of the content of all facilities linked to this content will also be updated.<br>Are you sure you want to save?',
                error: {
                    content_link_error: 'Content link failed',
                    content_unlink_error: 'Content unlink failed',
                },
                confirm_title: "Do you want to go to the edit screen?",
                confirm_description: "Unsaved content will be lost",
                not_reflect: (reflect_image) => `Do not apply to other languages${reflect_image ? '（Apply only images to them' : ''}`,
            },
        },
        marketlink:{
            label: {
                select_genre: 'Select Genre',
                select_area: 'Select Area',
            }
        },
        talks:{
            message:{
                enter_faq_quesiton: 'Please enter the question you would like to add and the wording you would like to address.',
                faq_exist_as_content: 'The question you are trying to add will already be auto-responded to as content with pictures.',
                add_faq_question :'Would you like to request additional FAQs?',
                faq_request_fail: 'Request submission failed.',
                correction_request_fail: 'Correction request failed.',
                question_flags_update: 'Guiding question flag updated.',
                question_flags_update_fail: 'Failed to update guiding question flag.',
                create_faq_success: 'Create FAQ successfully.',
                create_faq_fail: 'Create FAQ failed.'
            },
            label:{
                request_add_faq: 'Request to add FAQ',
                improvement_automatic_response: 'Improvement of automatic response <Please request>',
                add_faq: 'FAQ page question add',
                register_label: 'Add'
            },
            error: {
                need_faq_title_error: 'Please enter the question',       
            }
        },
        logmembers:{
            message:{
                delete_user_history: `Do you want to delete the user's history?`,
                cannot_recover_delete_user_history: 'Please note that conversation history cannot be recovered.',
                designate_spam_user: 'Do you want to designate the user as a spam user?',
                spam_user_cannot_chat: 'Spam users cannot chat.',
                unlock_spam_user: 'Do you want to unlock the user from being a spam user?',
                unlock_spam_user_can_chat: 'After the unlock, the user can chat.'
            },
            label:{
            }
        },
        notice: {
            message: {
                new_copy_action_title: 'Would you like to create a new notification?',
                new_copy_action_description: 'A new notification will be created by copying the selected notification.',
                delete_confirm: 'Do you want to delete the notification?',
                error: {
                    required_fields_not_filled: 'Required fields are not filled in.',
                },
            }
        },
        maximums: {
            message:{
                copy_stock: (stock_name) => `Are you sure you want to copy the specified stock "${stock_name}" ？`,
                new_stock:'This will create a new stock.<br>Please confirm.',
                reset_title: 'Do you want to reset the current stock details?',
                reset_description: 'This will delete all reservations for the current stock.<br>Data recovery is not possible. Are you sure you want to delete?',
                delete_title: 'Do you want to delete the current stock details?',
                match_title: 'Do you want to match the number of remaining slots with the stock amount?',
                match_description: '<br>The data cannot be recovered. Are you sure you want to continue?',
                save_title: 'Do you want to save the maximum settings?',
                save_description: 'The maximum setting will be created when you save. If the number of maximum is changed, it will be automatically reflected all at once when you save. Please be careful.',
                plan_delete_title: '内訳・プランを削除してよろしいですか',
                plan_delete_description: '「削除する」を押下後、予約フォームの表示、お客様の予約データなど<br>この内訳・プランを参照している場合、正しく表示できなくなりす。<br>また、削除した内容は復元することはできません。',
                error: {
                    group_name: 'Please enter a group name',
                    correct_number: 'Please enter the correct value',
                    hours: 'Please enter the hours of use',
                    hours_shape: 'Please enter the time in this form "08:00"',
                    category_blank: 'Please select cateory',
                }
            },
            label: {
                one_year: '※If a period of more than one year is set, it may take time to save the data.',
                add_group: 'Add group settings',
                add_time: 'Add time settings',
                time_settings: 'Time settings',
                maximum_settings: 'Maximum settings',
                maximum_settings_description: 'Choose to manage stock by group or person in the form. <br>Example: 1 group (4 people) = stock "1", group size "4".',
                number_of_stock: 'Number of stock',
                per_one_stock: 'Spots per stock',
                remaining_stock: 'Remaining stock',
                only_a_few_left: 'Amount left before displaying "Only a few left" (If set to blank, "Only a few left" will not be displayed)',
                price_settings: 'Price settings',
                base_price: 'Base price',
                add_price_breakdown: 'Add price breakdown',
                no_single_reservations: 'Single reservations not available',
                spot: ' spots',
                spot_counter: 'spots',
                stock: ' stocks',
                per_stock: ' spots per stock',
                yen_per_stock: ' yen per stock',
                yen_per_spot: ' yen per spot',
                maximum_num: 'use slots',
                reservation_stock: 'Stock',
                remaining: 'Left',
                reserved: 'Res',
                stock_total: 'Total number of stocks',
                number_per_stock: 'Number per stock',
                thresholds: 'Threshold remaining',
                selling: 'Selling',
                stop_selling: 'Stop selling',
                finish_selling: 'End of Sale',
                stop: 'Stop',
                some_stop_selling: 'Partial stop',
                batch_setting: 'Select all',
                sold_out: 'Sold out',
                select_operation: 'Please choose from below',
                view_lists: 'See reservation list',
                adjust_inventory: 'Manage inventory',
            }
        },
        maximum: {
            message: {
                error: {
                    comment_required: 'Comment is required',
                    comment_max_length: 'Comment must be less than 100 characters',
                }
            }
        },
        maximumcalendar: {
            message: {
                type_warning: '※ If the stock type is changed, the selected calendars will be deleted.',
                copy_title: 'Are you sure you want to copy the selected calendar?',
                copy_description: 'This will create a new calendar.<br>Please confirm.',
                success: {
                    update: 'Updated remaining stock'
                },
                error: {
                    data: 'Failed to retrieve data',
                    six: (num)=> `No more than ${num} stocks can be added`,
                    title_required: 'Calendar name is required',
                    color_required: 'Calendar color is required',
                    stock_required: 'Please add at least one stock',
                    update_fail: 'Update failed',
                    uncreatable: 'The Inquiry/Reservation Calendar cannot be created as there are no usable Inquiries, Reservation Forms, or Frames.',
                    db_error: 'Data update failed',
                    maximum_check_error: 'The set value exceeds the stock limit.',
                }
            },
            label: {
                new_edit: 'Add/Edit calendars',
                calendar_name: 'Calendar name',
                stock_type: 'Stock type',
                date: 'Date',
                time: 'Time',
                all: 'All',
                add_stock: 'Add stock',
                add_stock_message: 'Add stock',
                reservation: 'Reservation',
                stock: 'Stock',
                no_stock: 'No stock',
                one_day: '1 Day',
                three_days: '3 days',
                five_days: '5 days',
                monthly_calendar: 'Calendar',
                monthly_timeline: 'Timeline',
                cell_size: '行のサイズ',
                cell_large: '大',
                cell_small: '小',
                maximum_stats: 'Frame Statistics',
                maximum_name: 'Frame Name',
                maximum_total: 'Total',
                maximum_sold: 'Sold',
                maximum_remaining: 'Remaining'
            }
        },
        inquirycalendar: {
            message: {
                copy_title: 'Are you sure you want to copy the selected calendar?',
                copy_description: 'This will create a new calendar.<br>Please confirm.',
                max_reservations_hint: 'At most 4 reservations can be added',
                success: {
                    update: 'Updated inquiry calendar'
                },
                error: {
                    data: 'Failed to retrieve data',
                    four: 'No more than 4 reservations can be added',
                    title_required: 'Calendar name is required',
                    color_required: 'Calendar color is required',
                    reservation_required: 'Please add at least one reservation',
                    update_fail: 'Update failed',
                }
            },
            label: {
                new_edit: 'Add/Edit calendars',
                calendar_title: 'Calendar title',
                add_form: 'Add form',
                add_form_message: 'Add form',
                display_name: 'Display name',
                set_display_name: 'Set display name',
                form: '予約',
                remark_edit: 'Remark',
            }
        },
        talknew:{
            message:{
                delete: ' delete?',//通常は大文字だが、前に文字があるので、小文字スタート
                delete_faq: 'Do you want to delete the relevant FAQ?',
                advanced_settings_not_preview: 'Advanced settings cannot be previewed at this time.',
                confirm_delete_answer_data: 'The answer and statistical data will be completely deleted.<br>This operation cannot be undone. Are you sure you want to delete it?',
                confirm_delete_chatbot_faq: 'All language FAQ answers will be completely deleted.<br><strong>To enable data recovery, we recommend saving FAQs in each language and recording FAQ history before deletion.</strong><br>This operation cannot be undone. Are you sure you want to delete?',
                answer_limit: 'The number of answers has been limited.',
                button_maximum: 'Maximum number of buttons',
                select_group_facility: 'Select a group facility',
                save_edit_and_copy: 'Save your edits and copy them to the group facility',
            },
            label:{
                answer: 'Answer',
                action: 'Action',
                message: 'Message',
                link_text: 'Link text',
                button: 'Button',
                execution_skill: 'Execution skill',
                add_video: 'Add a video',
                add_image: 'Add an image',
                add_answer: 'Add an answer',
                add_menu: 'Add a menu',
                all_menu_answers: 'All menu answers',
            }
        },
        talkreport:{
            label:{
                register_answers: 'Register Answers'
            }
        },
        maximumorders:{
            message:{
                cancel_title: 'Are you sure you want to cancel the reservation?',
                email_title: 'Are you sure you want to resend the cancelation email?',
                reservation_title: 'Are you sure you want to resend the confirmation email?',
                setting_title: 'Select information for export (csv)',
                error:{
                    cancel_fail: 'Cancel failed',
                },
            },
            label:{
            }
        },
        surveys:{
            message:{

            },
            label:{
                search_by_code_or_survey_name:'Search by code or survey name',
            }
        },
        very: {
            top: {
                content: "Content",
		        inquiry: "Inquiry",
		        congestion: "Congestion",
		        customize_content: "Customize content",
		        survey: "Survey",
		        reception: "Reception",
                add: "Save",
                change: "Save",
                title_require: "Title is required.",
                url_require: "URL is required." ,
                inquiry_require: "Form ID is required." ,
                content_require: "Contents code is required." ,
                reception_require: "Reception ID is required." ,
                congestion_require: "Congestion ID is required." ,
                class_code_validation: "Re-select the 'Contents' classification.",
                page_require: "Page code is required." ,
                market_require: "Type is required." ,
                survey_require: "Survey code is required." ,
                title_duplicate: "Title is a duplicate.",
                icon_require: "Icon is required." ,
                untitled: "Untitled",
                invalid_content_id: "The reception ID entry is invalid.",
                save_title: (title) => `"${title}" is added.`,
                display_title: "Title",
                image: "Image",
                classification: "Function",
                inquiry_classification: "Form classification",
                select_classification: "Select by category",
                designation: "Specify individually",
                code: "Content code",
                page_code: "Page code",
                survey_code: "Survey code",
                comma_separated: "Comma-separated for multiple entries",
                select: "Select",
                select_contents : "Select Contents",
                select_receptions : "Select Receptions",
                input_url: "URL",
                function: "Function",
                select_delete_button: "Select the button you wish to delete.",
                select_reshuffle_button: "Select the button you wish to move position.",
                basic_function: "Basic function",
                customize: "Custom function",
                notices_add_error:"No more than 5 notices can be added.",
                main_functions_add_error:"No more than 5 top buttons can be added.",
                basic_function_add_error:"There are not enough setting items for basic.",
                footer_add_error:"No more than 3 footer buttons can be added.",
                footer_setting_error:"Please select the item you want to add.",
                facility_id: "Facility ID",
                content_id: "Content ID",
                content_name: "Content Name",
                reception_id: "Reception ID",
                inquiry_id: "Form code",
                designation_type: "Type",
                content_selection: "Content",
                facility_contents: "Facility contents",
                wide_area_content: "Wide-area content",
                all_display: "Display all",
                priority_display: "Display format",
                contents_list: "Contents list",
                category_selection: "Category selection",
                area_selection: "Area selection",
                area_designate: "Area designate",
                background: "Background",
                background_color_title: "Color",
                background_color: "Background",
                text_color: "Text",
                page: "Page",
                activity: "Activity Reservations",
                ticket: "Ticket sales",
                restaurant: "Restaurant Reservations",
                market_type: "Type",
                top_paste_confirm: 'Do you want to paste the settings for VERY?',
                top_paste_description: 'When pasted, the data for "Style," "Top buttons," and "Main menu" will be overwritten. This operation cannot be undone.',
                file_upload_memo: "※The title will be centered when a logo image is set.",
                main_image_url: "URL",
                main_image_save_error:`Please set the image.`,
                main_image_delete_confirm: (index) => `Are you sure to remove the 'Image${index}'?`,
                top_save_error_title: 'Do not save data',
                top_save_error_description: 'There are settings where required items have not been entered or selected',
                apply_template_confirm: (template_name) => `Are you sure to copy the settings from '${template_name}'?`,
                apply_template_note: "If applied, any existing content will be lost and overwritten with the template content.",
                top_copy_no_scene_error: 'Please select the user flow from the source to copy.',
                top_paste_no_scene_error: 'Please select the user flow to copy to.',
                scene_cd_duplicate_error: (scene_cd) => `The time period you set is already set in [${scene_cd}].`,
                font_src: 'Font src',
                font_family: 'Font family',
                font_validate_error: 'Please set font src and font family',
                is_positive_integer: 'Please enter the traveling check time as a positive integer',
                icon_setting: 'Icon setting',
                icon_change: 'Change icon',
                support_language_not_set_alert_title: "Supported language is no set",
                support_language_not_set_alert_description: "Please select one or more supported language from the 'Function settings' tab in 'Basic settings'.",
                support_language_not_set_alert_button: "Go to Basic settings",
                vod_setting : "TV information setting",
                vod_use : "TV information use",
                tv_top_image: "TV Top Image",
                tv_top_image_note: "The 'Main image' configured in VERY is displayed as-is on the TV Information 'Top' screen.<br/>If the image resolution is low, it may appear pixelated on the TV.  (Recommended resolution: 1920px＊1080px; for 4K: 3840px＊2160px)",
                change_main_image: "Change Image",
                main_image_upload_memo: "＊Please use high-resolution images when using the TV Information feature.",
            },
            report: {
                chatbot: "Chatbot",
                reserve: "Reservation",
                faq: "FAQ",
                request: "Request",
                congestion: "Crowdedness",
                checkout: "Check out",
                userguide: "User guide",
                set_one_year: "Please narrow down the period to within one year.",
            },
            userguide: {
                apply_template_warning: "The current data will be overwritten. Are you sure you want to continue?"
            },
            laundry: {
                success_save: "The laundry settings have been saved.",
                error: {
                    usage_guide_title_validation: "Please enter the laundry usage guide title within 20 characters.",
                    device_name_label_validation: (id) => `Please enter the device name for section ${id} within 20 characters.`,
                    device_description_validation: (id) => `Please enter the description for section ${id} within 100 characters.`,
                }
            }
        },
        push: {
            facilities: 'Target facility',
            set_time: 'Set a time',
            send_immediately: 'Send immediately',
            send: 'Send',
            pick_member: 'Please choose from the list on the left',
            message: {
                repush: 'Do you want to send the same content?',
                new_create:  'Create a new notification',
                delete: 'Do you want to delete this notification?',
                send_immediately: `Do you want to send immediately the selected contents?`,
                set_time: (date,time,name) => `Do you want to send at ${date} ${time}?`,
            },
            error: {
                content: 'Please select the contents you wish to send.',
                time: 'Please select the date.',
                channel: 'Please select channel.',
                language: 'Please select language.',
                channel_and_language: 'Please select channel and language.',
                count_zero: 'The number of targets is 0.',
                line_limit_warning: 'The targets have exceeded the possible push messages for the month. The messages will not be sent to some LINE users.'
            },
        },
        chat: {
            message: {
                token_error: "The user has logged in somewhere else. This screen will close.",
                bot_switch_error: "You have been switched to another bot. Please refresh the screen and try again.",
                network_error: "Your network connection is unstable. Please log out and log in again.",
                automatic_on: "Response mode was automatically turned on",
                filtering_error: "The maximum number of users that can be displayed once may have been exceeded, or the filtering conditions may have been incorrectly set. Please set the filtering conditions again.",
                message_send_error: "Message could not be sent to customer",
                mail_sent: "Sent mail",
                response_mode: "Switched to response mode",
                response_mode_change: "Response mode changed",
                response_mode_error: "Could not switch to response mode. Please send the message again.",
                other_operater_change: "This is currently being responded to by another operator. Do you want to respond to it instead?",
                other_operator_current: "This is currently being responded to by another operator",
                operator_changed: "Operator has changed",
                operator_change_error: "Operator change failed",
                user_info_change: "Customer information updated",
                send_request: "Do you want to send a request to the customer?",
                sales_mode_switch: "Switched to sales mode",
                display_error: "Could not get chat history",
                delete_message: "Do you want to delete the messsage? (It cannot be recovered once deleted)",
                kintone_support: "Requested support from Kintone",
                unrecognizable_question: "Do you want all of the unrecognizable questions to be handled?",
                unrecognizable_question_auto: "Do you want to turn on auto-respond mode and handle all unrecognizabel questions?",
                already_responded: "This customer has already been responded to",
                exit_chat_mode: "Are you sure you want to exit response mode? (The customer will be notified of the mode change)",
                auto_translate_on: "Automatic translation turned ON",
                auto_translate_off: "Automatic translation turned OFF",
                auto_response: "Switched to automatic response",
                auto_response_error: "Could not change the response mode",
                end_chat_mode: "End chat mode and turn on automatic response",
                upload_10MB_or_less: 'File size must be less than 10MB',
                upload_location: 'Request location',
                upload_image: 'Upload image',
                no_fixed_phrases: 'No fixed phrase defined'
            },
            label: {
                operator_responding: "Operator responding",
                other_operator_reponding: "Other operator responding",
                select_template: "Select template",
                ai_unknown_question: "AI unrecognizable questions",
                ai_unknown_question_clear: "Clear",
                ai_unknown_question_none: "None"
            }
        },
        reception: {
            label: {
                ticket_detail: 'Detailed setting of the issue date',
                date_setting: 'Date setting',
                start_date: 'Starting Date',
                end_date: 'End Date',
                weekdays: 'Date Selection',
                holiday: 'Public Holiday',
                excluded_date: 'Dates Excluded',
                include: 'Include',
                exclude: 'Exclude',
                not_set: "Not set",
                placeholder: 'Please enter the date or period Ex)2021/10/11,2021/12/20-2021/12/25',
                time_setting: 'Time setting',
                available_time: 'Usable Hours',
                excluded_time: 'Excluded Hours',
                item_cd: 'Item code',
                item_name: 'Item name',
                capacity: 'Capacity',
                capacity_min: 'Minimum',
                capacity_max: 'Maximum',
                reserve_alone: 'Not available for individual reservations',
            },
            message: {
                language: 'Please choose at least one language.',
                setting_time: 'Please set an issue date.',
                required: (label) => `${label} is required`,
                waiting_time: `Please set both "minutes per group" and "simultaneous reception per group" when configuring the waiting time.`,
                delete: 'Would you like to delete the corresponding facility?',
                display: 'Would you like to display the corresponding facility?',
                undisplay: 'Would you like to undisplay the corresponding facility?',
                capacity: 'Please set the number of people',
                printer: 'Please set the IP address / port number',
                display_auto_cancel_validation: 'Please set the time for Auto Cancel',
                display_must_check_items: 'Please check at least one item to display on the screen'
            },
            error_message: {
                pause_error_message: "Reception temporarily suspended",
                congestion_error_message: "Reception automatically stopped due to congestion",
                printer_connnect_error_message: "Failed to connect to the printer",
                limit_error_message: 'We have closed reception for today',
                network_error_message: 'Network error',
                unknown_error_message: "An error has occurred",
                refresh_label: "Refresh",
                reconnect_label: "Reconnect"
            }
        },
        receptionlist: {
            message: {
                pause_title :"Pause Reception",
                pause_message : "Requests from users will not be taken until the reception is resumed.",
                resume_title:"Resume Reception",
                resume_message:"Would you like to restart reception?",
                notify_title:"Would you like to notify the user?",
                notify_message:"The notification will be sent.",
                cancel_notification_title:"Would you like to cancel the notification?",
                cancel_notification_message:"This user will be skipped. The status will be left at 'Waiting'.",
                complete_title:"Would you like to complete the reception?",
                complete_message:"This user's reception is complete.",
                not_called_message:"This use is not called. Would you like to complete the reception?",
                cancel_reception_title:"Would you like to cancel the reception?",
                cancel_reception_message:"This user's reception wait will be canceled.",
                status_back_title:"Would you like to change the status back to waiting for reception?",
                status_back_message:"This user's status will be changed back to 'Waiting'.",
            },
            form_message: {
                not_number: "Please enter a number.",
                exceed_max_number: (num) => `The maximum value that can be entered is ${num}.`,
                exceed_min_number: (num) => `The minimum value that can be entered is ${num}.`,
                exceed_max_input_length: (num) => `Please enter a maximum of ${num} characters.`,
                exceed_min_input_length: (num) => `Please enter a minimum of ${num} characters.`,
                required: (label) => `${label} is required`,
            },
            label: {
                label_groups: 'groups',
                label_minutes : 'min.',
                people_num : "people",
                status_waiting: 'Waiting',
                status_calling_soon: 'Calling soon',
                status_calling: 'Calling',
                status_reception_complete: 'Reception Complete',
                status_auto_cancel: 'Auto Cancel',
                status_cancel: 'Cancel',
                
                button_status_call: 'Call',
                button_status_complete: 'Reception Complete',
                button_status_cancel: 'Cancel',
                button_status_cancel_call: 'Cancel Call',
                button_status_back_to_reception: 'Back to Reception',
                label_no_data: 'No data available',
            },
            bulk_action_text: (selectedCount) => `For ${selectedCount} selected items:`,
        },
        inquiry_survey: {
            link_text: 'Link text',
            add_link: 'Insert',
            default: 'Default',
            item_num: 'Number of items',
            question: 'Question',
            filter: 'Filter',
            filter_tips: 'Please select a language first.',
            expand: 'Expand',
            fold: 'Fold',
            type:{
                opt:'Single choice',
                chk:'Multiple choice',
                sel:'Dropdown',
                txt:'Short answer',
                txa:'Long answer',
                fup:'Upload file',
                frs:'Free space',
                mtx:'Matrix',
                fleq:'FAQ',
                prefecture_city:'Prefecture + City',
                maximum:'Reservation',
                name:'Name',
                address:'Address',
                payment:'Payment',
                scr: 'Scoring Evaluation',
                ord: 'Tabinaka Order',
            },
            txt:{
                tel:'Phone number',
                postcode:'Postal code',
                mail:'Email address',
                date:'Date',
                number:'Number',
                text:'Free input',
                coupon:'Coupon code',
                select_coupon:'Select Coupon code',
                num: (num) => `Every ${num} minutes`,
                excluding: 'Excluding',
                format: 'Format',
                time: 'Time',
                time_zone: 'Time zone',
                days: 'days',
                day: 'days',
                months: 'months',
                days_before: 'days before',
                excluding: 'Excluding day',
                future: 'Future',
                past: 'Past',
                all_period: 'Whole period',
                select_base_date: 'Select base date',
                dont_use: 'Do not select',
                application_date: 'Application date',
                start_date: 'Start date',
                end_date: 'End date',
                term_start: 'Start term',
                term_end: 'End term',
                excluding_holidays: 'Excluding holidays',
                including_holidays: 'Including holidays',
                reservable_period: 'Reservable period',
            },
            placeholder: {
                option: 'Choices',
                title: 'Title',
                city: '〇〇 City',
            },
            option: {
                text: 'Option',
                add: 'Add choices',
                input_title: 'Please enter a title',
                select: 'Please choose',
                add_image: 'Add an image',
                fup: 'Select file',
                change_image: 'Change',
                delete_image: 'Delete',
                limit_num_of_selections: 'Limit the number of options',
                limit_num_of_orders: 'Limit the number of orders',
                select_max: 'Maximum',
                select_min: 'Minimum',
                max_selection: 'Maximum',
                min_selection: 'Minimum',
                tax_included: 'Tax included',
                tax_service: 'Tax and service included',
                tax_none: 'Tax free',
            },
            mtx: {
                limit:'The maximum number has been added',
                limit_select: 'Choices selectable',
                no_limit: 'No limit',
                select_min: 'Minimum',
                select_max: 'Maximum',
                min: 'Minimum choices',
                max: 'Maximum choices',
                num: (num) => `${num}`,
            },
            scr: {
                eval_5: '5-point rating',
                nps: 'NPS rating',
                title_row: 'Title (row)',
                title: 'Title',
                option: 'Choices (column)',
                title_add: 'Add a title',
                score: 'points',
                eval_5_default: ["Very Dissatisfied", "Dissatisfied", "Average", "Satisfied", "Very Satisfied"],
                nps_default: ["Not Recommended", "Recommended"],
                scale: 'Evaluation Method'
            },
            message: {
                delete: {
                    survey:'Do you want to delete the selected item?',
                    label: (label) => `Delete the label,「${label}」.<br>The label setting will be removed.`,
                },
                error: {
                    time_format: 'Please enter the time in the correct format',
                    time_24: 'The time can be set until 24:00',
                    reservation: 'Please add your reservation',
                    branch_format: (num, title) => `The branch condition of “${num}. [${title}]” is incorrect. Please check the settings.`,
                    member_mail_template: 'Please set a user email template on the "Basic Settings" and enalbe "send this email".',
                    lang_type: (lang) => `Please check the form in ${lang}`,
                    duplicate_inquiry_res: 'Duplicate reservation information for item extension.',
                },
                confirm: {
                    translate: (text) => `${text} will be automatically translated. The current text will be overwritten. Are you sure you want to continue?`,
                    label:'Do you want to create a label from the title?',
                    sort_branch : {
                        title: '分岐条件の不整合を検知しました',
                        content: (item) => `分岐条件は条件元とする項目よりも後の項目にのみ設定可能です。</br>順番変更を続けますか？</br>（${item}の該当条件は削除されます）`,
                    }
                }
            },
            spl: {
                prefecture: 'Prefecture',
                city: 'City',
                name_separate: 'Separate first and last name',
            },
            branch: {
                title: 'Branches',
                select_answer: 'Please choose an answer',
                equal: 'Equal',
                not_equal: 'Not equal',
                include: 'Include',
                not_include: 'Not include',
                greater: 'Greater',
                smaller: 'Less',
                equal_or_greater: 'Equal or greater',
                equal_or_smaller: 'Equal or less',
                option_include: 'Include',
                option_not_include: 'Not include',
                after: 'After',
                before: 'Before',
                
                select_condition: 'Select condition',
                set: 'Set branches',
                select_title: 'Please choose a queston',
                select_answer: 'Please choose an answer',
                select_breakdown_type: 'Select breakdown type',
                select_max_of_reservations: 'Select the maximum number of reservations',
                select_type_of_branching_source: 'Select the source type',
                select_max_of_orders: 'Select the maximum number of orders',
                set_by_breakdown_type: 'Set conditions by breakdown',
                set_by_max_of_reservations: 'Set conditions by the maximum number of reservations',
                add_and: 'Please add a branch (AND)',
                delete: 'Do you want to delete the branch?',
                add: 'Add a branch',
                label_or: 'OR',
                // destination: 'Transition to',
                destination: 'Branch destination',
                condition: 'Conditions',
                title_placeholder: 'Condition title',
                add_condition: 'Add Conditions',
                count_of_orders: (title) => `Count of orders of ${title}`,
                orders_count: 'Count of orders',
                order_date: 'Date',
            },
            section: {
                title: 'Section',
                number: 'Number of questions',
            },
            maximum: {
                stock: 'Stock',
                reservation: 'Reservation',
                select: 'Please select an inventory',
                dont_use: 'Do not use an inventory',
                date: 'Date period',
                all: 'All period',
                discount: 'Discounts available',
                extension: 'Extension available',
                range: 'Range available',
                display_format: 'Display format',
                after_zero_clock: (time) => `Switch time：${time}`,
                up_to: (num) => `Up to ${num}`, 
                more_than: (num) => `More than ${num}`,
                before_hours: (num) => `Up to ${num} hours before`,
                before_hours_limit: (num) => `Until ${num}`,
                before_hours_minute: (hours, minutes) => `Until ${hours} hours ${minutes} minutes before`,
                after_days: (num) => `${num} days later`,
                quantity_limit: (num) => `Up to ${num}`,
                quantity_limit_min: (num) => `More than ${num}`,
                consecutive: (num) => `Consecutive slots：${num}`,
                cross_day: 'Cross day',
                unavailable: 'Unavailable',
                category_single_select: 'Category single-select',
                not_set: 'Not set',
                remove: 'Remove an inventory',
            },
            label:{
                text: 'Label',
                extension: 'Item Extension',
                select: 'Select label',
                add: 'Add a label',
                remove: 'Remove the label',
                edit: 'edit the label',
                delete: 'Delete the label',
                add_image: 'Add image',
            },
            res: {
                modal_title: 'Reservation information',
                select_title: 'Select reservation information',
                icon_pre_title: 'Reservation information',
                remove: 'Remove the reservation information',
            },
        },
        service: {
            label: {
                unhandled: 'Unhandled',
                handling: 'Handling',
                completed: 'Completed',
                cancelled: 'Cancelled',
                reception_id: 'Reception ID:',
                all: 'All Status',
            },
            message:{
                memo_delete: 'Do you want to delete this memo?',
                history_delete: 'Do you want to delete the user history?',
                complete : 'Memo deleted.',
                refresh_alert:'Closes the current page, because it is being switched to another bot.',
                notification_title:'Receipt notification',
                notification_message: 'Reception came from a customer.',
                notification_message_fail: 'Failed to get reception information.',
                messageDisplaying: (last_button_name,button_name) => `Do you want to change the status from "${last_button_name}" to "${button_name}"?`,
            }
        },
        
        sitepages: {
            label: {
                placeholder_title: 'Please enter a title.',
                type: 'Type',
                event_schedule: 'Event/Schedule',
                regular_holiday: 'Regular Holiday',
                time_zone: 'Time Zone',
                repetition: 'Repetition',
                weekly: 'Weekly',
                monthly: 'Monthly',
                yearly: 'Yearly',
                repetition_interval: 'Repetition Interval',
                all_day: 'All Day',
                start_date: 'Start Date',
                end_date: 'End Date',
                holiday: 'Holiday',
                excluded_date: 'Excluded Date',
                include: 'Include',
                exclude: 'Exclude',
                not_set: 'Not Set',
                placeholder_exclude: 'Please enter dates or periods. e.g., 2021/10/11, 2021/12/20-2021/12/25',
                color: 'Color',
                every_month: 'Every Month',
                no: 'No.',
                weekdays: 'Weekday',
                day: 'Day',
                color: 'Color',
            },            
            message: {
                copy_title: 'Do you want to copy the selected site page?',
                copy_description: 'Create a new site page.',
                image_max_length: (num) => `You can set up to ${num} images.`,
                error: 'Error',
                valid_error: 'There is an issue with the setting value.',
                valid_error_startdate_empty: 'The start date is not entered.',
                valid_error_enddate_empty: 'The end date is not entered.',
                valid_error_invalid_enddate: 'Please set the end date to a day after the start date.',
                valid_error_title_empty: 'The title is not entered.',
                valid_error_weekday_empty: 'Please select at least one weekday.',
                valid_error_start_empty: 'The start time is not entered.',
                valid_error_end_empty: 'The end time is not entered.',
                valid_error_invalid_end: 'Please set the end time to a time after the start time. For events that span multiple days, the end time must be before 6:00 AM.',
                valid_error_freq_and_interval: 'The frequency and interval are not entered.',
                valid_error_monthly_pulldown: 'There are unselected items in the repetition interval.',
            },
            button: {
                dialog_button_create: 'Create',
            },
        },
        inquirys: {
            label: {
                data_table_search_placeholder: 'Search by code or inquiry name',
                class_cd_default_option: '- Category tag -',
            },
            message: {
                new_copy_action_title: 'Would you like to create a new form?',
                new_copy_action_description: 'A new form will be created by copying the selected form.',
                new_version_action_title: 'Would you like to create a new version of the form?',
                invalid_range_setting: 'Please enter all settings for the range setting',
                copy_template_action_title: 'Would you like to create these forms from the template forms?',
            }
        },
        inquiry: {
            label: {
                delete_action_title: 'Do you want to delete the inquiry form?',
                delete_action_description: 'All user inquiry data will be deleted. Are you sure? Data cannot be recovered.',
                delete_action_confirm: 'Delete',
                delete_confirm_caption: 'Delete',
                confirm_title: 'Do you want to go to the edit screen?',
                usage_limit: 'Usage Limit:',
                mail_recipients: 'Recipients',
            },
            message: {
                error: {
                    inquiry_name_error: 'Please enter the inquiry form name.',
                    user_in_charge_error: 'Please select a person in charge.',
                    from_email_error: 'Please enter a valid sender email.',
                    reply_email_error: 'Please enter a valid reply email.',
                    limit_public_password_error: 'Please enter a password for limited public access.',
                    member_mail_template: 'The template to send to the user cannot be released.',
                    aws_varify: 'AWS authentication has failed.',
                    aws_domain_verify: 'The domain you entered is invalid or unavailable.<br>Please enter a valid domain.',
                    empty_domain: 'Please enter a domain.',
                    admin_email_error: "Please set the Send to administrator template on the Basic Settings page.",
                    support_lang: 'Please select one or more supported languages.',
                    please_input_receipt_tax_rate: 'Please input the receipt tax rate',
                    please_input_pay_limit: 'Please enter payment deadline',
                    mail_template_lang_error: 'Cannot set email template',
                    pms: 'PMS integration is set in the inquiry form creation, so PMS integration cannot be turned off.',
                    mail_template_error_message: (template_name) => {
                        return `Not all of the languages in [${template_name}] correspond to the languages displayed on this form. Please add data for each language to [${template_name}]. </br>`
                    },
                    cancel_policy_talkappipay_limit: 'Cancellation policy settings can only be applied via talkappi PAY.',
                },
                popup: {
                    aws_mail_confirm:'Please click the URL in the approval email from Amazon to approve. If you do not approve, the email will not be sent.'
                }
            }
        },
        inquirydesc: {
            label: {
                main_picture_add: 'Add Picture',
                translate_confirm_title: ' Automatic translation based on will be performed. The current content will be overwritten. Are you sure you want to proceed with the translation?',
                upload_image_oversize: 'Please select a file within 2MB for the image.',
                delete_action_title: 'Do you want to delete this action?',
                fold_expand: 'Expand',
                fold_close: 'Close',
                main_picture_recommend_aspect_ratio_label: ', For multiple sheets, all should be registered in the same ratio.'
            }
        },
        inquiryresult: {
            label: {
                none_selected_text_support: "- Support -",
                none_selected_text_status: "- Status -",
                content_delete_confirm: "Delete",
                delete_confirm_caption: "Delete confirmation",
                change_seach_conditions: 'Change Serach Conditions',
                save_seach_conditions: 'Save Serach Conditions',
                untitled_conditions: 'Untitled Conditions',
                cancelled : 'Cancelled',
            },
            message: {
                reservation_cancel_title: "Cancel the reservation?",
                content_delete_title: "Delete this content?",
                content_delete_desc: "It will be removed from the list and cannot be restored.",
                status_change_confirm: "Change the status?",
                user_spam_confirm_title: "Mark this user as spam?",
                user_spam_confirm_desc: "Inquiries from spam users are not displayed by default.",
                user_spam_remove_title: "Remove this user from the spam list?",
                user_spam_remove_desc: "Inquiries from this user will be displayed by default after removal.",
                mail_resend_confirm_title: "Resend completion email?",
                user_history_delete_comfirm_title: "Delete the user's history?",
                download_confirm_title: "Download selected items?",
                success_save_conditions:"Search conditions have been saved",
                fail_save_conditions:"Failed to save conditions",
                delete_condition_confirm: (title) => `Do you want to delete the saved refinement condition "${title}"?`,
                support_memo_2_step_amount: 'A payment request email has been sent. The amount is "{amount}" yen, and the deadline is "{limit}".',
                support_memo_2_step_refuse: 'An email has been sent stating that the reservation was not possible. The reservation has been cancelled.',
                mail_2_step_refuse_title: 'Send no reservation email?',
                inquiry_cancel_365_error: 'Credit card payments cannot be canceled after 365 days have passed.',
                mail_memo_member: 'Resent mail to guest.',
                mail_memo_admin: 'Resent mail to admin.',
                cancel_policy_cancel: 'Cancellations made through the management screen will be 100% refunded.',
                error: {
                    error_delete_fail: "Deletion failed.",
                    mail_resend_fail: "Sending failed.",
                    csv_export_fail: "Language selection is required due to differences in multilingual definitions.",
                    admin_modify:'This can not be modified.'
                },
                success: {
                    status_update_success: "The status has been updated.",
                    mail_resend_success: "Email sent."
                },
                admin_modify_title:'Do you want to modify this reservation?',
                auto_translate_fail:'Automatic translation failed.',
            }
        },
        survey: {
            message: {
                survey_name_error: 'Please enter a survey name.',
                support_lang_error: 'Please select one or more supported languages.',
                user_in_charge_error: 'Please select a person in charge.',
                delete_action_title: 'Do you want to delete the survey?',
                delete_action_description: 'All survey results and statistical data will be deleted.<br>Are you sure? This operation cannot be undone.',
                mail_template_confirm_title: 'Do you want to go to the edit screen?',
                mail_template_confirm_description: 'Unsaved changes will be lost.',
            },
        },
        surveydesc: {
            message: {
                section_delete_confirm: 'Do you want to delete this description section?',
            },
            label: {
                translate_confirm_title: ' Automatic translation based on will be performed. The current content will be overwritten. Are you sure you want to proceed with the translation?',
            },
        },
        surveyresult: {
            label: {
                customize_title: 'Customize graph',
                customize_graph_title: 'Title',
                customize_colors: 'Colors',
                customize_legend_position: 'Legend Position',
                customize_legend_top: 'Top',
                customize_legend_bottom: 'Bottom',
                customize_legend_left: 'Left',
                customize_legend_right: 'Right',
                customize_slice_text: 'Display in graph',
                customize_slice_text_percentage: 'Percentage',
                customize_slice_text_not_show: 'Hidden',
                customize_cat_row: 'Number of each item',
                customize_cat_row_show: 'Show',
                customize_cat_ratio_row: 'Numeric Axis Display',
                customize_cat_ratio_row_ratio: 'Ratio',
                customize_not_show: 'Hidden',
                customize_answers: 'Number of answers',
            },
            message: {
                delete_action_description: 'The data will be deleted from the list. Data cannot be restored.',
                error: {
                    safari_csv_export_fail: 'PDF output function is not available in safari.',
                    filter_value_required: 'Please select a value for the filter item.',
                }
            }
        },
        surveyreport: {
            label: {
                bar_monthly_graph_title: 'Monthly Answer Count',
                bar_daily_graph_title: 'Daily Answer Count',
                period_label: 'Period',
                count_answer: 'Answer Count',
            }
        },
        surveyentry: {
            message: {
                select_display_coupons: 'Please select coupons',
                display_coupons: 'Coupons to display the answer',
                display_coupon_switch_out_warning: 'This unselected coupon was also removed from the "Coupons to display the answer" settings of all entries.',
                display_coupon_deleted_modal_warning: ' If this coupon is selected in the "Coupons to display the answer" settings of any entries, it will also be removed from all those entries.',
                display_coupons_cleared_modal_warning: ' Coupons will also be removed from the "Coupons to display the answer" settings of all entries.',
                delete_coupon_confirm_message: 'Do you want to delete the coupon?',
                delete_all_coupon_confirm_message: 'Do you want to delete all the coupons?',
                delete_item_confirm: 'Do you want to delete this item?',
                delete_section_confirm: 'Do you want to delete this section?',
                delete_option_image: 'Do you want to delete this picture?',
                irreversible_action: 'This action cannot be undone',
            },
            error: {
                using_deleted_coupon_error: (question_title) => `[${question_title}] is set with the unusable coupon(s), please check it`
            }
        },
        msg: {
            label: {
                carousel: 'Carousel',
                check_json: 'Check JSON format',
                true_json: 'JSON format: The format is correct',
                false_json: 'JSON format: The format is incorrect',
                add_image: 'Add image',
                edit_type: 'Edit type',
                destination_lang: 'Destination language',
                destination_url: 'Destination URL',
                mobile: 'Smartphone URL',
                add_btn: 'Add button',
                delete_carousel: 'Delete carousel',
                priority_customize_name: '*The customized button name will be prioritized.',
                no_btn_name: '*Will not be displayed when button name is not set.',
                click_counts: 'Count clicks',
                channel: 'Channel of use',
                styles: 'Customize style',
                survey: 'Questionnaire',
                inquiry: 'Inquiry/Reservation',
                listing_period: 'Display Period',
            },
            message: {
                error_summernote_code: 'Please click on [＜／＞] again to end the code editing.',
                error_json: 'Please fix the JSON format or deactivate the JSON format check.',
                error_image: 'Please choose an image',
                delete_current_lang_confirm: (lang) => `Would you like to delete the content(${lang})?`,
                delete_all_lang_confirm: 'Would you like to delete the content (all languages)?',
                restore_default_contents_confirm: 'Would you like to delete the content (all languages) and restore the default content?',
                mail_template_allert: 'You cannot delete the email template because it is referenced in the following settings.',
                code_edit_fail_alert: 'You cannot change the email template code because it is referenced in the following settings.',
            },
        },
        payment: {
            label: {
                bot_settings: 'Facility Settings',
            },
            message: {
                paymens_delete_action_title: 'Do you want to delete this transaction?',
                payment_name_error: 'Please enter the name of the client',
                payment_postal_code_error: 'Please enter a valid postal code',
                payment_tel_error: 'Please enter a valid phone number',
                payment_account_number_error: 'Please enter a valid account number',
                payment_delete_action_title: 'Do you want to delete this transaction?',
                payment_delete_action_description: 'All transaction details and monthly billing records will be deleted. This action cannot be undone. Are you sure you want to proceed?'
            },
            option: {
            },
        },
        report: {
            label: {
                user_count_by_time_period_label: 'Users count by time period',
                time_period_label: 'Time period',
                users_count_label: 'Users count',
                clicks_count_label: 'Click count',
                session_count_label: 'Session count',
                new_user_label: 'New users count',
                japanese_label: 'Japanese',
                english_label: 'English',
                simplified_chinese_label: 'Simplified Chinese',
                traditional_chinese_label: 'Traditional Chinese',
                korean_label: 'Korean',
                time_period_settings: 'Time period',
                csv_format: 'CSV format',
                by_day: 'By day',
                by_month: 'By month',
            },
            message: {
                mail_send_confirm: 'Are you sure you want to send the payment notice by email?',
                mail_success: 'Email sent successfully.',
                mail_invalid_date: 'The date is not entered correctly.',
                mail_invalid_receiver: "Please register the recipient's information (company name, person in charge of settlement, email address) correctly.",
                mail_invalid_account_number: 'Please enter a valid account number.',
                mail_error: 'Failed to send email. Please check if the required information is registered.',
                mail_send_complete: 'Send complete',
            }
        },
        schedule: {
            label: {
                day : "day",
                week : "Week",
                month: "Month",
                time_setting: "Time setting",
                congestion: "Congestion",
                business_hours : "Business hours",
                unset :"unset",
                today: "today",
                all_day: "All day",
            }
        },
        newsletter: {
            filter_tips: 'Please select filter conditions',
            message: {
                email_title_error: 'Please enter the subject.',
                project_name_error: 'Please enter the project name.',
                user_in_charge_error: 'Please select a person in charge.',
                task_exec_status_error: (operation) => { 
                    if (operation == 'edit') {
                        return 'The newsletter is being sent or has already been sent, so it cannot be edited.';
                    } else if (operation == 'delete') {
                        return 'The newsletter is being sent or has already been sent, so it cannot be deleted.';
                    }
                },
                error: {
                    duplicated_extend_tpl_alias: 'The variable name of the extension item is duplicated',
                    extend_tpl_alias_edit_fail: 'Failed to edit the extension item variable name.',
                    reply_email_error: 'Please enter a valid reply email address.',
                },
                members_delete_action_title: 'Do you want to delete the selected contacts?',
                members_delete_action_description: 'The contacts and all related data will be deleted.',
                members_project_link_action_description: 'All related data(in task, attached tags and extended attributes) of the contacts and  will be deleted.',
                tags_project_link_action_description: 'All attached tags will be detached from contacts.',
                delete_action_title: 'Do you want to delete the corresponding task?',
                delete_action_description: 'The task will be canceled, and all related data will be deleted.',
                mail_template_confirm_title: 'Do you want to move to the editing screen?',
                mail_template_confirm_description: 'Unsaved content will be lost',
                csv_preview_warning: (undef) => `The following columns are undefined, so data will not be loaded: ${undef}.`,
                csv_import_completed: 'Contacts registration completed from CSV',
                mail_project_link_completed: 'Contacts added into project',
                tag_project_link_completed: 'Tags added into project',
                resolve_suppression_completed: email => `${email} removed from the suppression list`,
                not_found_on_suppression_list: email => `Email address ${email} does not exist on the suppression list`,
                csv_import_status_detail: (total,imported,skipped) => { 
                    if (skipped) {
                        return `Out of ${total} entries, ${imported} were imported (${skipped} were duplicates). Press the 'Complete' button to finish registration.`;
                    } else {
                        return `Out of ${total} entries, ${imported} were imported. Press the 'Complete' button to finish registration.`;
                    }
                 },
                mail_project_link_status_detail: (total,imported,skipped) => { 
                     if (skipped) {
                         return `Out of ${total} entries, ${imported} were added (${skipped} were duplicates).`;
                     } else {
                         return `Out of ${total} entries, ${imported} were added.`;
                     }
                  },
                tag_project_link_status_detail: (total,imported,skipped) => { 
                      if (skipped) {
                          return `Out of ${total} entries, ${imported} were added (${skipped} were duplicates).`;
                      } else {
                          return `Out of ${total} entries, ${imported} were added.`;
                      }
                   },
                csv_import_status_detail_from_inquiry: (total,imported) => { 
                    return `${imported} out of ${total} entries were imported. (Duplicate email addresses are not imported)`;
                },
                delete_tag_title: 'Are you deleting the corresponding tag?',
                delete_tag_description: 'Are you deleting the corresponding tag?',
                not_unique: 'Extension items are duplicated.',
                not_selected: 'There are unselected items.',
                copy_signature: 'Do you want to copy the signature?',
                delete_signature: 'Are you sure you want to delete the signature?',
                delete_project: 'Are you sure you want to delete the project?',
            },
            title: {
                extend_alias_edit_panel: (operation, extend_name) => { 
                    if (operation == 'edit') {
                        return `Edit alias for ${extend_name}`;
                    } else if (operation == 'add') {
                        return `Define alias for ${extend_name}`;
                    }
                 },
                 import_filter_modal: 'Select filter condition',
                 select_project: 'Select project',
                 add_to_project: 'Add to project',
            },
            label: {
                name: 'Name',
                mail: 'Email Address',
                tag: 'Tag',
                delete: 'Delete',
            },
            button: {
                insert_extend: 'insert param',
            },
            tooltip: {
                insert_extend: 'insert template parameter',
            },
        },        
        account: {
            message: {
                user_login_error_title: 'User Login',
                user_delete_confirm_title: 'Do you want to delete this account?',
                user_unfreeze_confirm_title: 'Do you want to unfreeze this account?',
                error: {
                    required_fields_not_filled: 'Required form fields are not filled.',
                    email_addr_full_width_space: 'Full-width space is included.',
                    email_addr_cannot_register: 'The email address you entered cannot be used to register an account.',
                    password_rule_1: 'The password must be at least 11 digits and contain at least one number, letter, and special character.',
                    password_rule_3: 'Password does not match.',
                },
            },
        },
        alert: {
            message: {
                error: {
                    required_day: 'Alert Email: Please enter the alert date and time.',
                    required_mail_users: 'Alert Email: Please select recipients.',
                },
            },
            label: {
                previous: 'Previous Alert Settings',
                complete: 'Sent',
                day_time: 'Alert Date and Time',
                mail_users: 'Recipients',
                remark: 'Remarks',
            },
        },
        congestion: {
            label: {
                '1': 'Available',
                '2': 'Slightly congested',
                '3': 'Very congested',
                '4': 'Entry restricted',
            },
        },
        translate: {
            label: {
            'translate_estimate':'Translation estimate',
            'high_estimate':'2 to 3 business days',
            'normal_estimate':'Within 10 business days',
            'low_estimate':'Within 20 business days',
            }
        },
        member: {
            label: {
                'age_group' : 'Age Group',
                'area' : 'Area',
                'member_status' : 'Member Status',
                'purpose' : 'Purpose',
                'reservation' : 'Reservation',
                'channel' : 'Channel',
                'about_car' : 'About the Car',
                'all' : 'All',
                'all_conditions' : 'All Conditions',
                'no_filter_condition' : 'No Filter Condition'
            },
            message : {

            }
        },
        botscene: {
            message: {
                empty_name_error: (index) => `Name is a required field. Enter the name of the section ${index}.`,
                empty_parameter_error: (index) => `The publication Parameters is a required field. Please enter the publication parameters for section ${index}.`,
                duplicate_name_error: (s1, s2) => `Duplicate language and suffix settings in sections ${s1} and ${s2}. Please enter them without duplication.`,
            }
        },
        issues: {
            message: {
                'redmine_setting_error' : 'Redmine integration is not properly configured.',
                'redmine_user_error' : 'The request permissions have not been granted. Please consult with your company’s representative',
                'redirect_to_top' : 'Redirecting to the top page.',
            }
        },
        disaster: {
            label: {
                'disaster_greeting_type': 'Greeting',
                'disaster_welcome_message_type': 'Welcome Message',
                'disaster_preview': 'Preview',
            },
            message: {
                'not_available' : 'Not available',
                'chatbot_only' : 'This page is for facilities using the chatbot only.<br>Logging out.',
                'toggle_display': 'Would you like to switch the display?',
                'apply_immediately': 'The changes will be applied immediately.',
            }
        },
        batch: {
            label: {
                page_title: 'Batch Execution Status List',
                execution_date_range: 'Execution Date Range',
                batch_type: 'Batch Type',
                execution_status: 'Execution Status',
                batch_select: 'Select Batch',
                manual_execute: 'Manual Execute',
                execution_date: 'Execution Date',
                batch_name: 'Batch',
                type: 'Type',
                status: 'Status',
                priority: 'Priority',
                details: 'Details',
                status_pending: 'Pending',
                status_running: 'Running',
                status_success: 'Success',
                status_failed: 'Failed',
                status_skipped: 'Skipped',
                status_unknown: 'Unknown',
                priority_high: 'High',
                priority_normal: 'Normal',
                priority_low: 'Low',
                type_auto: 'Auto Execution',
                type_manual: 'Manual Execution',
                type_retry: 'Retry Execution',
                execution_results: 'Execution Results',
                execution_time: 'Execution Time',
                exit_code: 'Exit Code',
                records_processed: 'Records Processed',
                processed: 'Processed',
                success: 'Success',
                failed: 'Failed',
                error_info: 'Error Information',
                retry_control: 'Retry Control',
                retry_count: 'Retry Count',
                next_retry: 'Next Retry',
                retry_reason: 'Retry Reason',
                daily_success_count: 'Daily Success Count',
                timestamp: 'Timestamp',
                scheduled_time: 'Scheduled Time',
                start_time: 'Start Time',
                end_time: 'End Time',
                created_at: 'Created At',
                updated_at: 'Updated At',
                system_info: 'System Information',
                execution_type: 'Execution Type',
                execution_server: 'Execution Server',
                process_id: 'Process ID',
                created_by: 'Created By',
                updated_by: 'Updated By',
                keyword_placeholder: 'Batch name, type, etc.',
                data_load_failed: 'Failed to load data.',
                duration_hours: 'hours',
                duration_minutes: 'minutes',
                duration_seconds: 'seconds'
            },
            message: {
                error: {
                    select_batch_required: 'Please select a batch.',
                    invalid_batch: 'Invalid batch specified.',
                    manual_execution_not_allowed: 'This batch cannot be executed manually.',
                    start_date_format_error: 'Start date format is incorrect.',
                    end_date_format_error: 'End date format is incorrect.',
                    date_range_error: 'Start date must be before end date.',
                    hotel_config_error: 'Failed to get related hotel information.',
                    system_error: 'An error occurred while adding manual execution task. Please contact system administrator.'
                },
                success: {
                    manual_execute_added: 'Manual execution task added, will start in next execution.',
                    manual_execute_added_with_id: (id) => `Manual execution task added, will start in next execution. Execution ID: ${id}`
                },
                confirm: {
                    manual_execute_title: 'Batch Manual Execution Confirmation',
                    manual_execute_detail: 'Do you want to start manual execution of the following batch：',
                    running_batch_refresh_title: 'Page Refresh Confirmation',
                    running_batch_refresh_detail: 'There are running batches. Do you want to refresh the page?'
                },
                notification: {
                    running_batch_auto_refresh: 'There are running batches. Auto-refresh in 30 seconds.'
                }
            }
        }
    }
}();
