<?php defined('SYSPATH') or die('No direct script access.');

return array(
	'html.title' => 'talkappi Booking',
	'index.findroom' => 'Find your room',
	'index.change' => 'Change reservation',
	'index.range' => 'Length of stay',
	'index.range_canot_change' => 'cannot change',
	'index.checkin' => 'Check-in',
	'index.checkin_fix_label' => '',
	'index.checkin_nochange' => 'Cancellation is required to change check-in date',
	'index.error_no_rooms' => 'Rooms are fully booked on the chosen dates.',
	'index.error_longterm' => 'At least {longterm} nights are needed to use this plan.',
	'index.error_max_longterm' => 'The plan chosen cannot be used for stays over {longterm} days.',
	'index.error_timeout' => 'An unexpected error has occurred.',
	'index.checkout' => 'Check-out',
	'index.rooms' => 'Rooms',
	'index.adults' => 'Adults<small style="font-size:8px;">/room</small>',
	'index.kids' => 'Children',
	'index.smoking' => 'Smoking',
	'index.nosmoking' => 'Nonsmoking',
	'index.bothsmoking' => 'Both',
	'index.fix_nosmoking' => '*All rooms are nonsmoking',
	'index.fix_smoking' => '*All Rooms are smoking rooms',
	'index.search' => 'Select Room',
	'index.close' => 'CLOSE',
	'index.input' => 'Reservation Information',
	'index.customer' => 'Guest Information',
	'index.name' => 'Name',
	'index.name_mark' => '',
	'index.name_placeholder' => 'ex) John Smith',
	'index.name_kana' => 'Name kana',
	'index.tel' => 'Phone number',
	'index.tel_mark' => '',
	'index.tel_placeholder' => 'ex) *****-123-4567',
	'index.mail' => 'Email',
	'index.mail_mark' => '',
	'index.mail_mark_dtl' => 'A confirmation email will be sent to this email address.',
	'index.mail_placeholder' => 'ex) <EMAIL>',
	'index.remark' => 'Requests',
	'index.confirm' => 'Complete Booking',
	'index.save' => 'SAVE',
	'index.notes' => 'Detail',
	'index.must' => '＊',
	'index.kids_age' => 'Age of child',
	'index.rooms_v2' => 'Rooms',
	'index.adults_v2' => 'Adults',
	'index.kids_v2' => 'Children',
	'index.number_unit' => '',
	'index.calendar_month' => '',
	'index.calendar_day' => '',
	'index.calendar_split' => '～',
	'index.apply' => 'Confirm',
	'index.step' => 'Booking complete in 2 steps',
	'notes.title' => 'Cancellation Policy',
	'notes.hotel_info' => 'Hotel Information',
	'complete.title' => 'Reservation result',
	'complete.agent' => 'talkappi reservation result',
	'complete.success_no' => 'Reservation number',
	'complete.success' => 'Your reservation has been confirmed. Please check your email adree that you have registered for the confirmation email of your reservation.',
	'complete.fail_no' => 'Error code',
	'complete.fail' => 'We could not complete your reservation. Please go back and redo your operation.',
	'error.invalid_access' => 'Unauthorized access.',
	'error.exception' => 'An error has occurred.',
	'index.error_name' => 'Please enter your name in alphabet',
	'index.error_tel' => 'Please enter a valid phone number',
	'index.error_mail' => 'Please enter a valid email address',
	'index.error_required' => 'Please enter your answer',
	'index.plan_detail' => 'Check the plan for the detail',
	'index.amount' => 'Tax included',
	'index.free_cancel_title' => 'Free cancellation',
	'index.free_cancel_deadline' => 'Can be canceled {days} days before check-in date',
	'index.free_cancel' => 'Free cancellation before {date} {time} (Tokyo Standard Time)',
	'index.free_cancel_title_1' => 'Cancellation fee applies starting today',
	'index.free_cancel_1' => 'Please check the cancellation policy for the detail',
	'index.free_cancel_title_2' => 'No cancellation',
	'index.free_cancel_2' => 'Please check the cancellation policy for the detail',
	'index.privacy' => 'Save information for other reservations',
	'index.coupon' => 'Coupon',
	'index.coupon_count' => ', your have {num}',
	'index.ignore_coupon' => 'Do not use any',
	'index.confirmation' => 'Confirmation',
	'index.confirmation_desc' => 'We aim to provide excellent service to you. Please answer the following questions',

	'index.amount.title' => 'Amount Detail',
	'index.amount.detail' => 'Detail',
	'index.amount.calculating' => '',
	'index.pay.title' => 'Choose a payment method',
	'index.pay.credit' => 'Online credit card payment',
	'index.pay.onsite' => 'On-site payment',
	'index.pay' => 'To payment',
	'index.adults_v2.male' => 'Adult(male)',
	'index.adults_v2.female' => 'Adult(female)',
	'index.error' => 'Error',

	'index.link' => 'Sync corporate membership',
	'index.link_1' => 'User verification',
	'index.link_2' => 'Confirm corporate information',
	'index.link_forgot_pass' => 'Forgot your corporate no./password?',
	'index.link_corp_name' => 'Facility subject to {bot_name}',
	'index.link_auth_desc' => '<b>A verification code has been sent to your registered email address.</b><br/>Please enter the verification code and click “Complete reservation”<br/><br/>*Please check your spam folder if you have not received a verification email.',
	'index.link_auth_title' => 'Enter verification code',
	'index.link_auth' => 'Verification code sent to your email address (6 digits)',
	'index.link_corp_desc' => 'If it is your first time, please register customer information. If you are a corporate member, please enter the “Corporate membership number” and “Corporate password” below and click “Sync” ',
	'index.link_confirm' => 'This hotel is a facility subject to {bot_name}.<br/>Please confirm the corporate membership information when making your reservation.',
	'index.link_title' => 'Corporate number',
	'index.link_title_tip' => 'Forgot your corporate no./password?',
	'index.link_corp_no' => 'Corporate No.',
	'index.link_password' => 'Corporation password',
	'index.button.link' => 'Sync',
	'index.link_success' => 'We could not find an account.',
	'index.link_fail' => 'We could not find an account.',
	'index.link_member_title' => 'Member information',
	'index.link_member_gender' => 'Sex',
	'index.link_member_birthday' => 'Birthday',
	'index.link_member_address' => 'Address',
	'index.link_member_postcode' => 'Postcode',
	'index.link_member_address1' => 'Prefecture',
	'index.link_member_address2' => 'City',
	'index.link_member_address3' => 'Street',
	'index.link_member_address4' => 'Name of building/Room number(Optional)',
	'index.link_member_mobile' => 'Cell phone number',
	'index.link_member_corp_title' => 'Corporation information',
	'index.link_member_company' => 'Workplace',
	'index.link_member_department' => 'Department',
	'index.link_member_company_tel' => 'Workplace phone number',
	'index.link_member_privacy' => '<a href="{privacy_url}" target="_blank">Agree to the Terms and Conditions and privacy policy</a>',
	'index.button_link_reserve' => 'Complete reservation',
	'index.label.link_modify' => 'Change',
	'index.error.link.input' => 'Please enter a valid code.',
	'index.error.link.input.footer' => 'The information is incorrect',

	'booking.template.title' => 'talkappi BOOKING',
	'booking.template.login' => 'Login/Join',
	'booking.template.footer.discovery' => 'Discover more',
	'booking.template.footer.news' => 'News',
	'booking.template.footer.magzine' => 'Email Newsletter ',
	'booking.template.footer.about' => 'About talkappi',
	'booking.template.footer.aboutdtl' => 'Connecting facilities & travelers throughout the journey',
	'booking.template.step.1' => 'Room selection',
	'booking.template.step.2' => 'Enter your detail',
	'booking.template.step.3' => 'Confirmation',
	'booking.template.step.4' => 'Final',
	'booking.template.button.detail' => 'Details',
	'booking.template.button.select' => 'Select',
	'booking.template.button.back' => 'Back',
	'booking.template.label.comment' => 'Reviews',
	'booking.template.label.period' => 'Period of stay',
	'booking.template.label.checkin' => 'Check-in',
	'booking.template.label.checkin.s' => 'Check-in',
	'booking.template.label.checkout' => 'Check-out',
	'booking.template.label.checkout.s' => 'Check-out',
	'booking.template.label.days' => 'Nights',
	'booking.template.label.pcount' => 'People',
	'booking.template.label.reserve.info' => 'Reservation Information',
	'booking.template.label.reserve.infodtl' => 'Details of the reservation',
	'booking.template.label.reserve.userinfo' => 'Guest Information',
	'booking.template.label.rooms' => 'Rooms',
	'booking.template.label.adults' => 'Adults',
	'booking.template.label.kids' => 'Children',
	'booking.template.label.more' => 'Show more',
	'booking.template.label.price' => 'Price per night',
	'booking.template.label.roomtype' => 'Room type',
	'booking.template.label.plan' => 'Accommodation plan',
	'booking.template.label.hotel.detail' => 'Hotel',
	'booking.template.label.term' => 'Terms of service',
	'booking.input.label.agree' => 'Agree and continue booking',
	'booking.input.label.title' => 'Enter guest information',
	'booking.input.label.lastname' => 'Last Name',
	'booking.input.label.firstname' => 'First Name',
	'booking.input.label.mail' => 'Email address',
	'booking.input.label.vmail' => 'Re-enter email address',
	'booking.input.label.phone' => 'Phone number',
	'booking.input.button.confirm' => 'Confirmation',
	'booking.complete.title' => 'Thank you for making a reservation',
	'booking.roomtype.button.find' => 'Search',
	'booking.roomtype.label.result' => 'Results (Room type)',
	'booking.plan.label.title' => 'Select a plan',

	'faq.button.home' => 'TOP',
	'faq.button.switch' => 'Switch category',
	'faq.title.category' => 'Category',
	'faq.title.top' => 'FAQ',
	'faq.title.keywords' => 'Search by frequently used keywords',
	'faq.title.faq_ai' => 'AI recommended questions',
	'faq.title.faq_most' => 'Frequently asked questions',
	'faq.title.faq_result' => 'Search results',
	'faq.title.faq_tag' => 'Tag results',
	'faq.title.no_result' => 'No results',
	'faq.title.faq_category' => 'in FAQ',
	'faq.title.faq_relation' => 'Related questions',
	'faq.title.category_search' => 'Choose category',
	'faq.title.contents' => 'Check the recommended information below',
	'faq.placeholder.keyword' => 'Please enter your question',
	'faq.button.search' => 'Search',
	'faq.title.contact' => '※Please try here if you cannot find it in the FAQ',
	'faq.button.contact' => 'Inquiry→',
	'faq.button.up' => 'Back to TOP',
	'faq.title.page' => ' Page',
	'faq.title.page.total' => ' items found: displaying ',
	'faq.title.page.range' => ' items',
	'faq.title.fontsize' => 'Text Size',
	'faq.title.close' => 'Close',
	'faq.button.fontsize.large' => 'Large',
	'faq.button.fontsize.normal' => 'Normal',
	'faq.button.fontsize.small' => 'Small',
	'faq.title.detail' => 'Please click below for the details.',
	'faq.title.detail2' => 'We will show you information on each hotel.<br/>Please click on the "Details" button below for further details.',
	'faq.button.detail' => 'Details',
	'faq.title.survey1' => 'Please tell us how you felt so we can improve our service.',
	'faq.title.survey2' => 'Was the answer helpful?',
	'faq.title.survey_fin' => 'Thank you for answering the survey.',
	'faq.title.survey_input_1' => 'What did you think of the service?',
	'faq.title.survey_input_2' => 'Please do not post your questions here as we would not be able to respond through this channel.',
	'faq.button.back' => 'Back',
	'faq.button.survey.yes' => 'Good',
	'faq.button.survey.no' => 'Bad',
	'faq.button.survey.send' => 'Send',
	'faq.label.left_bracket' => '"',
	'faq.label.right_bracket' => '"',
	'faq.button.show_inquiry' => 'To Inquiry Form',
	'faq.text.show_inquiry' => 'If cannot find your question, please contact us here.',

	// SURVEY INQUIRY 共通
	'survey.inquiry.common.edit' => 'Edit questions',
	'survey.inquiry.common.list' => 'Questions',
	'survey.inquiry.common.label.required' => 'Required',
	'survey.inquiry.common.label.item.add' => 'Add a question',
	'survey.inquiry.common.delete.item.title' => 'Do you want to delete the question?',
	'survey.inquiry.common.delete.item' => 'Delete the question',
	// 項目内
	'survey.inquiry.common.item.placeholder.title' => 'Please enter the question title',
	'survey.inquiry.common.item.placeholder.title.order' => 'Please enter a category (e.g., Drinks)',
	'survey.inquiry.common.item.to_edit_HTML' => 'Edit title',
	'survey.inquiry.common.item.edit_HTML.title' => 'Edit title',
	'survey.inquiry.common.item.image.add' => 'Add image',
	'survey.inquiry.common.item.image.add.title' => 'Add image',
	'survey.inquiry.common.item.image.confirm.display_none' => 'Do you want to hide "Add image"?',
	'survey.inquiry.common.item.image.confirm.delete' => 'Do you want to delete the image?',
	'survey.inquiry.common.item.image.confirm.delete.title' => 'The image saved will be deleted.',
	'survey.inquiry.common.item.title.new_page' => 'A new page will start from this question.',
	'survey.inquiry.common.item.select.max' => 'Maximum number of choices',
	'survey.inquiry.common.item.select.min' => 'Minimum number of choices',
	'survey.inquiry.common.item.add.option' => 'Add choices',
	'survey.inquiry.common.item.option.or' => 'or',
	'survey.inquiry.common.item.add.other' => 'add Other',
	'survey.inquiry.common.item.option.placeholder' => 'Choices {num}',

	// 形式
	'survey.inquiry.common.opt' => 'Single choice',
	'survey.inquiry.common.ord' => 'Tabinaka order',
	'survey.inquiry.common.chk' => 'Multiple choice',
	'survey.inquiry.common.sel' => 'Dropdown',
	'survey.inquiry.common.txt' => 'Short answer',
	'survey.inquiry.common.txa' => 'Long answer',
	'survey.inquiry.common.fup' => 'Upload file',
	'survey.inquiry.common.frs' => 'Free space',
	'survey.inquiry.common.mtx' => 'Matrix',
	'survey.inquiry.common.fleq' => 'FAQ',
	'survey.inquiry.common.spl.address.prefecture_city' => 'Prefecture + City',

	// txt, txa
	'survey.inquiry.common.txt.text' => 'Free input',
	'survey.inquiry.common.txt.num' => 'Number',
	'survey.inquiry.common.txt.country' => 'Country/Region',
	'survey.inquiry.common.txt.postcode' => 'Postal code',
	'survey.inquiry.common.txt.prefecture' => 'Prefecture',
	'survey.inquiry.common.txt.city' => 'City',
	'survey.inquiry.common.txt.city.placeholder' => '〇〇 City',
	'survey.inquiry.common.txt.street' => 'Address 1',
	'survey.inquiry.common.txt.room' => 'Name of building/Room number',
	'survey.inquiry.common.txa.min' => 'minimum characters',
	'survey.inquiry.common.txa.max' => 'maximum characters',
	'survey.inquiry.common.txt.reconfirm.mail' => 'Confirm email address',
	'survey.inquiry.common.txt.mail.sendmail' => 'Send this mail',
	'survey.inquiry.common.txa.placeholder' => 'Long answer (within 2000 characters)',
	'survey.inquiry.common.txt.txa.emotion_analytics' => 'Emotion Analysis',
	'survey.inquiry.common.txt.txa.privacy_masking' => 'Masking answers as personal information',

	// マトリクス
	'survey.inquiry.common.mtx.title.column' => 'Title (row)',
	'survey.inquiry.common.mtx.option.column' => 'Choices (column)',
	'survey.inquiry.common.mtx.title.num' => 'Title {num}',
	'survey.inquiry.common.mtx.add.title' => 'Add a title',
	'survey.inquiry.common.mtx.select.num' => '{num}',

	// セクション
	'survey.inquiry.common.label.section' => 'Section',
	'survey.inquiry.common.section.delete' => 'Delete section',
	'survey.inquiry.common.section.delete.confirm' => 'Do you want to delete the section?',
	'survey.inquiry.common.section.fold_up' => 'Fold the section',
	'survey.inquiry.common.label.section.add' => 'Add a section',
	'survey.inquiry.common.label.coupon.setting' => 'Coupon setting',
	'survey.inquiry.common.coupon.add' => 'Add coupon',
	'survey.inquiry.common.coupon.delete.all' => 'Delete all coupons',

	// 分岐条件
	'survey.inquiry.common.branch.set' => 'Set branches',
	'survey.inquiry.common.branch.set_btn' => 'Confirm',
	'survey.inquiry.common.branch.select.title' => 'Please choose a queston',
	'survey.inquiry.common.branch.select.answer' => 'Please choose an answer',
	'survey.inquiry.common.branch.add.and' => 'Please add a branch (AND)',
	'survey.inquiry.common.branch.delete' => 'Do you want to delete the branch?',
	'survey.inquiry.common.branch.add' => 'Add a branch',
	'survey.inquiry.common.branch.label.or' => 'or',
	'survey.inquiry.common.branch.destination' => 'Branch destination: ',
	'survey.inquiry.common.action.destination' => 'Add a destination based on the answers below',

	'survey.inquiry.common.info.member_mail_template' => 'Please check the "Send this email" on the email section of the short answer on the inquiry form editing page.</br> "Do not send" cannot be selected if there is a check on the section.',

	// inquiry
	'inquiry.common.make.entry.label' => 'Create label',
	'inquiry.common.label.name' => 'Name',
	'inquiry.common.label.name.full' => 'Full name',
	'inquiry.common.label.name.separate' => 'Separate first and last name',
	'inquiry.common.label.payment' => 'Payment',
	'inquiry.common.calendar' => 'Calendar selection',
	'inquiry.common.maximum' => 'Reservation',
	'inquiry.common.item.to_edit_HTML.title.desc' => 'Edit title description',
	'inquiry.common.item.edit_HTML.title.desc' => 'Title description',
	'inquiry.common.item.placeholder.title.desc' => 'Please enter a title description',
	'inquiry.common.item.to_edit_HTML.product.desc' => 'Edit product description',
	'inquiry.common.item.edit_HTML.product.desc' => 'Product description',
	'inquiry.common.maximum.all' => 'All period',
	'inquiry.common.maximum.date' => 'Date period',
	'inquiry.common.label.detailed.settings' => 'Settings',
	'inquiry.common.label.detailed.date_settings' => 'Calendar settings',
	'inquiry.common.label.detailed.time_settings' => 'Period settings',
	'inquiry.common.txt.range' => 'Duration',
	'inquiry.common.txt.time' => 'Time',
	'inquiry.common.txt.coupon' => 'Coupon code',
	'inquiry.common.txt.max' => 'Max',
	'inquiry.common.txt.min' => 'Min',
	'inquiry.common.txt.every_minute' => 'every minute',
	'inquiry.common.txt.add.coupon' => 'Add a coupon',
	'inquiry.common.txt.check.unique' => 'One RSV with same input within',
	'inquiry.common.txt.check.unique.suffix' => 'allowed',
	'inquiry.common.txt.date.setting' => 'Date setting',
	'inquiry.common.txt.time.setting' => 'Time setting',
	'inquiry.common.txt.sample.placeholder' => '(Entry field) Please enter sample text',
	'inquiry.common.txt.timing' => 'Tense',
	'inquiry.common.txt.timing.description' => 'Future:Future dates<br/>Past:Previous dates<br/>Whole period:Show all',
	'inquiry.common.txt.future' => 'Future',
	'inquiry.common.txt.past' => 'Past',
	'inquiry.common.txt.all_period' => 'Whole period',
	'inquiry.common.txa.faq' => 'Search FAQ by input contents',
	'inquiry.common.fleq.placeholder.sample' => 'Please enter a sample answer',
	'inquiry.common.maximum.select' => 'Please select an inventory',
	'inquiry.common.maximum.remove' => 'Remove an inventory',
	'inquiry.common.maximum.control' => 'Inventory control',
	'inquiry.common.add.notify_on_selection' => 'Notify with email on selected choices',
	'inquiry.common.placeholder.notification_conditions_title' => 'Title of notification conditions',

	'inquiry.common.add.amount_quantity' => 'Set the amount and quantity',
	'inquiry.common.add.item.desc' => 'Add item description',
	'inquiry.common.label.order.limit' => 'Order limit',
	'inquiry.common.label.order.tax.included' => 'Tax included',
	'inquiry.common.label.order.tax.service.included' => 'Tax and service included',
	'inquiry.common.label.order.tax.service.none' => 'Tax free',
	'inquiry.common.label.product.code' => 'Product code',
	'inquiry.common.txt.mail.sendmail' => 'Send this mail',

	'inquiry.common.txt.start.date' => 'Start date',
	'inquiry.common.txt.date.selection' => '(Specify date)',
	'inquiry.common.txt.end.date' => 'End date',

	'inquiry.common.label.specify.day' => 'Specify day of the week',
	'inquiry.common.label.mon' => 'Mon',
	'inquiry.common.label.tue' => 'Tue',
	'inquiry.common.label.wed' => 'Wed',
	'inquiry.common.label.thurs' => 'Thurs',
	'inquiry.common.label.fri' => 'Fri',
	'inquiry.common.label.sat' => 'Sat',
	'inquiry.common.label.sun' => 'Sun',
	'inquiry.common.label.holiday' => 'Holiday',
	'inquiry.common.label.including' => 'Including',
	'inquiry.common.label.excluding' => 'Excluding',
	'inquiry.common.label.holding.period' => 'Holding period',
	'inquiry.common.label.days' => 'days',
	'inquiry.common.label.day' => 'days',
	'inquiry.common.label.months' => 'months',
	'inquiry.common.label.excluding.day' => 'Excluding day',
	'inquiry.common.label.term' => 'Term',

	'inquiry.common.label.maximum.start' => 'Reservation start',
	'inquiry.common.label.maximum.end' => 'Reservation end',
	'inquiry.common.label.maximum.reservable_period' => 'Reservable period',
	'inquiry.common.label.maximum.term.start' => 'Reservation period start',
	'inquiry.common.label.maximum.term.end' => 'Reservation period end',
	'inquiry.common.label.maximum.quantity' => 'Reservation quantity',
	'inquiry.common.label.maximum.limit' => 'Reservation limit',
	'inquiry.common.label.maximum.limit_min' => 'Minimum reservation limit',
	'inquiry.common.label.maximum.display.format' => 'Display format',
	'inquiry.common.label.maximum.discount' => 'Discount settings',
	'inquiry.common.label.maximum.extension' => 'Extended settings',
	'inquiry.common.label.maximum.switch_time' => 'Switch time',
	'inquiry.common.label.maximum.range'=>'Range settings',
	'inquiry.common.label.maximum.ui_version'=>'New UI (Option)',
	'inquiry.common.label.maximum.category_single_select'=>'Category single-select',
	'inquiry.common.label.maximum.range_setting_enable'=>'Enable',
	'inquiry.common.label.maximum.reserve_setting'=>'Reservation Settings',
	'inquiry.common.label.maximum.min_reserve_unit'=>'Minimum Reservation Unit',
	'inquiry.common.label.maximum.max_reserve_unit'=>'Maximum Reservation Unit',
	'inquiry.common.label.maximum.reserve_span'=>'Reservation Interval',
	'inquiry.common.label.maximum.reserve_span_before'=>'Reservation Interval(Before)',
	'inquiry.common.label.maximum.reserve_span_after'=>'Reservation Interval(After)',
	'inquiry.common.label.maximum.reserve_time'=>'Reservation Time',
	'inquiry.common.label.maximum.from'=>'〜',
	'inquiry.common.label.maximum.to'=>'',
	'inquiry.common.label.maximum.out_service_reserve' => 'Reservations/returns possible outside maintenance hours',

	'inquiry.common.label.time.use' => 'Time of use',
	'inquiry.common.label.excluded.time.zone' => 'Excluded time zone',
	'inquiry.common.label.time.schedule' => 'Time schedule',
	'inquiry.common.label.time.every.minute' => 'Every {num} minute',

	'inquiry.common.label.maximum.label' => 'Unit',
	'inquiry.common.label.maximum.cross_day' => 'Cross day',
	'inquiry.common.label.maximum.num_label_select' => 'Display quantity',
	'inquiry.common.label.maximum.consecutive' => 'Consecutive slots',
	'inquiry.common.label.maximum.type' => 'Stock consumption',
	'inquiry.common.label.maximum.default' => 'Default',
	'inquiry.common.label.select.destination' => 'Select Destination',
	'inquiry.common.label.select.add.notification_condition' => 'Add a condition for notification',
	'inquiry.common.label.action.setting.title' => 'Set extended actions for choices',
	'inquiry.common.label.action.setting.notify.title' => 'Notify with email on selected choices',
	'inquiry.common.label.action.notification_conditions' => 'Notification conditions',
	'inquiry.common.label.action.select' => 'Please select an option',
	'inquiry.common.label.branch.equal' => 'equal to',
	'inquiry.common.label.branch.not_equal' => 'unequal to',
	'inquiry.common.label.branch.include' => 'include',
	'inquiry.common.label.branch.not_include' => 'do not include',
	'inquiry.common.label.branch.greater' => 'Larger than',
	'inquiry.common.label.branch.smaller' => 'Smaller than',
	'inquiry.common.label.branch.equal_or_greater' => 'or more',
	'inquiry.common.label.branch.equal_or_smaller' => 'or less',

	'inquiry.common.label.up.to' => 'up to',
	'inquiry.common.label.days.before' => 'days before',
	'inquiry.common.label.until' => 'until',
	'inquiry.common.label.detailed.settings.maximum' => 'Detailed settings for reservation',
	'inquiry.common.label.maximum.days.after' => 'days after',
	'inquiry.common.label.maximum.days.later' => 'days after',
	'inquiry.common.label.maximum.days.before' => 'days before',
	'inquiry.common.label.until.days' => 'until',
	'inquiry.common.label.until.hours' => 'hours',
	'inquiry.common.label.until.minutes' => 'minutes before',
	'inquiry.common.label.all_versions' => 'Show all versions',

	'survey.html.title' => 'talkappi Survey',
	'survey.index.title' => 'talkappi Survey',
	'survey.index.title.name' => 'Survey name',
	'survey.index.label.content' => 'Introduction',
	'survey.index.label.content.name' => 'Survey summary',
	'survey.index.label.period' => 'Research period',
	'survey.index.label.count' => 'Number of questions',
	'survey.index.label.count.content' => 'Max. {count} questions',
	'survey.index.label.duration' => 'Survey length',
	'survey.index.label.duration.content' => 'about {duration} min',
	'survey.index.label.present' => 'Gift',
	'survey.index.label.remark' => 'This research is held by the operating company. The answers will be analyzed without identifying the individual.',
	'survey.index.label.expired' => 'This research has ended',
	'survey.index.label.limit' => 'New applications are no longer taken as the number of responses have reached the limit.',
	'survey.index.button.answer' => 'Answer',
	'survey.input.label.must' => '*Required',
	'survey.input.button.prev' => 'Back',
	'survey.input.button.next' => 'Next',
	'survey.input.button.send' => 'Send',
	'survey.input.label.required.not.input' => 'Please check that all answers are filled in.',
	'survey.input.label.required.not.input.eachquestion' => 'This is a required field.',
	'survey.input.text.length' => 'Must be between {min_length} ~ {max_length} letters.',
	'survey.input.text.max_length' => 'Must be under {max_length} characters.',
	'survey.input.text.min_length' => 'Must be at least {min_length} characters.',
	'survey.input.text.tel' => '080-1234-5678(Hyphen (-) can be omitted)',
	'survey.input.text.address' => '123-4567(Hyphen (-) can be omitted)',
	'survey.input.text.num' => 'Please enter a number',
	'survey.input.text.num.dash' => 'Please enter a number(Hyphen (-) can be omitted)',
	'survey.input.text.email' => 'Please enter your email address',
	'survey.input.text.email.confirm' => '(Re-enter)',
	'survey.input.text.email.confirm.error' => 'The email address does not match',
	'survey.input.text.mtx.error' => 'Please answer each row',
	'survey.input.text.mtx.chk.message' => 'Please choose {max_min} from each row',
	'survey.input.text.chk.message' => 'Please choose {max_min}',
	'survey.input.text.chk.error' => 'Please check the choices.',
	'survey.input.text.mtx.chk.min' => '{min} minimum',
	'survey.input.text.mtx.chk.max' => '{max} maximum',
	'survey.input.text.mtx.chk.num' => '{num}',
	'survey.input.text.date' => 'Please enter information following the example.',
	'survey.input.text.fup' => 'Select file',
	'survey.input.text.sel' => 'Please select',
	'survey.complete.title' => 'Survey complete',
	'survey.complete.label.thanks' => "Thank you for taking your time<br> in answering the survey",
	'survey.complete.label.unusable' => "This coupon is out of date. <br>Valid dates:{start_date}~{end_date}<br>Please send the coupon to your email address by clicking the 'Save/send coupon' button.",
	'survey.complete.button.coupon' => 'See coupons',
	'survey.complete.button.close' => 'Back to chat',
	'survey.complete.button.closewindow' => 'Close',
	'survey.coupon.label.period1' => '{days} days left',
	'survey.coupon.label.period2' => 'Expiration date: {date}',
	'survey.coupon.label.period3' => 'Stating date: {date}',
	'survey.coupon.label.period4' => 'Expiration date: {date} days after the coupon is issued<br>*Issued when the coupon is opened during the valid dates.',
	'survey.coupon.label.period5' => 'Valid dates: {date} days from today',
	'survey.coupon.label.present' => 'Gift coupon',
	'survey.coupon.label.remark' => 'The expiration date and time of the coupons are set at UTC+09:00.',
	'survey.coupon.button.use' => 'Use the coupon',
	'survey.coupon.dialog.title' => 'Would you like to use this coupon?',
	'survey.coupon.dialog.content' => 'The usage status will change once you click ”Yes”. Please show the screen to the staff before your payment.',
	'survey.coupon.label.expired' => 'This coupon has expired',
	'survey.coupon.label.notstart' => 'This coupon cannot be used yet. <br> Please try again after once the dates have become valid',
	'survey.coupon.label.used' => 'This coupons has been used',
	'survey.coupon.label.maxused' => 'This coupon has reached its use limit',
	'survey.coupon.label.canotuse' => 'You do not have this coupon',
	'survey.coupon.label.issue' => 'Gifts are no longer available as the number of coupons have reached the limit.',
	'survey.coupon.expire.after.issue' => '{date} days after issue',
	'survey.common.label.required' => 'Required',
	'survey.common.label.input' => 'Please enter your answer',
	'survey.common.label.other' => 'Other',
	'survey.common.label.other.input' => 'Please enter your answer',
	'survey.common.label.other.input.required' => '(Required)',
	'survey.common.label.other.input.optional' => '',
	'survey.branch.select.equal' => 'equal to',
	'survey.branch.select.not_equal' => 'not equal to',
	'survey.branch.select.include' => 'include',
	'survey.branch.select.not_include' => 'do not include',
	'survey.branch.select.greater' => 'Larger than',
	'survey.branch.select.smaller' => 'Smaller than',
	'survey.branch.select.equal_or_greater' => 'or more',
	'survey.branch.select.equal_or_smaller' => 'or less',

	'inquiry.html.title' => 'talkappi Inquiry',
	'inquiry.index.title' => 'talkappi Inquiry',
	'inquiry.index.label.content' => 'Introduction',
	'inquiry.index.label.period' => 'Service period',
	'inquiry.index.label.count' => 'Number of questions',
	'inquiry.index.label.count.content' => 'Max. {count} questions',
	'inquiry.index.label.duration' => 'Inquiry length',
	'inquiry.index.label.duration.content' => 'about {duration} min',
	'inquiry.index.label.present' => 'Gift',
	'inquiry.index.label.remark' => 'This research is held by the operating company. The answers will be analyzed without identifying the individual.',
	'inquiry.index.label.invalid.title' => 'Service Restriction',
	'inquiry.index.label.invalid.description' => 'We are sorry.<br>The form cannot be processed <br>as this service is currently unavailable.',
	'inquiry.index.label.versionup.description' => 'Unfortunately, this form has expired.<br>It will switch to the new form after 10 seconds.<br>Please enter the details again after the new form is displayed. We apologize for the inconvenience.',
	'inquiry.index.label.inquiry_answer_limit.description' => 'Entry has closed as the number of people has reached the limit.',
	'inquiry.index.label.answer_limit.description' => 'Your entry has been submitted already.<br>Thank you for your cooperation.',
	'inquiry.index.button.answer' => 'Answer',
	'inquiry.input.label.must' => '*Required',
	'inquiry.input.button.send' => 'Send',
	'inquiry.input.button.prev' => 'Edit',
	'inquiry.input.button.pay' => 'Pay',
	'inquiry.input.button.back' => 'Back',
	'inquiry.input.button.confirm' => 'Confirm information',
	'inquiry.input.button.modify' => 'Change reservation',
	'inquiry.input.button.cancel' => 'Cancel reservation',
	'inquiry.modify.error' => 'This reservation is not eligible for online changes. Please contact the facility.',
	'inquiry.cancel.error.check' => 'The cancellation period has ended. Please contact the facility directly.',
	'inquiry.cancel.error.canceled' => 'This reservation has been canceled already.',
	'inquiry.cancel.complete' => 'The reservation has been canceled.',
	'inquiry.cancel.complete.desc' => 'The details have been sent to your registered email address.<br>Thank you for using our service.',
	'inquiry.input.label.required.not.input' => 'Please check that all answers are filled in.',
	'inquiry.input.label.required.not.input.eachquestion' => 'This is a required field.',
	'inquiry.input.transition.error' => 'When using the automatic translation function of the browser, some contents of the form may not be displayed correctly. Please turn off the automatic translation function.',
	'inquiry.input.message.no-pay-reserve' => 'Your reservation payment is incomplete. Please complete your payment on the payment page. If you wish to cancel your reservation and make a new one, please choose “Make a new reservation”',
	'inquiry.input.button.cancel-no-pay-reserve' => 'Make a new reservation',
	'inquiry.input.button.continue-no-pay-reserve' => 'Continue reservation',
	'inquiry.input.button.close' => 'Close',
	'inquiry.input.text.length' => 'Must be between {min_length} ~ {max_length} letters.',
	'inquiry.input.text.max_length' => 'Must be under {max_length} characters.',
	'inquiry.input.text.min_length' => 'Must be at least {min_length} characters.',
	'inquiry.input.text.num.minmax' => 'Please enter a number between {min}~{max}.',
	'inquiry.input.text.num.max' => 'Please enter a number under {max}.',
	'inquiry.input.text.num.min' => 'Please enter a number over {min}.',
	'inquiry.input.text.tel' => '*****-123-4567',
	'inquiry.input.text.address' => '123-4567(Hyphen (-) can be omitted)',
	'inquiry.input.text.num' => 'Please enter a number',
	'inquiry.input.text.num.dash' => 'Please enter a number(Hyphen (-) can be omitted)',
	'inquiry.input.text.email' => 'Please enter your email address',
	'inquiry.input.text.email.confirm' => '(Re-enter)',
	'inquiry.input.text.email.confirm.error' => 'The email address does not match',
	'inquiry.input.text.clock' => 'Please enter the time correctly.',
	'inquiry.input.text.date' => 'Please enter information following the example.',
	'inquiry.input.text.fup' => 'Select file',
	'inquiry.input.text.sel' => 'Please select',
	'inquiry.input.text.other.confirm' => 'Other',
	'inquiry.input.text.txt.date' => 'Date',
	'inquiry.input.text.txt.time' => 'Time zone',
	'inquiry.input.spl.address.roomNo' => '(optional)',
	'inquiry.input.spl.address.country.label' => 'Country/Region',
	'inquiry.input.spl.address.postcode.label' => 'ZIP/Postal code',
	'inquiry.input.spl.address.prefecture.label' => 'Prefecture',
	'inquiry.input.spl.address.city.label' => 'City',
	'inquiry.input.spl.address.street.label' => 'Street address',
	'inquiry.input.spl.address.room.label' => 'Building, floor, room number',
	'inquiry.input.spl.name.full' => 'Full name',
	'inquiry.input.spl.name.first' => 'First name',
	'inquiry.input.spl.name.last' => 'Last name',
	'inquiry.input.price.tax' => '(Tax included)',
	'inquiry.input.price.tax-service' => '(Tax and service included)',
	'inquiry.complete.title' => 'Inquiry complete',
	'inquiry.complete.label.thanks' => "Thank you for taking your time<br> in answering the Inquiry",
	'inquiry.complete.label.period' => "Expiration date: Until {date}",
	'inquiry.complete.button.coupon' => 'See gift coupons',
	'inquiry.complete.button.close' => 'Back to chat',
	'inquiry.complete.button.closewindow' => 'Close',
	'inquiry.complete.error.overtime_title' => 'Reservaton error',
	'inquiry.complete.error.overtime' => 'The reservation has been canceled as the payment was not completed within the given time.',
	'inquiry.complete.button.input' => 'Back to Top',
	'inquiry.complete.common.title' => 'Acceptance completed',
	'inquiry.complete.common.description' => 'Thank you for your inquiry. We will usually reply within 3 business days.',
	'inquiry.coupon.label.period1' => '{days} days left',
	'inquiry.coupon.label.period2' => 'Usable until {date}',
	'inquiry.coupon.label.present' => 'Gift coupon',
	'inquiry.coupon.label.remark' => 'The expiration date and time of the coupons are set at UTC+09:00.',
	'inquiry.coupon.button.use' => 'Use the coupon',
	'inquiry.coupon.dialog.title' => 'Would you like to use this coupon?',
	'inquiry.coupon.dialog.content' => 'The usage status will change once you click ”Yes”. Please show the screen to the staff before your payment.',
	'inquiry.coupon.label.expired' => 'This coupon has expired',
	'inquiry.coupon.label.notstart' => 'Unused coupons',
	'inquiry.coupon.label.used' => 'This coupons has been used',
	'inquiry.coupon.label.maxused' => 'This coupon has reached its use limit',
	'inquiry.coupon.label.canotuse' => 'You do not have this coupon',
	'inquiry.common.label.required' => 'Required',
	'inquiry.common.label.input' => 'Please enter your answer',
	'inquiry.common.label.other' => 'Other',
	'inquiry.common.label.other.input' => 'Please enter your answer',
	'inquiry.common.label.other.input.error' => 'Please enter the details',
	'inquiry.common.label.limit.num.error' => 'The number of maximum reservations {unit} has been exceeded. Please select the quantity again.',
	'inquiry.common.label.limit.num.min.error' => 'ご予約可能な予約数{unit}を下回っているため、数量を選択し直してください。',
	'inquiry.common.label.reserve.alone.error' => 'Your selected type cannot be reserved independently.',
	'inquiry.common.label.other.input.required' => '(Required)',
	'inquiry.common.label.other.input.optional' => '',
	'inquiry.common.tab.step1' => 'Customer information',
	'inquiry.common.tab.step2' => 'Confirm',
	'inquiry.common.tab.step2.payment' => 'Confirm & pay',
	'inquiry.common.tab.step3' => 'Complete',
	'inquiry.common.complete.title' => 'Your request has been sent',
	'inquiry.common.complete.urlbtn' => 'Return to website',
	'inquiry.login.title' => 'Restricted page',
	'inquiry.login.description' => 'Enter password',
	'inquiry.login.error' => 'Please enter the correct password.',
	'inquiry.login.placeholder' => 'Password',
	'inquiry.login.button' => 'Sign in',
	'inquiry.faq.button' => 'FAQ',
	'inquiry.label.num' => 'Quantity',
	'inquiry.label.close' => 'Close',
	'inquiry.label.expand' => 'Expand',
	'inquiry.button.coupon_apply' => 'Apply',

	'coupon.dialog.remark' => 'If you have not received the email, please check your spam folder or security settings and try again.',
	'coupon.dialog.title' => 'Save/send coupon',
	'coupon.dialog.content.link' => 'Copy and share the coupon link',
	'coupon.dialog.copy' => 'Copy',
	'coupon.dialog.content.send' => 'Send coupon by email',
	'coupon.dialog.mail' => 'Enter email address',
	'coupon.dialog.button' => 'Send',
	'coupon.dialog.mail.success' => 'The email has been sent.',
	'coupon.dialog.mail.fail' => 'Please enter a valid email address',
	'coupon.button.yes' => 'Use coupon',
	'coupon.button.no' => 'Not now',
	'coupon.dialog.use.store' => 'Place of use',
	'coupon.dialog.use.title' => 'この画面を<br>お店の方に提示してください。',
	'coupon.dialog.use.staff' => '＜店舗スタッフ様＞',
	'coupon.dialog.use.staff.remark' => '「使用する」を押すと、クーポンが使用済みの
		ステータスになってしまい、元に戻せません。
		店舗スタッフ様が「使用する」ボタンを押して、
		クーポン使用となります。',
		
	'coupon.facility.choose' => 'Select a facility',
	'coupon.dialog.copy.success' => 'The link has been copied.',
	'coupon.label.all.maxused' => 'このクーポンは利用上限枚数に達しているため、利用できません。',

	'common.error.front.title' => 'An error occurred.',
	'common.error.front.sub_title' => 'Please try again later.',
	'common.error.http.exception.403' => 'Access is forbidden.',
	'common.error.http.exception.500' => 'Sorry, an error has occurred.',
	'common.error.http.exception.404' => 'The URL does not exist.',

	'admin.common.modal.delete.confirm' => 'This action cannot be undone.',
	'admin.common.modal.image.flie.category' => 'within 2MB, png or jpg files',
	'admin.common.modal.image.flie.upload' => 'Click here to upload',

	'admin.common.button.search' => 'Search',
	'admin.common.button.reset' => 'Reset',
	'admin.common.button.publish' => 'Publish',
	'admin.common.button.new' => 'New',
	'admin.common.button.edit' => 'Edit',
	'admin.common.button.edit_action' => 'Edit',
	'admin.common.button.edit_type' => 'Edit type',
	'admin.common.button.select' => 'Select',
	'admin.common.button.save' => 'Save',
	'admin.common.button.clone' => 'Clone',
	'admin.common.button.copy_public_url' => 'Copy public URL',
	'admin.common.button.preview' => 'Preview',
	'admin.common.button.modify' => 'Modify',
	'admin.common.button.delete.full' => 'Delete',
	'admin.common.button.customize' => 'Customize',
	'admin.common.button.clear' => 'Clear',
	'admin.common.button.delete' => 'Delete',
	'admin.common.button.invalid' => 'Invalid',
	'admin.common.button.delete_action' => 'Delete',
	'admin.common.button.back' => 'Back',
	'admin.common.button.return_to_list' => 'Return to list',
	'admin.common.button.showlog' => 'Historical view',
	'admin.common.button.deletelog' => 'Delete history',
	'admin.common.button.confirm' => 'Confirm',
	'admin.common.button.cancel' => 'Cancel',
	'admin.common.button.copy_url' => 'Copy URL',
	'admin.common.button.copy' => 'Copy',
	'admin.common.button.details' => 'Advanced settings',
	'admin.common.button.end' => 'End',
	'admin.common.button.send' => 'Send',
	'admin.common.button.all_classifications' => 'All classifications',
	'admin.common.button.uncategorized' => 'Uncategorized',
	'admin.common.button.csv_export' => 'CSV export',
	'admin.common.button.csv_export_internal' => 'CSV export※',
	'admin.common.button.csv_print' => 'CSV print',
	'admin.common.button.csv_export_inadequacy' => 'CSV export of inadequacy',
	'admin.common.button.close' => 'Close',
	'admin.common.button.create_new' => 'Create New',
	'admin.common.button.import' => 'Import',
	'admin.common.button.add' => 'Add',
	'admin.common.button.return_to_default' => 'Return to default',
	'admin.common.button.verify' => 'Preview',
	'admin.common.button.go_to_form' => 'Go to form',
	'admin.common.button.delete_ja' => 'Delete Japanese',
	'admin.common.button.delete_js' => 'Delete Simple Japanese',
	'admin.common.button.delete_en' => 'Delete English',
	'admin.common.button.delete_cn' => 'Delete Simplified Chinese',
	'admin.common.button.delete_tw' => 'Delete Traditional Chinese',
	'admin.common.button.delete_kr' => 'Delete Korean',
	'admin.common.button.delete_th' => 'Delete Thai',
	'admin.common.button.delete_vi' => 'Delete Vietnamese',
	'admin.common.button.delete_ne' => 'Delete Nepali',
	'admin.common.button.delete_it' => 'Delete Italian',
	'admin.common.button.delete_es' => 'Delete Spanish',
	'admin.common.button.delete_de' => 'Delete German',
	'admin.common.button.delete_fr' => 'Delete French',
	'admin.common.button.delete_pt' => 'Delete Portuguese',
	'admin.common.button.delete_ru' => 'Delete Russian',
	'admin.common.button.delete_id' => 'Delete Indonesian',
	'admin.common.button.delete_other' => 'Delete this language',
	'admin.common.button.delete_all' => 'Delete all language',
	'admin.common.button.link_all' => 'Link All',
	'admin.common.button.unlink_all' => 'Unlink All',

	'admin.common.label.lang' => 'Language',
	'admin.common.label.markup_lang' => 'Markup Language',
	'admin.common.label.all_lang' => 'All Languages',
	'admin.common.label.select_all_lang' => 'All',
	'admin.common.label.all_type' => 'All Types',
	'admin.common.label.conversation_date' => 'Conversation date',
	'admin.common.label.sns' => 'Channel',
	'admin.common.label.sns_all' => 'All Channels',
	'admin.common.label.all' => 'All',
	'admin.common.label.sns_and_name' => 'Channel • Name',
	'admin.common.label.member_id' => 'Member ID',
	'admin.common.label.user_id' => 'User ID',
	'admin.common.label.request_time' => 'Answer time',
	'admin.common.label.response_status' => 'Response status',
	'admin.common.label.service' => 'Service',
	'admin.common.label.person_in_charge' => 'Person in charge',
	'admin.common.label.person_in_charge.all' => 'All Person in charge',
	'admin.common.label.editor' => 'Editor',
	'admin.common.label.date' => 'Date',
	'admin.common.label.time' => 'Time',
	'admin.common.label.period' => 'Period',
	'admin.common.label.list' => 'List',
	'admin.common.label.list_display' => 'List view',
	'admin.common.label.list_link' => 'List link',
	'admin.common.label.published' => 'Published',
	'admin.common.label.new_add' => 'New regist',
	'admin.common.label.add' => 'Regist',
	'admin.common.label.code' => 'Code',
	'admin.common.label.name' => 'Name',
	'admin.common.label.username' => 'User name',
	'admin.common.label.details' => 'Details',
	'admin.common.label.description' => 'Description',
	'admin.common.label.common_description' => 'Common description',
	'admin.common.label.common_description_placeholder' => 'Common description that will be displayed on each channel.',
	'admin.common.label.extra_description' => 'Extra description',
	'admin.common.label.extra_description_placeholder' => 'Extra description will only be displayed in VERY and TV Information. And it will not appear in CHATBOT.',
	'admin.common.label.photo' => 'Photo',
	'admin.common.label.photo_action' => 'Photo action',
	'admin.common.label.action' => 'Action',
	'admin.common.label.click_counts' => 'Click counting',
	'admin.common.label.applies_to_all_languages' => 'Applies to all languages',
	'admin.common.label.multilingual_translation' => 'Multilingual translation',
	'admin.common.label.multilingual_display' => 'Multilingual display',
	'admin.common.label.multilingual_data'=>'多言語登録',
	'admin.common.label.immediate_photo_reflection' => 'Immediate photo reflection',
	'admin.common.label.last_update' => 'Last update',
	'admin.common.label.revision_request' => 'Revision request',
	'admin.common.label.button' => 'Button ',
	'admin.common.label.button_name_standard' => 'Button name (standard)',
	'admin.common.label.phone_number' => 'Phone number',
	'admin.common.label.address' => 'Address',
	'admin.common.label.map_url' => 'Map url',
	'admin.common.label.mobile' => 'Mobile',
	'admin.common.label.class' => 'Class',
	'admin.common.label.type' => 'Type',
	'admin.common.label.classification' => 'Category',
	'admin.common.label.provider' => 'Provider',
	'admin.common.label.classification.add' => 'Add category',
	'admin.common.label.industry' => 'Industry',
	'admin.common.label.area' => 'Area',
	'admin.common.label.operating_conditions' => 'Operating conditions',
	'admin.common.label.congestion_situation' => 'Congestion situation',
	'admin.common.label.display_order' => 'Display order',
	'admin.common.label.display_information' => 'Display information',
	'admin.common.label.keyword' => 'Keyword',
	'admin.common.label.content' => 'Content',
	'admin.common.label.content_search' => 'Search content',
	'admin.common.label.request_input' => 'Request input',
	'admin.common.label.untitled' => 'Untitled',
	'admin.common.label.untitled_content' => 'Untitled',
	'admin.common.label.request' => 'Request',
	'admin.common.label.request_detail' => 'Request detail',
	'admin.common.label.original_text' => 'Original text',
	'admin.common.label.historical_view' => 'Historical view',
	'admin.common.label.title' => 'Title',
	'admin.common.label.registration_date' => 'Registration date',
	'admin.common.label.registered_person' => 'Registered person',
	'admin.common.label.deadline' => 'Deadline',
	'admin.common.label.cancellation_request' => 'cancellation request',
	'admin.common.label.mark_as_supported' => 'mark as supported',
	'admin.common.label.checked' => 'checked',
	'admin.common.label.operation' => 'operation',
	'admin.common.label.file' => 'file',
	'admin.common.label.upload' => 'Upload',
	'admin.common.label.upload_file' => 'upload new files',
	'admin.common.label.allowed' => 'Allowed',
	'admin.common.label.not_allowed' => 'Not allowed',
	'admin.common.label.display' => 'Display',
	'admin.common.label.undisplay' => 'Undisplay',
	'admin.common.label.impressions' => 'Impressions',
	'admin.common.label.name.human' => 'Name',
	'admin.common.label.new_created' => 'New Created',
	'admin.common.label.uncustomized' => 'Uncustomized',
	'admin.common.label.customized' => 'Customized',
	'admin.common.label.chat' => 'Web chat',
	'admin.common.label.mail' => 'Mail',
	'admin.common.label.translate' => 'Translate',
	'admin.common.label.all_registration_status' => 'All registration status',
	'admin.common.label.all_registration_types' => 'All registration types',
	'admin.common.label.registered' => 'Registered',
	'admin.common.label.unregistered' => 'Unregistered',
	'admin.common.label.required_registration' => 'Required registration',
	'admin.common.label.optional_registration' => 'Optional registration',
	'admin.common.label.facility' => 'Facility',
	'admin.common.label.faq_number' => 'FAQ No.',
	'admin.common.label.question' => 'Question',
	'admin.common.label.count' => 'Count',
	'admin.common.label.count_click' => 'Click count',
	'admin.common.label.count_click_button' => 'Button click count',
	'admin.common.label.count_search' => 'Search count',
	'admin.common.label.details_of_registration' => 'Details of registration',
	'admin.common.label.edit' => 'Edit',
	'admin.common.label.edit_answer' => 'Edit answer',
	'admin.common.label.editing' => 'Editing',
	'admin.common.label.not_answered' => 'Not answered',
	'admin.common.label.user' => 'User',
	'admin.common.label.user_screen_display_order' => 'User screen display order',
	'admin.common.label.narrowdown' => 'Filter',
	'admin.common.label.status_kana' => 'Status',
	'admin.common.label.chat_example' => 'Check in',
	'admin.common.label.conditions' => 'Conditions',
	'admin.common.label.user_flow' => 'User flow',
	'admin.common.label.all_user_flows' => 'All user flows',
	'admin.common.label.ai_recognition_rate' => 'AI recognition rate',
	'admin.common.label.add_faq' => 'Request to add FAQ',
	'admin.common.label.web_user' => 'Web User',
	'admin.common.label.web_tester' => 'Tester',
	'admin.common.label.text' => 'Text',
	'admin.common.label.image' => 'Image',
	'admin.common.label.video' => 'Video',
	'admin.common.label.pdf' => 'PDF',
	'admin.common.label.font' => 'Font',
	'admin.common.label.advanced_settings' => 'Advanced settings',
	'admin.common.label.carousel' => 'Carousel',
	'admin.common.label.menu' => 'Menu',
	'admin.common.label.template' => 'Templates',
	'admin.common.label.all.template' => 'All Templates',
	'admin.common.label.mail_template' => 'Mail templates',
	'admin.common.label.csv_file' => 'CSV file',
	'admin.common.label.sample' => 'Sample',
	'admin.common.label.reset_to_default' => 'Reset to default',
	'admin.common.label.reset_to_default_all' => 'Reset to default all language',
	'admin.common.label.default_line_menu' => 'Default line menu',
	'admin.common.label.transition_to_parents' => 'Transition to Parents',
	'admin.common.label.listing_period' => 'Listing Period',
	'admin.common.label.listing_type' => 'Listing Type',
	'admin.common.label.related_data' => 'Related data',
	'admin.common.label.channel' => 'Channel',
	'admin.common.label.applying_css_styles' => 'Applying CSS styles',
	'admin.common.label.topics_faq' => 'Topics (FAQ)',
	'admin.common.label.line_rich_menu' => 'LINE Rich Menu',
	'admin.common.label.booking_engine' => 'Booking Engine',
	'admin.common.label.scene' => 'Scene',
	'admin.common.label.unset' => 'Unset',
	'admin.common.label.frequently_used_keywords' => 'Frequently used keywords',
	'admin.common.label.header_title' => 'Header title',
	'admin.common.label.detail_button_title' => 'Detail button title',
	'admin.common.label.hp_customization' => 'HP customization',
	'admin.common.label.preview' => 'Preview',
	'admin.common.label.historical_search' => 'Historical search',
	'admin.common.label.history_csv' => 'History csv',
	'admin.common.label.chat_contents' => 'Chat contents',
	'admin.common.label.user_chat_only' => 'User chat only',
	'admin.common.label.public_setting' => 'Publish settings',
	'admin.common.label.apply_template' => 'Apply template',
	'admin.common.label.source_facility' => 'Source facility',
	'admin.common.label.apply' => 'Apply',
	'admin.common.label.restriction_setting' => 'Restriction settings',
	'admin.common.label.show_published_only' => 'Published only',
	'admin.common.label.public_url' => 'Public URL',
	'admin.common.label.public_url.mail' => 'For Mail',
	'admin.common.label.public_url.mail.hint' => 'Please use for sending e-mail',
	'admin.common.label.obfuscation_url' => 'Obfuscation URL',
	'admin.common.label.qr_redirect_url' => 'QR code redirect URL',
	'admin.common.label.public' => 'Public',
	'admin.common.label.copy' => 'Copy',
	'admin.common.label.chat' => 'Chat',
	'admin.common.label.note' => 'Note',
	'admin.common.label.conversation' => 'Conversation',
	'admin.common.label.number_of_conversations' => 'Number of conversations',
	'admin.common.label.journey' => 'Journey',
	'admin.common.label.spam_user' => 'Spam user',
	'admin.common.label.spam_display' => 'Spam display',
	'admin.common.label.attribute_information' => 'Attribute information',
	'admin.common.label.last_conversation_date' => 'Last conversation date',
	'admin.common.label.change' => 'Change',
	'admin.common.label.reservations' => 'Reservations',
	'admin.common.label.month' => 'Month',
	'admin.common.label.auto_response' => 'Auto response',
	'admin.common.label.display_zero' => '0 items',
	'admin.common.label.total' => 'Total',
	'admin.common.label.ratio' => 'Ratio',
	'admin.common.label.reception_period' => 'Reception period',
	'admin.common.label.reception' => 'Reception',
	'admin.common.label.reception_detail' => 'Details',
	'admin.common.label.response' => 'Response',
	'admin.common.label.completion' => 'Completion',
	'admin.common.label.cancellation' => 'Cancellation',
	'admin.common.label.number_of_repeaters_and_unasked questions' => 'Repeaters and unasked questions',
	'admin.common.label.new_user' => 'New user',
	'admin.common.label.repeaters_unasked_questions' => '[Repeaters/Unasked Questions]',
	'admin.common.label.pc' => 'PC',
	'admin.common.label.unknown' => 'Unknown',
	'admin.common.label.mobile_bracket' => '<br>(Mobile)',
	'admin.common.label.by_country' => 'By country',
	'admin.common.label.number_of_user_utterances' => 'Number of user utterances',
	'admin.common.label.statistics_type' => 'Statistics type',
	'admin.common.label.unsupported' => 'Unsupported',
	'admin.common.label.supported' => 'Supported',
	'admin.common.label.date_of_use' => 'Date of use',
	'admin.common.label.usage_environment' => 'Usage environment',
	'admin.common.label.number_of_results' => 'Number of results',
	'admin.common.label.year' => 'Year',
	'admin.common.label.number_of_users' => 'Number of users',
	'admin.common.label.number_of_user_visits' => 'Number of user visits',
	'admin.common.label.number_of_user_operations' => 'Number of user operations',
	'admin.common.label.division' => 'Division',
	'admin.common.label.sufficient' => 'Sufficient',
	'admin.common.label.insufficient' => 'Insufficient',
	'admin.common.label.reason' => 'Reason',
	'admin.common.label.basic.setting' => 'Basic settings',
	'admin.common.label.service.setting' => 'Subscribed Services',
	'admin.common.label.form' => 'Form',
	'admin.common.label.status' => 'Status',
	'admin.common.label.answer' => 'Answer',
	'admin.common.label.answer_format' => 'Answer format',
	'admin.common.label.menu_answer' => 'Menu answer',
	'admin.common.label.menu_edit' => 'Menu edit',
	'admin.common.label.menu_title' => 'Menu title',
	'admin.common.label.add_menu' => 'Add a menu',
	'admin.common.label.title_edit' => 'Title edit',
	'admin.common.label.add_answer' => 'Add an answer',
	'admin.common.label.body' => 'Body',
	'admin.common.label.japanese_reference' => 'Japanese reference',
	'admin.common.label.message' => 'Message',
	'admin.common.label.add_message' => 'Add a message',
	'admin.common.label.add_action' => 'Add Action',
	'admin.common.label.edit_html' => 'Edit HTML',
	'admin.common.label.faq_data' => 'FAQ Data',
	'admin.common.label.faq_data' => 'What\'s FAQ Data',
	'admin.common.label.save_publish' => 'Save & Publish',
	'admin.common.label.temporary_save' => 'Temporary save',
	'admin.common.label.copy_group_facilities' => 'Copy to group facilities',
	'admin.common.label.del_faq_all_lang_data' => 'Delete all FAQ answers',
	'admin.common.label.button_title' => 'Button title',
	'admin.common.label.add_button' => 'Add button',
	'admin.common.label.result' => 'Result',
	'admin.common.label.demo' => 'Demo',
	'admin.common.label.period_of_execution' => 'Period of execution',
	'admin.common.label.classification_tags' => 'Classification',
	'admin.common.label.number_of_answers' => 'Number of answers',
	'admin.common.label.template' => 'Template',
	'admin.common.label.support.lang' => 'Supported laguage',
	'admin.common.label.display.lang' => 'Displayed language',
	'admin.common.label.f.cd' => 'User flow',
	'admin.common.label.calendar' => 'Calendar',
	'admin.common.label.default.pulldown' => 'Defalut',
	'admin.common.label.edit.old_screen' => 'Edit on old screen',
	'admin.common.label.reflect.all_lang' => 'Apply to all languages<br> Source:',
	'admin.common.label.reflect.not.translate' => 'Apply without translation',
	'admin.common.label.reflect.translate' => 'Use Google Translate and apply',
	'admin.common.label.reflect.native.translate' => 'Request a Native Translation',
	'admin.common.label.not_reflect' => 'Do not apply to other languages',
	'admin.common.label.alert.mail' => 'Notification email',
	'admin.common.label.alert.date' => 'Alert time',
	'admin.common.label.mail.destination' => 'Send to',
	'admin.common.label.setting' => 'Settings',
	'admin.common.label.expect.lang' => 'Preferred Language',
	'admin.common.label.priority' => 'Priority',
	'admin.common.label.priority.high' => 'High',
	'admin.common.label.priority.normal' => 'Normal',
	'admin.common.label.priority.low' => 'Low',
	'admin.common.label.category' => 'Category',
	'admin.common.label.mobile_annotation' => '※The numbers in parentheses represent the total for mobile',
	'admin.common.label.unlink' => 'Unlink',
	'admin.common.label.link' => 'Link',
	'admin.common.label.mail.address' => 'Email address',
	'admin.common.label.url' => 'URL',
	'admin.common.label.all_user' => 'All users',
	'admin.common.label.sns.x' => 'X(Twitter)',
	'admin.common.label.sns.instagram' => 'Instagram',
	'admin.common.label.sns.line' => 'LINE',
	'admin.common.label.sns.youtube' => 'Youtube',
	'admin.common.label.sns.facebook' => 'Facebook',
	'admin.common.label.sns.tiktok' => 'TikTok',
	'admin.common.label.sum' => 'Sum',
	'admin.common.label.percent' => 'Percent',
	'admin.common.label.all_results' => 'All results',
	'admin.common.label.share' => 'Share',
	'admin.common.label.prev_result' => 'Previous result',
	'admin.common.label.next_result' => 'Next result',
	'admin.common.label.usage_day'=>'Before the usage day:',
	'admin.common.label.usage_time'=>'Before the usage time:',

	'admin.common.label.file_upload_extension' => 'File extension',
	'admin.common.label.file_upload_limit_size' => 'MB or less',
	'admin.common.label.auto_translation' => 'Auto translation',

	'admin.common.label.auto_translation_0' => 'No',
	'admin.common.label.auto_translation_1' => 'Yes',
	'admin.common.label.native_translation' => 'Native translation',
  
	'admin.common.label.save_complete' => 'The changes have been saved.',
	'admin.common.label.item_copy' => 'Copy',
	
	'admin.common.label.use' => 'Use',
	'admin.common.label.do_not_use' => 'Do not use',

	'admin.common.memo.title' => 'Add note',
	'admin.common.memo.details' => 'Details',
	'admin.common.memo.cancel' => 'Cancel',
	'admin.common.memo.save' => 'Save',

	'admin.common.message.action_cannot_undone' => 'This action cannot be undone.',
	'admin.common.message.confirm_to_delete' => 'Yes, I will delete it.',
	'admin.common.message.error.msg.code' => 'The code entered has been registered already. Please use a different code.',
	'admin.common.native.translation.desc' => 'Native translation is handled by our in-house staff.',
	'admin.common.native.translation.explain' => 'Please select priority and preferred language, and provide your requests.',
	'admin.common.native.translation.placeholder' => 'Please provide your requests',
	'admin.common.auto.translation.desc' => 'Please select the contents for automatic translation.',
	'admin.common.label.access_count' => 'PV count',

	'admin.template.menu.change_bot' => 'change bot',
	'admin.template.menu.check_bot' => 'check bot',
	'admin.template.menu.check_faq' => 'check faq',
	'admin.template.menu.group_bot_list' => 'group bot lists',
	'admin.template.menu.back_to_portal' => 'back to portal',
	'admin.template.menu.reference_facility_setting' => 'reference facility setting※',
	'admin.template.menu.introduction_facility_list' => 'introduction facility list※',
	'admin.template.menu.new_bot' => 'new bot※',
	'admin.template.menu.content_copy' => 'Copy',
	'admin.template.menu.content_paste' => 'Paste',
	'admin.template.menu.very_setting_copy' => 'Copy the settings for VERY',
	'admin.template.menu.very_setting_paste' => 'Paste the settings for VERY',
	'admin.template.menu.passkey_setting' => 'Passkey Settings',
	'admin.template.menu.logout' => 'Logout',
	'admin.template.menu.select_bot' => 'select bot',
	'admin.template.menu.select_child_bot' => 'select child bot',
	'admin.template.menu.manual' => 'Operating manual',
	'admin.template.menu.faq_list' => 'FAQ list',

	'admin.login.head.title' => 'talkappi admin site',
	'admin.login.title.login' => 'Sign In',
	'admin.login.label.login.id' => 'User Id ',
	'admin.login.placeholder.mail' => 'Mail',
	'admin.login.placeholder.password' => 'Password',
	'admin.login.label.language' => 'Language',
	'admin.login.checkbox.keep_login' => 'Keep me logged in',
	'admin.login.checkbox.disaster_login' => 'Login in Disaster Mode',
	'admin.login.title.forgot_password_case' => 'forget password',
	'admin.login.title.reissue' => 'reissue',
	'admin.login.title.change_password_case' => 'change your password',
	'admin.login.title.modify' => 'modify',
	'admin.login.title.forgot_password' => 'Forget Password',
	'admin.login.title.forgot_password_before_sent' => 'Password will be sent to your registered email address.',
	'admin.login.title.forgot_password_after_sent' => '',
	'admin.login.title.forgot_password_input' => 'パスワード再発行',
	'admin.login.title.forgot_password_login.id' => 'ログインID（検証用）',
	'admin.login.message.forgot_password_invalid_mail' => '正しいメールを入力してください',
	'admin.login.message.forgot_password_invalid_url' => 'URL無効です',
	'admin.login.message.forgot_password_overtime_url' => 'URLが期間切りです',
	'admin.login.message.forgot_password_complete' => 'パスワード再発行完了。新しいパスワードを利用して、ログインください。',
	'admin.login.title.change_password' => 'Change Password',
	'admin.login.title.old_password' => 'Old Password',
	'admin.login.title.new_password' => 'New Password',
	'admin.login.title.new_password_confirm' => 'New Password(confirm)',
	'admin.login.title.confirm' => 'confirm',
	'admin.login.title.back_to_login' => 'back',
	'admin.login.title.admin_site' => 'admin page',
	'admin.login.message.password_rule_1' => 'The password must be at least 11 digits and contain at least one number, letter, and special character.',
	'admin.login.message.password_rule_2' => 'When resetting a password, it is not possible to set the previous password again.',
	'admin.login.message.password_rule_3' => 'Password does not match.',
	'admin.login.message.input_valid_mail' => 'Please enter a valid email',
	'admin.login.message.invalid_id_and_mail' => 'Username and password are incorrect.',
	'admin.login.message.changed_password.' => 'You have successfully changed your password.',
	'admin.login.message.invalid_id_or_mail_check_again' => 'Your user id or password is incorrect. Please check again. Your account will be locked if you enter it incorrectly more than a certain number of times.',
	'admin.login.message.password_expired' => 'Your password has expired. Please set a new password.',

	'admin.login.message.passkey_auth_failed' => 'Passkey authentication failed.',
	'admin.login.message.passkey_auth_canceled' => 'The user canceled the operation or the operation timed out.',
	'admin.login.message.security_error' => 'A security error occurred. Please check your HTTPS connection.',
	'admin.login.message.browser_not_supported' => 'The browser does not support this feature.',
	'admin.login.message.passkey_login_failed' => 'Passkey login failed.',
	'admin.login.message.passkey_auth_start_failed' => 'Passkey authentication start failed.',
	'admin.login.message.passkey_auth_starting' => 'Starting Passkey login...',
	'admin.login.message.passkey_auth_success' => 'Passkey login successful.',
	'admin.login.message.passkey_login' => 'Passkey login',

	'admin.login.message.restricted_ip' => 'Applicable users are restricted to specific IP usage only.',
	'admin.login.message.account_locked' => 'Your account has been locked due to too many incorrect entries. The lock will be released after about 30 minutes.',
	'admin.login.message.email_not_exist' => 'Email address does not exist',
	'admin.login.message.2fa_code' => 'Authentication code',
	'admin.login.message.enter_2fa_code' => 'Please enter the 2 factor authentication code that was sent to your email address.',
	'admin.login.message.invalid_2fa_code' => 'The authentication code is invalid. You will need to re-enter your login ID and password after 5 failed attempts.',
	'admin.login.message.expired_2fa_code' => 'The authentication code has expired.',
	'admin.login.message.back_to_id_pass_form' => 'Back to ID/PASS input',
	'admin.login.message.2fa_mail_title' => 'talkappi One-Time Password Notification',
	'admin.login.message.2fa_mail_body_1' => "-------------------------------------------------\nThis email has been sent automatically.\nPlease note that we cannot respond to inquiries sent to this email address.\n-------------------------------------------------\n\nThank you for using talkappi.\n\nWe are sending you the one-time password for the talkappi management screen.\n\n【One-Time Password】\n\n",
	'admin.login.message.2fa_mail_body_2' => "\n\nThe password is valid for 30 minutes.\n",

	'admin.account.message.error.used_email_address' => 'Email address has already been registered.',
	'admin.account.message.error.email_addr_cannot_register' => 'The email address you entered cannot be used to register an account.',
	'admin.account.message.error.using_undefined_role' => 'Undefined user role is being used.',
	'admin.account.message.available_email_address' => 'Work email address that can receive emails',
	'admin.account.message.input_fullname' => 'Taro Yamada',
	'admin.account.message.password_repeat' => 'Confirm password',

	'admin.account.message.password_rule_1' => 'At least 11 digits with at least one number, letter, and special character.',

	'admin.account.message.redmine_account_created' => 'Redmine Account Created',
	'admin.account.message.redmine_account_not_created' => 'Redmine Account Not Created',
	'admin.account.label.mail_account_info' => 'Send email with account info.',
	'admin.account.label.confirm_password' => 'Confirm Password',
	'admin.account.label.2fa' => '2 Factor Authentication',
	'admin.account.label.2fa_description' => 'Enter the 6-digit security code delivered to your email',
	'admin.account.label.2fa_description.required' => 'Two-factor authentication is required as part of our security enhancements.',
	'admin.account.label.auto_password' => 'Auto-generate Password',
	'admin.account.label.password' => 'Password',
	'admin.account.label.basic_info_settings' => 'Basic Info Settings',

	'admin.account.label.user_role' => 'User Type',
	'admin.account.label.role_cd' => 'Role Code',
	'admin.account.label.role_name' => 'Role Name',
	'admin.account.label.role_description' => 'Description',
	'admin.account.label.role_functions' => 'Functions & Authorities',
	'admin.account.label.access_permission_settings' => 'Access Permission Settings',
	'admin.account.label.personal_info_access_all' => 'Personal Information Access',
	'admin.account.label.personal_info_access_all_hint' => 'When set to OFF, personal information will be masked, and users will not be able to view personal data such as user names and email addresses.',
	'admin.account.label.personal_info_access_in_charge' => 'Only allowed for in charge',
	'admin.account.label.access_not_in_charge' => 'Access Permission (Not in charge)',
	'admin.account.label.access_not_in_charge_hint' => 'Configure whether users can view results for INQUIRY, SURVEY, etc., that are not assigned to themselves.',
	'admin.account.label.auth_all_groups_flg' => 'Group facilities Permission',
	'admin.account.label.notification_settings' => 'Notification Settings',
	'admin.account.label.notify_by_email' => 'Notify with Email',
	'admin.account.label.notification_lang' => 'Notification Languages',
	'admin.account.label.call_flg' => 'Receive calls in omotenashi',
	'admin.account.label.call_group_cd' => 'Call group',
	'admin.account.label.call_type' => 'Call type',
	'admin.account.label.call_type_audio' => 'Audio',
	'admin.account.label.call_type_video' => 'Video',
	'admin.account.label.notify_by_browser' => 'Notify with web browser',
	'admin.account.label.notify_by_messenger' => 'Notify in Messenger',
	'admin.account.label.notify_by_line' => 'Notify with LINE',
	'admin.account.label.notify_by_wechat' => 'Notify with Wechat',
	'admin.account.label.notify_by_slack' => 'Notify with Slack',
	'admin.account.label.redmine_settings' => 'Redmine Settings',
	'admin.account.label.redmine_account_create' => 'Create Redmine Account',
	'admin.account.label.redmine_account_name' => 'Redmine Account Name',
	'admin.account.label.redmine_account_lastname' => 'Yamada',
	'admin.account.label.redmine_account_firstname' => 'Taro',
	'admin.account.label.redmine_account' => 'Redmine Account',
	'admin.account.label.unfreeze' => 'Unfreeze',
	'admin.account.label.receiving_emails' => 'Receiving emails',
	'admin.account.label.login_id_info' => 'Please register a work email address that you can receive. Free email addresses cannot be registered.',
	'admin.account.label.name_info' => 'Please enter your full name.',
	'admin.account.label.two_fa_info' => 'When logging in, you will enter a 6-digit security code sent to your email address for authentication.',

	'admin.account.label.redmine_related_advanced_settings' => 'Redmine Related Settings (To Advanced Settings)',
	'admin.account.label.support_settings' => 'Support',
	'admin.account.label.support_use' => 'Use support',
	'admin.account.th.user_count' => 'Users count',
	'admin.account.th.edit_operations' => 'Edit operations',
	'admin.account.th.name' => 'User name',
	'admin.account.th.account_name' => 'Name',
	'admin.account.th.user_role' => 'User role',
	'admin.account.th.login_id' => 'Login ID',
	'admin.account.th.available_lang' => 'Available languages',
	'admin.account.th.last_login' => 'Last login time',

	'admin.account.placeholder.notify_condition_setting' => 'Notify condition setting',
	'admin.common.button.save0' => 'Save',

	'admin.top.progress-info.customers' => '', //customers
	'admin.top.progress-info.conversations' => '', //conversations
	'admin.top.progress-info.requests' => '', //conversations
	'admin.top.progress-info.reservations' => '', //reservations
	'admin.top.progress-info.faqs' => '', //faqs
	'admin.top.progress-info.workingdays' => '', //Working days
	'admin.top.title.number_of_users' => 'Number of users',
	'admin.top.title.news' => 'News',
	'admin.top.title.all_news' => 'All News',
	'admin.top.label.week' => '1w',
	'admin.top.label.month' => '1m',
	'admin.top.label.half_year' => '6m',
	'admin.top.label.year' => '1y',
	'admin.top.label.all' => 'all',
	'admin.top.label.bar_chart' => 'bar chart',
	'admin.top.label.pie_chart' => 'pie chart',
	'admin.top.label.by_channel' => 'by channel',
	'admin.top.label.by_language' => 'by language',
	'admin.top.label.user_statistics' => 'User statistics',
	'admin.top.label.word_cloud' => 'Word Cloud',
	'admin.top.label.word_cloud.tip' => "Latest month's data",
	
	'admin.top.notify.title' => 'Important Notification',
	'admin.top.notify.readmore' => 'Read more',
	'admin.top.notify.readlater' => 'Read later',

	'admin.items.item_div1.title' => 'Facility contents',
	'admin.items.item_div2.title' => 'Area contents',
	'admin.items.item_div3.title' => 'Group facilities',
	'admin.items.item_div4.title' => 'Media',
	'admin.items.item_div5.title' => 'Sales contents',
	'admin.items.item_div6.title' => 'Customized contents',

	'admin.item.itemdisplay.name' => 'Facility name',
	'admin.item.itemdisplay.cate_not_setting' => 'Not set',
	'admin.item.itemdisplay.cate_tips' => '*The display order of facilities is based on categories.',
	'admin.item.itemdisplay.random_display' => 'Random display',
	'admin.item.itemdisplay.all_backrandom' => 'Randomly display',
	'admin.item.itemdisplay.add_cate_info' => 'The display order of facilities is based on categories. Please add categories.',
	'admin.item.itemdisplay.add_cate_guide' => 'Add category',
	'admin.item.itemdisplay.recommend' => 'Recommended',
	'admin.item.itemdisplay.recommend_tips' => 'Will be displayed in the "Recommended" section of the WeChat menu',
	'admin.item.itemdisplay.multilingual' => 'Language display:',

	'admin.items.item_order.label' => 'Display Order of Contents',
	'admin.items.item_order.order_title' => 'Display Order of Categories',
	'admin.items.item_order.tips_label' => '*The display order of facilities is based on their categories. Please select a category.',
	'admin.items.item_order.tips_label_2' => '*The display order of facilities is based on their categories. Please select a category. Add a new category if necessary.',
	'admin.items.item_order.apply_title' => 'Apply',
	'admin.items.item_order.random_type_-1' => 'Random (front display)',
	'admin.items.item_order.random_type_1' => 'In order',
	'admin.items.item_order.random_type_0' => 'Random (back display)',
	'admin.items.item_order.edit' => 'Display edit',

	'admin.itme.itemmeu.info' => 'Info',
	'admin.itme.itemmeu.description' => 'Description',
	'admin.itme.itemmeu.code' => 'Code',
	'admin.itme.itemmeu.keyword' => 'Keyword',
	'admin.itme.itemmeu.display' => 'Sort',
	'admin.itme.itemmeu.back' => 'Back to list',
	'admin.itme.itemmeu.next' => 'Next',
	'admin.itme.itemmeu.multiple_allowed' => 'multiple allowed',
	'admin.itme.itemmeu.regional_settings' => 'Regional settings',
	'admin.itme.itemmeu.non_display' => '(non-display)',

	'admin.itme.item.code' => 'Content code',
	'admin.itme.item.code.placeholder' => '',
	'admin.itme.item.status' => 'Status',
	'admin.itme.item.link_code' => 'Link code',
	'admin.itme.item.subclass' => 'Detailed classification',
	'admin.itme.item.display_period' => 'Display period',
	'admin.itme.item.annual_display' => 'Annual display',
	'admin.itme.item.longitude_latitude' => 'Latitude and longitude',
	'admin.itme.item.location_information' => 'location information',
	'admin.itme.item.tag' => 'Tag',
	'admin.itme.item.immediate_reflection_photos' => 'Immediate reflection of photos',
	'admin.itme.item.language_display' => 'Language display',
	'admin.itme.item.photo_display' => 'Photo display',
	'admin.itme.item.public_flag' => 'Public flag',
	'admin.itme.item.business_hours' => 'Business hours',
	'admin.itme.item.congestion_status_display' => 'Congestion status display',
	'admin.itme.item.waiting_time' => 'waiting time (minutes)',
	'admin.itme.item.vacant' => 'vacant',
	'admin.itme.item.slightly_crowded' => 'slightly crowded',
	'admin.itme.item.crowded' => 'crowded',
	'admin.itme.item.congestion_url' => 'congestion url',
	'admin.itme.item.classification_add' => 'add',
	'admin.itme.item.classification_edit' => 'classification edit',
	'admin.itme.item.additional_classification' => 'add',
	'admin.itme.item.cancel_classification' => 'cancel',
	'admin.itme.item.copy' => 'copy',
	'admin.item.item.business.hours' => 'Detailed business hours setting',
	'admin.item.item.plan.setting' => 'Plan Setting',
	'admin.item.itemdesc.mobile.url' => 'Mobile URL',
	'admin.item.itemdesc.multilang.message' => 'The information in Japanese is being displayed as inherited. Please modify the content in the language.',
	'admin.item.itemdesc.version.temporary_save' => 'Temporary Save',
	'admin.item.itemdesc.version.current' => 'Current Version',
	'admin.item.itemdesc.version.previous' => 'Previous Version',
	'admin.item.itemdesc.go_to_current' => 'Go to Current Version',
	'admin.item.itemdesc.in_edit' => 'In Edit',
	'admin.item.itemdesc.readonly' => 'Read Only',
	'admin.item.itemdesc.back_to_edit' => 'Back to Edit',
	'admin.item.itemdesc.edit_from_the_version' => 'Edit from This Version',
	'admin.item.itemdesc.reflact_image_to_other_lang' => 'Set the Same Image for All Languages',
	'admin.item.itemdesc.existed_comment' => 'Existing Comment',
	'admin.item.itemdesc.comment_label' => 'Comment',
	'admin.item.itemdesc.comment_placeholder' => 'Please enter a comment',
	'admin.item.itemdesc.no_comment' => 'No comment',
	'admin.item.itemdesc.back_to_current' => 'Back to Current Version',
	'admin.item.itemdesc.auto_translate' => 'Auto Translate',
	'admin.item.itemdesc.tranlate.message' => 'The translation subject is the name and description. Please update the URL of the photo and button by yourself if necessary.',

	'admin.itemlink.publication_period' => 'Publication period',
	'admin.itemlink.publication_display' => 'Publication display',
	'admin.itemlink.content_name' => 'Content name',
	'admin.itemlink.distance' => 'Straight line distance(km)',

	'admin.marketlink.activity' => 'activity',
	'admin.marketlink.ticket' => 'ticket',
	'admin.marketlink.restaurant' => 'restaurant',
	'admin.marketlink.select_genre' => 'Select Genre',
	'admin.marketlink.select_area' => 'Select Area',
	'admin.marketlink.tabelog' => 'Tabelog',

	'admin.reportmenu.monthly_statistics' => 'Monthly statistics',
	'admin.reportmenu.number_of_users_by_channel' => 'Number of users (by channel)',
	'admin.reportmenu.number_of_users_by_time_period' => 'Number of users (by time period)',
	'admin.reportmenu.number_of_users_by_user_flow' => 'Number of users (by user flow)',
	'admin.reportmenu.number_of_users_by_country' => 'Number of users (by country)',
	'admin.reportmenu.number_of_conversations_by_channel' => 'Number of conversations (by channel)',
	'admin.reportmenu.manned_response_status' => 'Manned response status',
	'admin.reportmenu.manned_response_daily' => 'Manned response (daily)',
	'admin.reportmenu.request_reception_status' => 'Request reception status',
	'admin.reportmenu.flow_path' => 'Flow path',

	'admin.report.monthly.title' => 'Usage report',
	'admin.report.monthly.by_new' => 'New users',
	'admin.report.monthly.by_language' => 'Users by language',
	'admin.report.monthly.by_channel' => 'Users by channel',
	'admin.report.monthly.user_count_by_time' => 'User count by time zone',

	'admin.faqreportmenu.search_window_input' => 'Search window input',
	'admin.faqreportmenu.keyword_selection' => 'Keyword selection',
	'admin.faqreportmenu.category_selection' => 'Category selection',
	'admin.faqreportmenu.question_selection' => 'Question selection',
	'admin.faqreportmenu.question_display' => 'Question display',
	'admin.faqreportmenu.related_question_display' => 'Related question display',
	'admin.faqreportmenu.survey' => 'Survey',
	'admin.faqreportmenu.flow_path' => 'Flow path',
	'admin.faqreportmenu.daily_statistics' => 'Daily statistics',
	'admin.faqreportmenu.monthly_statistics' => 'Monthly statistics',
	'admin.faqreportmenu.usage_history' => 'Usage history',

	'admin.faqs.label.chatbot_question_add' => 'Chatbot question add',
	'admin.faqs.label.faq_page_question_add' => 'FAQ page question add',
	'admin.faqs.label.check_multiple_facility' => 'Confirmation of multiple facility FAQ responses',
	'admin.faqs.label.for_faq_system' => 'For faq system',
	'admin.faqs.message.appropriate_proposal' => 'After reviewing the details of your request and the conversation history, we will make the most appropriate proposal from the following methods.',
	'admin.faqs.message.note' => '※Please note that we may not be able to respond to your request. Thank you for your understanding.',
	'admin.faqs.message.proposal_list' => '<li>・Add new FAQ</li><li>・AI Learning Enhancement</li><li>・Add content with photos</li>',
	'admin.faqs.message.js-faq-title' => 'Questions you would like to add or wording you would like to address',
	'admin.faqs.message.js-faq-answer' => 'Answer you would like to add',
	'admin.faqs.message.js-redmine-request.title' => 'Question & Answer, Special Note',
	'admin.faqs.message.js-redmine-request.description' => '*Please provide answers to additional questions and any other special notes.',
	'admin.faqs.message.js-redmine-existing.title' => 'The question you are trying to add is already available for automatic response.',
	'admin.faqs.message.js-redmine-existing.description' => 'If you want to edit the content, please click "Edit FAQ"',
	'admin.faqs.message.js-redmine-existing.faq_button' => 'FAQ Edit',
	'admin.faqs.message.js-redmine-complete.title' => 'Sending completed.',
	'admin.faqs.message.js-redmine-complete.description' => '<p>We will contact you when we have completed our response,</p><p>so please be patient.</p>',
	'admin.faqs.message.js-other-words.title' => 'Add other questions/phrases',

	'admin.talknew.button.edit_faq_old_screen' => 'Edit FAQs on the old screen',
	'admin.talknew.button.translate_multiple_languages' => 'Translate Japanese into multiple languages',
	'admin.talknew.button.inherit_child_bots' => 'Inherit responses to child bots',
	'admin.talknew.label.edit_history_none' => 'Edit history None',
	'admin.talknew.label.previous.alert' => 'Last alarm set',
	'admin.talknew.label.alert.done' => 'Sent',

	'admin.faqnew.message.note_1' => '*This is the screen for adding FAQs to the talkappi FAQ. Please note that the registered contents will not be reflected in the chatbot FAQ.',
	'admin.faqnew.message.note_2' => 'If you would like to add an FAQ to the chatbot FAQ, please submit your request through the "Add Chatbot Question" link <a href="/admin/talks">here</a>.',

	'admin.faqpage.message.setup' => '*Please be sure to set the pink unset.',

	'admin.logcsv.title.file_list' => 'List of files available for download',

	'admin.logmembers.label.search_from_basic_information' => 'Search from basic information',
	'admin.logmembers.label.search_from_chat_history' => 'Search from chat history',
	'admin.logmembers.label.search_from_user_attributes' => 'Search from user attributes',
	'admin.logmembers.label.search_from_manned_response_information' => 'Search from manned response information',

	'admin.talkreport1.label.statistics' => 'FAQ usage statistics',
	'admin.talkreport1.label.statistics.detail' => 'Usage details',
	'admin.talkreport1.label.list_of_answered_question' => 'List of answered questions',
	'admin.talkreport1.label.answered_question' => 'Answered question',
	'admin.talkreport1.label.unanswered_question' => 'Unanswered question',
	'admin.talkreport1.label.unrecognized_question' => 'Unrecognized question',
	'admin.talkreport1.label.category' => 'Category',
	'admin.talkreport1.label.question' => 'Question',
	'admin.talkreport1.label.number_of_times_questioned' => 'Number of times<br>questioned',
	'admin.talkreport1.label.number_of_times_displayed' => 'Number of times<br>displayed',
	'admin.talkreport1.label.number_of_times_rated_adequate' => 'Number of times<br>rated satisfied',
	'admin.talkreport1.label.number_of_times_rated_inadequate' => 'Number of times<br>rated unsatisfied',
	'admin.talkreport1.label.insufficient_ratio' => 'Insufficient ratio',
	'admin.talkreport1.label.num_asked' => 'Number of questions asked',
	'admin.talkreport1.label.num_displayed' => 'Number of displays',
	'admin.talkreport1.label.ai_chatbot_parentheses' => '(AI chatbot)',
	'admin.talkreport1.label.talkappi_faq_parentheses' => '(talkappi FAQ)',
	'admin.talkreport1.label.satisfaction_survey' => 'Result of satisfaction survey',
	'admin.talkreport1.label.insufficient_ratio' => 'Percentage of unsatisfied',
	'admin.talkreport1.label.adequate' => 'Satisfied',
	'admin.talkreport1.label.inadequate' => 'Unsatisfied',
	'admin.talkreport1.label.no_anwser' => 'No answer',
	'admin.talkreport1.label.adequate_total' => 'Satisfied (total)',
	'admin.talkreport1.label.inadequate_total' => 'Unsatisfied (total)',
	'admin.talkreport1.label.satisfaction_stats_total' => 'Satisfaction statistics (total)',
	'admin.talkreport1.label.csv_export_all' => 'CSV export (all)',
	'admin.talkreport1.label.csv_export_inadequacy' => 'CSV export (unsatisfied)',

	'admin.talkreport1.label.edit_faq' => 'FAQ Edit',
	'admin.talkreport1.label.channel' => 'Channel',
	'admin.talkreport1.label.name' => 'Name',
	'admin.talkreport1.label.language' => 'Language',
	'admin.talkreport1.label.scene' => 'User flow',
	'admin.talkreport1.label.date' => 'Conversation date',
	'admin.talkreport1.label.content' => 'Content',
	'admin.talkreport1.label.satisfaction' => 'Satisfaction',
	'admin.talkreport1.label.reasons_for_inadequate' => 'Reasons for unsatisfied',
	'admin.talkreport1.label.surveys_distributed' => 'Surveys distributed count',
	'admin.talkreport1.label.surveys_response' => 'Survey response count',
	'admin.talkreport1.label.chart_title.satisfaction' => 'Satisfaction',
	'admin.talkreport1.label.chart_title.surveys' => 'Survey',
	'admin.talkreport1.label.chart_title.distributed' => 'Distributed',
	'admin.talkreport1.label.chart_title.response' => 'Response',
	'admin.talkreport1.label.chart_description' => 'There may be larger numbers of "No answer" as users who are satisfied with the answer tend to end the chat without selecting "satisfied" and dissatisfied users tend to choose "unsatisfied".<br/>Please note that the number of "satisfied" users may be higher than the data shown in the graph on the right.',

	'admin.talkreport8.label.list_of_unanswered_question' => 'List of unanswered questions',
	'admin.talkreport8.label.count' => 'Count of questions',
	'admin.talkreport8.label.answer_question_display' => 'Answer question display',
	'admin.talkreport8.label.latest_response_status' => 'Latest Response Status',
	'admin.talkreport8.label.answered' => 'Answered',
	'admin.talkreport8.label.unregistered' => 'Unregistered',

	'admin.talkreport2.label.list_of_unrecognized_question' => 'List of unrecognized question',
	'admin.talkreport2.label.date_of_question' => 'Date of question',
	'admin.talkreport2.label.question_content' => 'Question content',
	'admin.talkreport2.label.sns' => 'Channel',
	'admin.talkreport2.label.name' => 'Name',
	'admin.talkreport2.label.request' => 'Request',

	'admin.report3.label.new_user_not_asked' => 'New users[Not Asked]',
	'admin.report3.label.number_of_repeat_user_by_month' => 'Number of repeat users(by month)[cumulative total]',
	'admin.report3.label.new_conversation_number_of_user_utterances' => 'New conversation[number of user utterances]',

	'admin.botreport3.label.new_user_not_asked' => 'Number of new users',
	'admin.botreport3.label.number_of_not_asked' => 'Number of users who have not asked questions',
	'admin.botreport3.label.number_of_repeat_user' => 'Number of repeat users',
	'admin.botreport3.label.new_conversation_number' => 'New conversation number',
	'admin.botreport3.label.total_conversation_number' => 'Total conversation number',

	'admin.report7daily.label.day' => 'Day',
	'admin.report7daily.label.count' => 'Number of Cases',

	'admin.msgdesc.button_message' => '※ If "button name" is not set, the corresponding menu will be hidden.',

	'admin.uploadfiles.h5' => 'Please upload files to be used on talkappi.<br/>Please refrain from using it for any other purpose.',

	'admin.maximums.stock_list' => 'Stock list',
	'admin.maximums.stock_title' => 'Stock title',
	'admin.maximums.stock_type' => 'Stock type',
	'admin.maximums.stock_label' => 'Stock label',
	'admin.maximums.tax_label' => 'Tax label',
	'admin.maximums.stock_remain_few_label' => 'Remain few label',
	'admin.maximums.maximum_num_switch' => 'Plan maximum flag',
	'admin.maximums.category' => 'Plan',
	'admin.maximums.category_master_description' => 'Plan master must be registered before use.',
	'admin.maximums.category_add' => 'Add plan',
	'admin.maximums.remaining_stock' => 'Remaining stock',
	'admin.maximums.stock_change' => 'Stock change',
	'admin.maximums.number_of_stock' => 'Number of stock',
	'admin.maximums.christmas_cake' => 'Christmas cake',
	'admin.maximums.stock_name' => 'Stock name',
	'admin.maximums.stock_advanced_settings' => 'Stock advanced settings',
	'admin.maximums.code_placeholder' => 'Auto-generated',
	'admin.maximums.reset' => 'Reset stock',
	'admin.maximums.stock_csv_export' => 'Remaining stock CSV export',
	'admin.maximums.stock_detail_settings' => 'Stock detail settings',
	'admin.maximums.stock_period' => 'Stock period',
	'admin.maximums.stock_usage' => 'Stock usage',
	'admin.maximums.reservation_date' => 'Reservation date',
	'admin.maximums.reception_id' => 'Reception ID',
	'admin.maximums.stock_date' => 'Stock (date)',
	'admin.maximums.stock_time' => 'Stock (time)',
	'admin.maximums.stock_range_start' => 'From',
	'admin.maximums.stock_range_end' => 'To',
	'admin.maximums.number_slots_groups' => 'Number of slots',
	'admin.maximums.number_slots_people' => 'Number of slots (people)',
	'admin.maximums.total_price' => 'Amount',
	'admin.maximums.breakdown' => 'Breakdown (only shown if breakdown is set for stock pricing)',
	'admin.maximums.reservation_lead' => 'Reservation lead',
	'admin.maximums.resend_reservation_email' => 'Resend reservation completion email',
	'admin.maximums.resend_cancel_email' => 'Resend cancellation email',
	'admin.maximums.consistent_remaining' => 'Consistent with remaining slots',
	'admin.maximums.csv_field_setting' => 'Fields setting',
	'admin.maximums.stop_selling' => 'Stop selling',
	'admin.maximums.force_update' => 'The stock quantity you set exceeds the upper limit. Ignore and update.',

	'admin.maximumcalendar.title' => 'Inventory calendar list',
	'admin.maximumcalendar.details' => 'Inventory calendar details',
	'admin.maximumcalendar.name' => 'Inventory calendar name',
	'admin.maximumcalendar.calendar_name' => 'Calendar name',
	'admin.maximumcalendar.add_edit' => 'Add/Edit calendars',
	'admin.maximumcalendar.large_view' => 'Large calendar view',
	'admin.maximumcalendar.num' => 'amount',
	'admin.maximumcalendar.price' => 'quantity',

	'admin.maximumorders.label.today' => 'Today',
	'admin.maximumorders.label.tomorrow' => 'Tomorrow',
	'admin.maximumorders.label.this_month' => 'This month',
	'admin.maximumorders.label.next_month' => 'Next month',
	'admin.maximumorders.label.result_type' => 'Show results by',
	'admin.maximumorders.label.split' => 'Split',
	'admin.maximumorders.label.summary' => 'Plain',

	'admin.label.labels' => 'Business labels',
	'admin.label.title.id' => 'ID',
	'admin.label.title.div' => 'Div',
	'admin.label.title.name' => 'Name',
	'admin.label.title.relation' => 'Relation',

	'admin.notices.very.display' => 'Displayed In Very',
	'admin.notices.notice.area' => 'Notice area',
	'admin.notices.half.modal' => 'Half modal',
	'admin.notice.list' => 'Notices list',
	'admin.notice.name' => 'Notice name',
	'admin.notice.name.explanation' => 'This name is used for the administration site. It is not displayed to users.',
	'admin.notice.explanation' => 'Creating a notice alone will not make it appear on the VERY. Please go to "Top Screen Settings" and select the notice you want to display from "Content Settings" under "Announcements".',
	'admin.notice.setting' => 'Notice setting',
	'admin.notice.basic_setting' => 'Basic setting',
	'admin.notice.contents' => 'Contents',
	'admin.notice.halfmodal.title.helper' => 'Displayed in bold',
	'admin.notice.halfmodal.description.helper' => 'If a line break is not forced, it will automatically break at around 20 characters (depending on the device).',

	'admin.verytop.very.setting' => 'Top screen settings',
	'admin.verytop.format_setting' => 'Style',
	'admin.verytop.background_color' => 'Background color',
	'admin.verytop.text_color' => 'Text color',
	'admin.verytop.basic_settings' => 'Basic settings',
	'admin.verytop.title' => 'Title',
	'admin.verytop.main_image' => 'Main image',
	'admin.verytop.main_image_add' => 'Add',
	'admin.verytop.logo_image' => 'Logo image',
	'admin.verytop.content_setting' => 'Content setting',
	'admin.verytop.top_functions' => 'Top contents',
	'admin.verytop.footer' => 'Footer',
	'admin.verytop.add' => 'Add',
	'admin.verytop.add_contents' => 'Add',
	'admin.verytop.basic_functions' => 'Basic functions',
	'admin.verytop.customize' => 'Customize',
	'admin.verytop.change' => 'Save',
	'admin.verytop.notices' => 'Notices',
	'admin.verytop.notices.half.modal' => 'Half modal',
	'admin.verytop.main_contents' => 'Main functions',
	'admin.verytop.official_sns' => 'Official accounts',
	'admin.verytop.content' => 'Content',
	'admin.verytop.inquiry' => 'Inquiry',
	'admin.verytop.congestion' => 'Congestion',
	'admin.verytop.customize_content' => 'Customize content',
	'admin.verytop.userguide' => 'Userguide',
	'admin.verytop.reception' => 'Reception',
	'admin.verytop.page' => 'Page',
	'admin.verytop.survey' => 'Survey',
	'admin.verytop.laundry' => 'Laundry',
	'admin.verytop.traveling_check' => 'Traveling Check',
	'admin.verytop.traveling_check.time' => 'Time',
	'admin.verytop.traveling_check.time.minute' => 'minute',
	'admin.verytop.traveling_check.distance' => 'Distance',
	'admin.verytop.traveling_check.distance.meters' => 'meters',
	'admin.verytop.traveling_check_description' => 'The application will no longer be accessible after the specified time has elapsed. To enable functionality, the user must scan the QR code again or be within the specified distance from the facility.',
	'admin.verytop.call' => 'Internal Call',
	'admin.verytop.call.room_num' => 'Room number',
	'admin.verytop.select_five' => 'You can select up to five',
	'admin.verytop.select_three' => 'You can select up to three',
	'admin.verytop.preview_message' => 'The preview is not updated realtime.<br />Please save the settings to see the changes.',
	'admin.verytop.source_bot' => 'Source bot',
	'admin.verytop.source_user_flow' => 'Source user flow',
	'admin.verytop.font_upload' => 'Font upload',
	'admin.verytop.font_setting' => 'Font setting',
	'admin.verytop.font_setting.placeholder' => 'If not specified, the default font is applied',
	'admin.verytop.background_image' => 'Background Image',
	'admin.verytop.category_type' => 'Main menu display',
	'admin.verytop.category_type_button' => 'Button display',
	'admin.verytop.category_type_icon' => 'Icon display',
	'admin.verytop.widget.settings' => 'Widget Settings',
	'admin.verytop.widget.show' => 'Show',
	'admin.verytop.widget.hide' => 'Hide',
	'admin.verytop.widget.weather_settings' => 'Weather widget',
	'admin.verytop.widget.wether_temperature' => 'Weather / High & Low Temperatures',
	'admin.verytop.widget.sunrise_sunset' => 'Sunrise / Sunset',
	'admin.verytop.widget.wifi_settings' => 'Wi-fi widget',
	'admin.verytop.widget.wifi_content_label' => 'text content',
	'admin.verytop.copy_url_with_scene_cd' => 'Copy the URL with user flow',
	'admin.verytop.history.section_that_cannot_restore_backup' => 'This field cannot be restored to the original state even if the previous version is restored.',
	'admin.verytop.history.please_edit_after_restore_backup' => 'Please save the content after making changes.',
	'admin.verytop.history.cannot_show_mobile_preview_for_old_versions' => 'Page previews of the previous versions cannot be displayed.',
	
	'admin.veryreport.title' => 'VERY TRAVEL Usage Stats',
	'admin.veryreport.progress-info.user' => '', //VERY USER
	'admin.veryreport.progress-info.member' => '', // VERY MEMBER
	'admin.veryreport.progress-info.guest' => '', //VERY GUEST
	'admin.veryreport.main-click' => 'Main function clicks',
	'admin.veryreport.top-click' => 'Top category clicks',
	'admin.veryreport.day' => 'Day',
	'admin.veryreport.week' => 'Week',
	'admin.veryreport.month' => 'Month',
	'admin.veryreport.year' => 'Year',
	'admin.veryreport.times' => ' clicks',
	'admin.veryreport.click_count_graph_title' => 'Monthly number of clicks on features and contents',
	'admin.veryreport.not_unique_user_count_tip' => 'Not the number of unique users',
	'admin.veryreport.item_name' => 'Item name',
	'admin.veryreport.total' => 'Total',

	'admin.veryreport1.user-log' => 'User growth',
	'admin.veryreport1.user-log-lang' => 'Users by language',
	'admin.veryreport1.click' => 'Clicks count',
	'admin.very.report.functions' => 'Top contents',
	'admin.very.report.categories' => 'Main functions',
	'admin.very.report.notice' => 'Notices',
	'admin.very.report.sns' => 'SNS',
	'admin.very.report.user_count' => 'Users',
	'admin.very.report.click_count' => 'Clicks',
	'admin.very.report.goto_old_ver' => 'Back to old version',
	'admin.very.report.user_statistics' => 'User statistics',
	'admin.very.report.click_statistics' => 'Click statistics',

	'admin.veryuserguide.apply_template' => 'Apply template',

	'admin.adminveryreport.title' => 'Very Usage Stats',
	'admin.adminveryreport.daily' => 'Daily',
	'admin.adminveryreport.monthly' => 'Monthly',
	'admin.adminveryreport.bot_name' => 'Bot Name',
	'admin.adminveryreport.unique_user_count' => 'Unique User Count',
	'admin.adminveryreport.operation_count' => 'Operation Count',

	'admininquiry.baseinfo.not_send_mail' => 'No email sent',
	'admininquiry.baseinfo.not_notify' => 'No notification',

	'admin.waiting.reception' => 'Reception',
	'admin.waiting.reception.setting' => 'Reception setting',
	'admin.waiting.fasilities.lists' => 'Facility List',
	'admin.waiting.basic.setting' => 'Basic Settings',
	'admin.waiting.fasility.setting' => 'Facility Settings',
	'admin.waiting.facility.use' => 'Facility',
	'admin.waiting.add.fasility' => 'Add New Facility',
	'admin.waiting.pick.fasilities' => 'Pick from Facilities',
	'admin.waiting.ticket.setting' => 'Ticket Settings',
	'admin.waiting.ticket.time' => 'Ticket Availability Time',
	'admin.waiting.max.reception.party' => 'Daily Reception Total',
	'admin.waiting.waiting.time.caliculate' => 'Waiting Time Calculation',
	'admin.waiting.call.soon' => 'Soon-to-be-Calling Notification',
	'admin.waiting.call.pair' => 'pairs',
	'admin.waiting.call.pair.after' => 'after',
	'admin.waiting.call.atfer' => 'Call After',
	'admin.waiting.call.minute' => 'minutes',
	'admin.waiting.auto.cancel' => 'Automatic Cancellation',
	'admin.waiting.online.ticket' => 'Online Ticketing',
	'admin.waiting.waiting.time' => 'Waiting Time',
	'admin.waiting.minute.pair' => 'minutes/pair',
	'admin.waiting.available.num' => 'Simultaneous Reception',
	'admin.waiting.time.simulation' => 'Simulation:',
	'admin.waiting.waiting.about' => 'Approximately',
	'admin.waiting.pair.num' => 'pairs Waiting',
	'admin.waiting.time.num' => 'minutes Waiting',
	'admin.waiting.on' => 'Open',
	'admin.waiting.off' => 'Paused',
	'admin.waiting.not_public' => 'Not Public',
	'admin.waiting.auto.off' => 'Auto Paused',
	'admin.reception.status.automatic_stop' => 'Automatic Stop',
	'admin.reception.status.manual_stop' => 'Manual Stop',
	'admin.reception.facility.name' => 'Facility Name',
	'admin.reception.waiting.parties' => 'Number of Waiting Parties',
	'admin.reception.reception_status' => 'Reception Status',
	'admin.reception.result.list' => 'Calling Management',
	'admin.reception.operation.list' => 'Reception Page',
	'admin.reception.reception.no' => 'Reception Number Setting',
	'admin.reception.reception.no.desc' => 'You can set the reception number format for each facility. (Example: If you want to set it as T0001, set it as TNNNN.)',
	'admin.reception.member' => 'Member Registration',
	'admin.reception.member.necessary' => 'Required',
	'admin.reception.capacity.setting' => 'Capacity Setting',
	'admin.reception.capacity.item.add' => 'Add Item',
	'admin.reception.printer' => 'On-Site Ticket Printing',
	'admin.reception.printer.setting' => 'On-Site Ticket Printing Setting',
	'admin.reception.auto.pause' => 'Automatic Pause Function',
	'admin.reception.auto.pause.tooltip' => 'Automatically calculate waiting time and suspend ticket issuance when it exceeds the closing time of operations.',
	'admin.reception.printer.ip.address' => 'IP Address (Printer)',
	'admin.reception.printer.port.no' => 'Port Number (Printer)',
	'admin.reception.printer.receipt.description' => 'Receipt Description',

	'adminvery.receptionlist.facility-status.taken' => "Online reception taken",
	'adminvery.receptionlist.facility-status.paused' => "Reception paused",
	'adminvery.receptionlist.facility-status.auto.paused' => "Reception paused because of the crowdness",
	'adminvery.receptionlist.facility-status.pause' => "Pause",
	'adminvery.receptionlist.facility-status.resume' => "Resume",
	'adminvery.receptionlist.label.number-called' => "Number<br/>called",
	'adminvery.receptionlist.label.number-waiting' => "Number<br/>waiting",
	'adminvery.receptionlist.label.waiting-time' => " Waiting time",
	'adminvery.receptionlist.label.calling-soon-notice' => '"Calling soon"<br>notice',
	'adminvery.receptionlist.label.groups' => "groups",
	'adminvery.receptionlist.label.minutes' => "min.",
	'adminvery.receptionlist.label.status.waiting' => 'Not called yet',
	'adminvery.receptionlist.label.status.complete' => 'Reception complete',
	'adminvery.receptionlist.label.status.cancel' => 'Cancel',
	'adminvery.receptionlist.label.status.calling' => 'Calling',
	'adminvery.receptionlist.label.reception-number' => 'Reception number',
	'adminvery.receptionlist.label.complete-at' => 'Reception completed at',
	'adminvery.receptionlist.label.canceled-at' => 'Canceled at',
	'adminvery.receptionlist.label.time-issued' => 'Time issued',
	'adminvery.receptionlist.label.status' => 'Status',
	'adminvery.receptionlist.label.operation' => 'Operation',

	'adminvery.veryqr.label.pop_setting' => 'POP Settings',
	'adminvery.veryqr.label.qr_setting' => 'QR Code Settings',
	'adminvery.veryqr.label.room_setting' => 'Room Settings',
	'adminvery.veryqr.label.pop_design' => 'POP Design',
	'adminvery.veryqr.label.size' => 'Size',
	'adminvery.veryqr.label.background_color' => 'Background Color',
	'adminvery.veryqr.label.text_color' => 'Text Color',
	'adminvery.veryqr.label.qr_code_color' => 'QR Code Color',
	'adminvery.veryqr.label.subtitle' => 'Subtitle',
	'adminvery.veryqr.label.main_title' => 'Main Title',
	'adminvery.veryqr.label.room_number' => 'Room Number',
	'adminvery.veryqr.label.room_number_list' => 'Room Number List',
	'adminvery.veryqr.label.logo_image' => 'Logo Image',
	'adminvery.veryqr.label.icon' => 'Icon',
	'adminvery.veryqr.label.add_icon' => 'Add Icon',
	'adminvery.veryqr.label.export_data' => 'Export Data',
	'adminvery.veryqr.label.preview' => 'Preview (Reflects after saving)',
	'adminvery.veryqr.label.notice_room_number' => 'This is the public URL that serves as the base for the QR code. It does not include information such as room numbers. To verify the destination, please scan the generated QR code.',

	'adminvery.reception_operation.title_ja' => '順番待ち受付',
	'adminvery.reception_operation.title_en' => 'Queue registration',
	'adminvery.reception_operation.receipt' => 'Print Receipt',
	'adminvery.reception_operation.receipt_ja' => '発券する',
	'adminvery.reception_operation.receipt_en' => 'Get a Ticket',
	'adminvery.reception_operation.status.connecting' => 'Connecting',
	'adminvery.reception_operation.status.reconnecting' => 'Reconnecting',
	'adminvery.reception_operation.status.complete' => 'Printing Complete',
	'adminvery.reception_operation.status.error' => 'Error',
	'adminvery.reception_operation.back' => 'Return to Ticketing Screen',

	'adminvery.receptionlog.label.notification_counts' => 'Notification Counts',
	'adminvery.receptionlog.label.notification_unit_price' => 'Notification Unit Price',
	'adminvery.receptionlog.label.notification_total_price' => 'Total Price',
	

	'adminvery.receptiondisplay.display_settings' => 'Display Settings',
	'adminvery.receptiondisplay.usage' => 'Display Usage',
	'adminvery.receptiondisplay.items' => 'Items to Display on the Screen',
	'adminvery.receptiondisplay.public_url' => 'Display Public URL',

	'admin.push.new' => 'New notifcation',
	'admin.push' => 'Push notification',
	'admin.push.filter' => 'talkappi filter settings',
	'admin.push.target' => 'Target',
	'admin.push.all_channel_target' => 'Target for all channels',

	'admin.push.line_target' => 'Target of LINE',
	'admin.push.line_available_count' => 'Contents available for the month',
	'admin.push.line_limit_count' => 'Contents limit for the month',
	'admin.push.method' => 'Method',
	'admin.push.to.all' => 'Send to all',
	'admin.push.individual' => 'Send immediately',
	'admin.push.last.talk' => 'Last chat period',
	'admin.push.register' => 'Sign up period',
	'admin.push.user.attr' => 'User attribute',
	'admin.push.facility.cd' => 'User flow',
	'admin.push.search.key' => 'Keyword search',
	'admin.push.search.name' => 'Search from name, user No., etc.',
	'admin.push.search.chat' => 'Search from chat history',
	'admin.push.pick.member' => 'Pick up members to send',
	'admin.push.pick.content' => 'Pick up contents to send(Up to 5)',
	'admin.push.pick.content_notice' => 'For content that is set only in Japanese, it will not be sent to users who are not Japanese.',
	'admin.push.member.id' => 'Member ID',
	'admin.push.last.talk.time' => 'Last chat date',
	'admin.push.test.user' => 'Test user',
	'admin.push.web.user' => 'Web user',
	'admin.push.send.member' => 'Send to',
	'admin.push.member.numbers' => 'people',
	'admin.push.member.posts' => 'posts',
	'admin.push.save.once' => 'Save temporarily',
	'admin.push.confirm' => 'Check',
	'admin.push.target.range' => 'Target range',
	'admin.push.facility' => 'Target facility',
	'admin.push.content' => 'Notification content',
	'admin.push.name' => 'Notification name',
	'admin.push.mode' => 'Notification mode',
	'admin.push.date' => 'Date and time of dispatch',
	'admin.push.date.plan' => 'Notification schedule',
	'admin.push.send' => 'Send',
	'admin.push.state' => 'Status',
	'admin.push.result' => 'Result',
	'admin.push.resend' => 'Resend',
	'admin.push.type' => 'Type',
	'admin.push.type_and_date' => 'Type and date',
	'admin.push.content.cd' => 'Content CD',
	'admin.push.content.name' => 'Content name',
	'admin.push.content.new' => 'Edit contents',
	'admin.push.lists' => 'Content list',
	'admin.push.type' => 'Notification type',
	'admin.push.status' => 'Status',
	'admin.push.created.by' => 'Creator',
	'admin.push.detail.back' => 'Back to details',
	'admin.push.segment' => 'Segment',
	'admin.push.target.num' => 'Target number',
	'admin.push.all.facility' => 'All facilities',
	'admin.push.all.facility.cd' => 'All user flows',
	'admin.push.user.attr.all' => 'All user attributes',
	'admin.push.chennel.all' => 'All channels',
	'admin.push.result.all' => 'All results',
	'admin.push.created' => 'Created',
	'admin.push.plan' => 'Planned',
	'admin.push.execute' => 'Executed',
	'admin.push.done' => 'Completed',
	'admin.push.test' => 'Test',
	'admin.push.real' => 'Real',
	'admin.push.execute.log' => 'Log',
	'admin.push.not.send' => 'Unsent',
	'admin.push.line_limit_error_message' => 'The targets have exceeded the possible push messages for the month. The messages will not be sent to some LINE users.',

	'admin.common.error.operation.save' => 'Operation failed',

	'admin.send.addr.button.import_from_inquiry' => 'Import from inquiry result',
	'admin.send.addr.button.import_from_survey' => 'Import from survey result',
	'admin.send.addr.button.import_from_member' => 'Import from member',
	'admin.send.addr.button.import_from_account' => '※Import from talkappi accounts',
	'admin.send.addr.button.import_from_csv' => 'Import from CSV',
	'admin.send.addr.button.import_members' => 'Import',
	'admin.send.addr.button.add_to_project' => 'Add to project',
	'admin.send.addr.title.select_project' => 'Select project',
	'admin.send.task.label.object' => 'Project',
	'admin.send.task.label.sender_display_name' => 'Sender display name',
	'admin.send.task.label.sender_display_name_hint' => 'You can set up any display name for sender here. If you do not set a display name, only sender address will be shown.',
	'admin.send.label.mode' => 'Send mode',
	'admin.send.label.test_addresses' => 'Test emails',
	'admin.send.button.test' => 'Send test email',
	'admin.send.label.signature' => 'Signature Settings',
	'admin.send.label.signature_select' => 'Please select a signature',
	'admin.send.label.signature_edit' => 'Signature edit',
	'admin.send.tag.tag.untagged' => 'Untagged',


	'admin.newsletterimport.label.select_language' => 'Please select the language. (The function to import from multilingual forms will be released gradually)',
	'admin.newsletterimport.label.select_language_survey' => 'Please select the language.',
	'admin.newsletterimport.label.select_inquiry' => 'Please select the inquiry',
	'admin.newsletterimport.label.select_survey' => 'Please select the survey',
	'admin.newsletterimport.label.select_label' => 'Please select the item label',
	'admin.newsletterimport.label.select_entry' => 'Please select the item',
	'admin.newsletterimport.label.tag' => 'Tag',
	'admin.newsletterimport.label.pcount' => 'people',
	'admin.newsletterimport.label.import_inquiry' => 'Import',
	'admin.newsletterimport.label.import_survey' => 'Import',
	'admin.newsletterimport.label.import_member' => 'Import',
	'admin.newsletterimport.label.export' => 'Take In',
	'admin.newsletterimport.button.exclude' => 'Remove',
	'admin.newsletterimport.label.all_facilities' => 'All facilities',
	'admin.newsletterimport.label.project' => 'Project: ',

	'admin.newsletterimport.btn.preview' => 'Import',
	'admin.newsletterimport.btn.apply' => 'Take In',
	'admin.newsletterimport.label.select_bot' => 'Please select the facilities',
	'admin.newsletterimport.label.extend' => 'entend item',
	'admin.newsletterimport.label.add_extend_entry' => 'add entend item',

	'admin.signature.label.signature' => 'Signature',
	'admin.signature.label.title' => 'Signature Title',
	'admin.signature.label.detail' => 'Signature Content',

	'admin.newsletterproject.label.project' => 'Project',
	'admin.newsletterproject.label.project_list' => 'Projects',
	'admin.newsletterproject.label.project_setting' => 'Project Setting',
	'admin.newsletterproject.label.name' => 'Project name',
	'admin.newsletterproject.label.description' => 'Description',
	'admin.newsletterproject.label.person_in_charge_setting' => 'Person in charge',
	'admin.newsletterproject.th.last_update' => 'Last Update',
	'admin.newsletterproject.th.address' => 'Contacts',
	'admin.newsletterproject.th.tag' => 'Tags',
	'admin.newsletterproject.th.newsletter' => 'Newsletters',

	'admin.newsletternewtask.quota_display.message' => 'Current month sent emails: %s out of %s, remaining: %s. If the limit is exceeded, additional charges will apply.',
	'admin.newsletter.label.resolve_suppression' => 'Remove from suppression list',

	'admin.newsletter.label.admin_user_name' => 'Name (Admin)',
	'admin.newsletter.label.admin_bot_name' => 'Facility name (Admin)',

	'admin.surveys.label.name' => 'Survey name',

	'admin.inquiry.label.gtm_detail' => 'To set container ID of the Google Tag Manager (GTM) , please input the ID such as GTM-XXXXXXXX.',
	'admin.chat.header.bot_target' => 'Bot target',
	'admin.chat.header.user_lead' => 'User guidance',
	'admin.chat.header.chat_date' => 'Chat date',
	'admin.chat.header.operator' => 'Operator',
	'admin.chat.header.username' => 'User name',
	'admin.chat.header.display_range' => 'Display range',
	'admin.chat.header.user_display' => 'User display (ascending)',
	'admin.chat.header.history_delete' => 'Deletable history',
	'admin.chat.header.information_masking' => 'Display personal information',
	'admin.chat.header.enter_key' => 'Enter key action',
	'admin.chat.header.order_by_registration' => 'By registration',
	'admin.chat.header.order_by_last_used' => 'By last used',
	'admin.chat.header.display_masking' => 'Hide (masking)',
	'admin.chat.header.enter_key_send' => 'Send',
	'admin.chat.header.enter_key_line' => 'New line (Shift+Enter to send)',
	'admin.chat.header.online' => 'Online',
	'admin.chat.header.online_operator' => 'Online operators',

	'admin.chat.window.add_memo' => 'Add memo',
	'admin.chat.window.type_memo' => 'Enter memo',
	'admin.chat.window.send_support' => 'Send support',
	'admin.chat.window.auto_translate' => 'Automatic translation',
	'admin.chat.window.operator_responding' => 'Operator responding',
	'admin.chat.window.operator_respond' => 'Operator respond',
	'admin.chat.window.local_time' => 'Local time',
	'admin.chat.window.see_more' => 'See more',
	'admin.chat.window.unread' => 'Unread',
	'admin.chat.window.already_responded' => 'Make responded',
	'admin.chat.window.send_mail' => 'Send mail',
	'admin.chat.window.delete_chat' => 'Delete message',
	'admin.chat.window.start_chat' => 'The customer has opened the chat window',
	'admin.chat.window.end_chat' => 'The customer has closed the chat window',

	'admin.chat.form.type_message' => 'Enter message...',
	'admin.chat.form.message_template' => 'Message template',
	'admin.chat.form.message_template_select' => 'Select message template',
	'admin.chat.form.respond_all' => 'Respond to all',
	'admin.chat.form.preview' => 'Preview of content',
	'admin.chat.form.operator' => 'Operator',
	'admin.chat.form.operator_respond' => 'Operator respond',
	'admin.chat.form.ai_unknown_question' => 'AI unrecognizable questions',
	'admin.chat.form.ai_unknown_question_clear' => 'Clear',
	'admin.chat.form.ai_unknown_question_none' => 'None',

	'admin.chatlist.user_search' => 'Search user',
	'admin.chatlist.web_user' => 'Web User',
	'admin.chatlist.responding' => 'Responding',
	'admin.chatlist.request' => 'Request',

	'admin.servicelist.title.servicelist' => 'Request History',
	'admin.servicelist.title.service' => 'Request Response',
	'admin.servicelist.label.servicelist' => 'Request',
	'admin.servicelist.label.service' => 'Request Registration ※',
	'admin.servicelist.table.period' => 'Period',
	'admin.servicelist.table.condition' => ' Condition',
	'admin.servicelist.table.notification' => ' Notification',
	'admin.servicelist.table.deleted_display' => 'Display Deleted',
	'admin.servicelist.table.search' => ' Search',
	'admin.servicelist.table.csv_export' => 'CSV Export',

	'admin.servicetable.title.date' => ' Date',
	'admin.servicetable.title.type' => ' Type',
	'admin.servicetable.title.details' => 'Details',
	'admin.servicetable.title.contact' => 'Contact',
	'admin.servicetable.title.status' => 'Status',
	'admin.servicetable.title.assignee' => 'Assignee',
	'admin.servicetable.label.reception_id' => 'Reception ID:',
	'admin.servicetable.label.chat' => 'Chat',
	'admin.servicetable.label.fixed_phase' => 'Fixed Phase',
	'admin.servicetable.label.edit' => 'Edit',
	'admin.servicetable.label.delete' => 'Delete',
	'admin.servicetable.label.memo' => 'Memo',

	'admin.servicebox.label.edit' => 'Edit',
	'admin.servicebox.label.chancel' => 'Cancel',
	'admin.servicebox.label.submit' => 'Submit',

	'admin.service.label.servicelist' => 'Request History',
	'admin.service.label.reception_list' => 'Reception List',
	'admin.service.label.reception_application' => 'Request Registration ※',
	'admin.service.label.date' => 'Date',
	'admin.service.label.time' => 'Time',
	'admin.service.label.type' => 'Type',
	'admin.service.label.details' => 'Details',
	'admin.service.label.room_number' => 'Room Number',
	'admin.service.label.name' => 'Name',
	'admin.service.label.accept' => 'Accept',
	'admin.service.label.back' => 'Back',
	'admin.service.label.status.unhandled' => 'Unhandled',
	'admin.service.label.status.handling' => 'Handling',
	'admin.service.label.status.completed' => 'Completed',
	'admin.service.label.status.cancelled' => 'Cancelled',
	'admin.service.label.status.change' => 'Update request status',

	'admin.service.dropdown.status' => '-Status-',
	'admin.service.dropdown.all' => 'All',
	'admin.service.dropdown.no_status' => 'None',

	'admin.site.setting' => 'Site page setting',
	'admin.site.refer' => 'Inflow route',
	'admin.site' => 'Site page',
	'admin.site.id' => 'Page ID',
	'admin.site.name.explanation' => 'This name is used for the administration site. It is not displayed to users.',
	'admin.site.title' => 'Web Page Title',
	'admin.site.desc' => 'Web Page Description',
	'admin.site.footer' => 'Footer',
	'admin.site.image' => 'Page image',

	'admin.sitepages.label.title' => 'Title',
	'admin.sitepages.label.link' => 'Page Link',
	'admin.sitepage.label.embed_link' => 'Embed Code',
	'admin.site.items_background_image' => 'Background image',
	'admin.site.main_image.ratio' => '4:1. If there is no Page description, then 6:1.',
	'admin.sitepage.label.embed_scroll_mode' => 'Side-scroll',
	'admin.sitepage.label.embed_grid_mode' => 'View in a list',

	// 2023/3/10 #39310
	'admin.surveymenu.label.survey' => 'Basic Settings',
	'admin.surveymenu.label.new' => 'New Registration',
	'admin.surveymenu.label.desc' => 'Overview Setting',
	'admin.surveymenu.label.entry' => 'Survey Questions',
	'admin.surveymenu.label.result' => 'Answer Results',
	'admin.surveymenu.label.couponresult' => 'Coupon Usage Status',
	'admin.surveymenu.label.surveys' => 'Return to List',
	'admin.surveymenu.label.report' => 'Result Report',

	'admin.survey.label.survey_setting' => 'Survey Settings',
	'admin.survey.label.survey_name' => 'Survey Name',
	'admin.survey.label.scene_cd' => 'User Flow',
	'admin.survey.label.template_cd' => 'Template',
	'admin.survey.label.user_in_charge' => 'Person in Charge',
	'admin.survey.label.class_cd' => 'Category (multiple)',
	'admin.survey.label.code' => 'Code',
	'admin.survey.label.period' => 'Period',
	'admin.survey.label.duration' => 'Required Time(minutes)',
	'admin.survey.label.supported_languages' => 'Supported languages',
	'admin.survey.label.lang_display' => 'Multilingual Display',
	'admin.survey.label.survey_answer_limit' => 'Answer Limit/Total',
	'admin.survey.label.survey_answer_limit_hint' => 'Leave blank if there is no limit',
	'admin.survey.label.answer_limit' => 'Answer Limit/Person',
	'admin.survey.label.answer_limit_yes' => 'YES',
	'admin.survey.label.answer_limit_no' => 'NO',
	'admin.survey.label.answer_setting' => 'After Answering Settings',
	'admin.survey.label.redirect_url' => 'Redirect URL',
	'admin.survey.label.member_mail_template' => 'Send to User',
	'admin.survey.label.user_mail_template' => 'Send to Administrator',
	'admin.survey.label.mail_users' => 'Recipient',
	'admin.survey.label.present' => 'Coupon',
	'admin.survey.label.public_setting' => 'Public Setting',
	'admin.survey.label.public_url' => 'Public URL',

	'admin.surveydesc.label.input_page' => 'Answer Page',
	'admin.surveydesc.label.complete_page' => 'Completion Page',
	'admin.surveydesc.label.complete_basic_info' => 'Basic Information',
	'admin.surveydesc.label.complete_title' => 'Title',
	'admin.surveydesc.label.complete_summary' => 'Summary',
	'admin.surveydesc.label.icon' => 'Image',
	'admin.surveydesc.label.icon_default' => 'Default Image',
	'admin.surveydesc.label.icon_setting' => 'Set Image',
	'admin.surveydesc.label.input_setting' => 'Title Setting',
	'admin.surveydesc.label.input_title' => 'Survey Name',
	'admin.surveydesc.label.input_description_summary' => 'Summary',
	'admin.surveydesc.label.survey_image' => 'Main Image',
	'admin.surveydesc.label.description_section' => 'Description Section',
	'admin.surveydesc.label.input_extra_title' => 'Title',
	'admin.surveydesc.label.input_extra_description_summary' => 'Content',
	'admin.surveydesc.label.add_description_section' => 'Add Description Section',
	'admin.surveydesc.label.mobile_preview' => 'Preview (Mobile only)',
	'admin.surveydesc.label.action' => 'Action',
	'admin.surveydesc.label.display_text' => 'Display Text',
	'admin.surveydesc.label.destination_url' => 'Destination URL',
	'admin.surveydesc.label.placeholder_destination_url' => 'Please enter the URL of the destination',
	'admin.surveydesc.label.base_language' => 'Base language',
	'admin.surveydesc.label.automatic_translate' => 'Automatic Translation',

	'admin.surveyresult.label.csv_type_cd_1' => 'Bulk multiple answers',
	'admin.surveyresult.label.csv_type_cd_2' => 'Split multiple answers',
	'admin.surveyresult.label.title' => 'Survey Name: ',
	'admin.surveyresult.label.csv.label' => 'Multiple-choice Options',
	'admin.surveyresult.label.type_total' => 'Survey Statistics',
	'admin.surveyresult.label.type_detail' => 'Answer Details',
	'admin.surveyresult.label.pdf_export' => 'Export screen as PDF',
	'admin.surveyresult.label.fup_download' => 'download',
	'admin.surveyresult.label.ratio' => 'Ratio',
	'admin.surveyresult.label.count_person' => ' Person',
	'admin.surveyresult.label.pie_chart' => 'Pie Chart',
	'admin.surveyresult.label.vertical_bar_chart' => 'Vertical Bar Chart',
	'admin.surveyresult.label.horizontal_bar_chart' => 'Horizontal Bar Chart',
	'admin.surveyresult.label.other_input' => 'Other input',
	'admin.surveyresult.label.customize_button' => 'Customize graph button',
	'admin.surveyresult.label.flg_ai_bot_name' => 'Name',
	'admin.surveyresult.label.answser_date' => 'Answer date',
	'admin.surveyresult.label.answer_content' => 'Answer content',
	'admin.surveyresult.label.follow_reply' => 'Follow-up reply',
	'admin.surveyresult.label.minute' => 'minute(s)',
	'admin.surveyresult.label.table' => 'Table',
	'admin.surveyresult.label.number_of_people' => 'Number of people',
	'admin.surveyresult.label.emotion_mode_all' => 'All Emotions',
	'admin.surveyresult.label.emotion_mode_positive' => 'Positive',
	'admin.surveyresult.label.emotion_mode_negative' => 'Negative',
	'admin.surveyresult.label.posneg' => 'Positive/Negative',
	'admin.surveyresult.label.emotion_graph' => 'Emotion Analysis Graph',
	'admin.surveyresult.label.emotion_graph.legend_positive' => 'Positive',
	'admin.surveyresult.label.emotion_graph.legend_negative' => 'Negative',
	'admin.surveyresult.label.emotion_graph.legend_neutral' => 'Neutral',
	'admin.surveyresult.label.eval' => 'Score Evaluation',
	'admin.surveyresult.label.message' => 'Send message',

	'admin.surveyreport.label.view' => 'View',
	'admin.surveyreport.label.monthly' => 'Monthly',
	'admin.surveyreport.label.daily' => 'Daily',
	'admin.surveyreport.label.count_answer' => 'Answer Count',
	'admin.surveyreport.label.csvexport.monthly_filename' => 'Monthly Response Count',
	'admin.surveyreport.label.csvexport.daily_filename' => 'Daily Response Count',
	'admin.surveyreport.label.csvexport.header_time_monthly' => 'Month',
	'admin.surveyreport.label.csvexport.header_time_daily' => 'Day',
	'admin.surveyreport.label.csvexport.header_count' => 'Response Count',

	'admin.inquirymenu.label.inquirys_list' => 'Inquiry Forms List',
	'admin.inquirymenu.label.inquiry_detal' => 'Inquiry Form Details',
	'admin.inquirymenu.label.basic_settings' => 'Basic Settings',
	'admin.inquirymenu.label.add_new' => 'Add New',
	'admin.inquirymenu.label.summary_settings' => 'Overview Settings',
	'admin.inquirymenu.label.create_inquiry_form' => 'Inquiry Form Creation',
	'admin.inquirymenu.label.inquiry_result' => 'Inquiry Results',
	'admin.inquirymenu.label.inquiry_refer' => 'Flow Path',

	'admin.inquirys.label.list' => 'Forms list',
	'admin.inquirys.label.inquiry_name' => 'Form name',
	'admin.inquirys.label.period' => 'Period',
	'admin.inquirys.label.class_cd' => 'Category tag',
	'admin.inquirys.label.answers_count' => 'Number of responses',
	'admin.inquirys.label.awaiting_response' => 'Awaiting responses',
	'admin.inquirys.label.access_count' => 'PV count',
	'admin.inquirys.label.conversion_rate' => 'Conversion rate',
	'admin.inquirys.label.last_update' => 'Last update',
	'admin.inquirys.label.has_coupon' => 'Coupon available',
	'admin.inquirys.label.revision' => ' Revision',
	'admin.inquirys.label.person_in_charge' => 'Person in charge',
	'admin.inquirys.label.template_list_default' => '- Template -',
	'admin.inquirys.label.newinquiry' => 'Create from blank ',
	'admin.inquirys.label.copyfromtemplate' => 'Copy from template',

	'admin.inquiry.label.inquiry_settings' => 'Inquiry form settings',
	'admin.inquiry.label.appearance_settings' => 'Appearance settings',
	'admin.inquiry.label.advanced_settings' => 'Advanced settings',
	'admin.inquiry.label.inquiry_name' => 'Inquiry form name',
	'admin.inquiry.label.scene_cd' => 'User Flow',
	'admin.inquiry.label.template' => 'Template',
	'admin.inquiry.label.user_in_charge' => 'Person in charge',
	'admin.inquiry.label.class_cd' => 'Category (multiple)',
	'admin.inquiry.label.base_ver_id' => 'Revision base',
	'admin.inquiry.label.base_ver_id_hint' => 'Revision source INQUIRY ID can be specified',
	'admin.inquiry.label.entry_label' => 'Entry label',
	'admin.inquiry.label.inquiry_cd' => 'Code',
	'admin.inquiry.label.inquiry_cd_placeholder' => 'Automatically generate',
	'admin.inquiry.label.period' => 'Period',
	'admin.inquiry.label.support_flg' => 'Response state',
	'admin.inquiry.label.support_flg_no' => 'NO',
	'admin.inquiry.label.support_flg_yes' => 'YES',
	'admin.inquiry.label.use_cart' => 'Show Shopping Cart',
	'admin.inquiry.label.request_completion_notification_message' => 'Request completion notification message',
	'admin.inquiry.label.notification_message_default' => 'Room {3} guest {1} has requested to borrow an item.',
	'admin.inquiry.label.recaius_explanation' => 'Message definition for notifications:<br>{1}: Name<br>{2}: Phone<br>{3}: Room Number<br>{4}: Usage Date and Time<br>{5}: Email<br>{51}: Extend1<br>{52}: Extend2<br>{53}: Extend3<br>{54}: Extend4<br>{55}: Extend5',
	'admin.inquiry.label.pms_integration' => 'PMS integration',
	'admin.inquiry.label.pms_integration_hint' => 'After integrating with the PMS, you will be able to select (Charge to Room) as a payment method. Currently, the only PMS available for integration is Assist.',
	'admin.inquiry.label.is_mail_logged' => 'Record email interactions',
	'admin.inquiry.label.cancel' => 'Cancel',
	'admin.inquiry.label.cancel_inquiry' => 'Cancel',
	'admin.inquiry.label.modify_inquiry' => 'Modify',
	'admin.inquiry.label.cancel_no' => 'Disable',
	'admin.inquiry.label.cancel_yes' => 'Enable',
	'admin.inquiry.label.cancel_period' => 'Cancellation deadline',
	'admin.inquiry.label.modify_period' => 'Modify deadline',
	'admin.inquiry.label.day_before' => 'Days before',
	'admin.inquiry.label.day_after' => 'Days after',
	'admin.inquiry.label.limit_time' => 'Until',
	'admin.inquiry.label.before_hour' => 'Hours before',
	'admin.inquiry.label.ok_whenever' => 'Available anytime',
	'admin.inquiry.label.cancel_policy' => 'Policy setting',
	'admin.inquiry.label.duration' => 'Time required (minutes)',
	'admin.inquiry.label.support_language' => 'Supported languages',
	'admin.inquiry.label.lang_display' => 'Multilingual display',
	'admin.inquiry.label.faq_scene' => 'Show chatbot & FAQ',
	'admin.inquiry.label.faq_scene_hit' => 'Please select "Do not show" except for the general inquiry form.',
	'admin.inquiry.label.faq_blank_text' => 'Do not show',
	'admin.inquiry.label.gtm_tag_setting' => 'GTM tag setting',
	'admin.inquiry.label.receipt_setting' => 'Receipt setting',
	'admin.inquiry.label.receipt_setting_no' => 'No Output',
	'admin.inquiry.label.receipt_setting_yes' => 'Output',
	'admin.inquiry.label.tax_rate' => 'Tax Rate',
	'admin.inquiry.label.reception_id_prefix' => 'Reception ID setting',
	'admin.inquiry.label.reception_id_prefix_hint' => 'The format of the reception ID can be set for each form. (e.g. set to TNNNN if you want it to be T0001)',
	'admin.inquiry.label.receiption_id_random' => 'Use random ID',
	'admin.inquiry.label.inquiry_answer_limit' => 'Limit on number of responses/total',
	'admin.inquiry.label.inquiry_answer_limit_hint' => 'If blank, there is no limit',
	'admin.inquiry.label.answer_limit' => 'Limit on number of responses/person',
	'admin.inquiry.label.answer_limit_yes' => 'YES',
	'admin.inquiry.label.answer_limit_no' => 'NO',
	'admin.inquiry.label.cancel_modify_settings' => 'Cancel/modify settings',
	'admin.inquiry.label.after_answer_settings' => 'After response settings',
	'admin.inquiry.label.member_mail_template' => 'Send to user',
	'admin.inquiry.label.member_mail_template_thanks' => 'thanks mail',
	'admin.inquiry.label.thanks_mail_text_1' => 'Send mail',
	'admin.inquiry.label.thanks_mail_text_2' => '',
	'admin.inquiry.label.member_mail_remind' => 'Send reminders (up to 2 times)',
	'admin.common.label.first_time' => 'First time',
	'admin.common.label.second_time' => 'Second time',
	'admin.inquiry.label.member_mail_from' => 'Sender address',
	'admin.inquiry.label.member_mail_from_placeholder' => 'use <EMAIL>',
	'admin.inquiry.label.member_mail_form_hint' => 'The sender address will <NAME_EMAIL>. If you wish to set your company email address as the sender, additional settings are required. Please contact your Customer Success representative for assistance.',
	'admin.inquiry.label.member_mail_from_error' => 'The specified sender email address is not verified. <br/>Click on the URL of the approval email from Amazon to approve it. Emails will not be sent unless approved.',
	'admin.inquiry.label.member_mail_replyto' => 'Reply-To Address',
	'admin.inquiry.label.member_mail_replyto_placeholder' => '<EMAIL>',
	'admin.inquiry.label.member_mail_replyto_hint' => 'This will be the email address where replies from users will be received. Please set it to your company email address.',
	'admin.inquiry.label.usage_day_hint' => 'Applicable only when date and time slots are set. If multiple items have slots set, the item with the earliest time will be applied.',
	'admin.inquiry.label.usage_time_hint' => 'Applicable only when time slots are set. If multiple items have slots set, the item with the earliest time will be applied.',	
	'admin.inquiry.label.member_mail_sender' => 'Set facility name as sender',
	'admin.inquiry.label.member_mail_sender_no' => 'Not set',
	'admin.inquiry.label.member_mail_sender_yes' => 'Set',
	'admin.inquiry.label.user_mail_template' => 'Send to administrator',
	'admin.inquiry.label.mail_users' => 'Recipients',
	'admin.inquiry.label.action' => 'Actions',
	'admin.inquiry.label.notify.admin' => 'Notify administrator',
	'admin.inquiry.label.notify.user' => 'Notify user',
	'admin.inquiry.label.present' => 'Coupon',
	'admin.inquiry.label.payment_settings' => 'Payment settings',
	'admin.inquiry.label.payment_2_step' => 'Pay after confirm',
	'admin.inquiry.label.payment_2_step.pay_in_hours' => 'Pay limited(in hours)',
	'admin.inquiry.label.payment_sandbox' => 'Payment Sandbox Mode',
	'admin.inquiry.label.payment' => 'Payment method',
	'admin.inquiry.label.payment_1' => 'Local payment',
	'admin.inquiry.label.payment_2' => 'Credit card',
	'admin.inquiry.label.payment_3' => 'Paypay',
	'admin.common.label.redirect_to' => 'Rediect to',
	'admin.common.label.redirect_to_hint' => 'Normally, this setting is not required. If set, access to the public URL will automatically redirect to the URL set here.',
	'admin.inquiry.label.mail.admin' => 'Admin mail',
	'admin.inquiry.label.mail.users' => 'User mail',
	'admin.inquiry.label.open_url_decription' => '*The URL has been improved so that it cannot be guessed.(You can still access from the old URL)',
	'admin.common.label.limit_acceptance_hours' => 'Limit acceptance hours',
	'admin.common.label.limit_acceptance_hours_hint' => 'Please enter time inquiries are accepted.',
	'admin.inquiry.label.form_link_hint' => "Please select the form you wish to view Inquiry Results for from the dropdown menu on the left, then click 'Go to form'.",
	'admin.inquiry.label.url.standard' => 'Standard URL',
	'admin.inquiry.label.url.flow_path' => 'Flow path URL',
	'admin.inquiry.label.url.standard.hint' => 'A simple URL without tracking functionality.</br>If you want to track how users accessed the form, please use the "Flow path URL" instead.',
	'admin.inquiry.label.url.flow_path.hint' => 'A URL that allows you to track how users accessed the form.</br>You can use this in the "Flow path" tab for marketing and traffic analysis purposes.',
	'admin.inquiry.label.url.mail' => 'Email',
	'admin.inquiry.label.url.hp' => 'HP',

	'admin.inquirysetting.label.sender_setting' => 'Sender address setting',
	'admin.inquirysetting.placeholder.input_domain' => 'Please enter the domain',
	'admin.inquirysetting.button.get_dns' => 'Get DNS',
	'admin.inquirysetting.label.dns_record_details' => 'DNS record details',
	'admin.inquirysetting.th.record_type' => 'Type',
	'admin.inquirysetting.th.host_name' => 'Host',
	'admin.inquirysetting.th.value' => 'Value',
	'admin.inquirysetting.corporation' => 'Company Name',
	'admin.inquirysetting.address' => 'Address',
	'admin.inquirysetting.tel' => 'Phone number',
	'admin.inquirysetting.regist_no' => 'Registration Number',
	'admin.inquirysetting.template' => 'Template',
	'admin.inquirysetting.tax_rate' => 'Default Tax Rate',
	'admin.inquirysetting.user_in_charge.setting' => 'Member setting',
	'admin.inquirysetting.user_in_charge' => 'Set a member',

	'admin.inquirydesc.label.display_lang_not_show' => '(Not Displayed)',
	'admin.inquirydesc.label.base_language' => 'Base Language',
	'admin.inquirydesc.label.automatic_translate' => 'Automatic Translation',
	'admin.inquirydesc.label.input_page' => 'Answer Page',
	'admin.inquirydesc.label.confirm_page' => 'Confirmation Page',
	'admin.inquirydesc.label.complete_page' => 'Completion Page',
	'admin.inquirydesc.label.basic_info_complete' => 'Basic Information (Confirmation Page)',
	'admin.inquirydesc.label.basic_info_hint' => 'The settings made in the response screen are automatically reflected.',
	'admin.inquirydesc.label.extend' => 'Expand',
	'admin.inquirydesc.label.basic_info_confirm' => 'Basic Information (Confirmation Page)',
	'admin.inquirydesc.label.inquiry_name' => 'Inquiry Form Name',
	'admin.inquirydesc.label.basic_info' => 'Basic Information',
	'admin.inquirydesc.label.inquiry_title' => 'Inquiry Title',
	'admin.inquirydesc.label.inquiry_description' => 'Inquiry Overview',
	'admin.inquirydesc.label.brief_tip' => 'This summary description is only displayed when viewing an INQUIRY listing, such as PAGE or BOOK.',
	'admin.inquirydesc.label.edit_inquiry_section' => 'Edit Description Section',
	'admin.inquirydesc.label.inquiry_section_edit' => 'Description Section Edit',
	'admin.inquirydesc.label.section_info_hint' => 'If not edited, it will be copied from the answer page.',
	'admin.inquirydesc.label.cancel' => 'Cancel',
	'admin.inquirydesc.label.main_picture' => 'Main Picture',
	'admin.inquirydesc.label.description_section' => 'Description Section',
	'admin.inquirydesc.label.description_section_title' => 'Title',
	'admin.inquirydesc.label.description_section_content' => 'Content',
	'admin.inquirydesc.label.initialization' => 'Initial Display',
	'admin.inquirydesc.label.initialization_show' => 'Show',
	'admin.inquirydesc.label.initialization_fold' => 'Fold',
	'admin.inquirydesc.label.description_section_remove' => 'Do you want to remove the description section?',
	'admin.inquirydesc.label.action_can_not_undone' => 'This action cannot be undone.',
	'admin.inquirydesc.label.modal_delete_button' => 'Delete',
	'admin.inquirydesc.label.modal_cancel_button' => 'Cancel',
	'admin.inquirydesc.label.description_section_add' => 'Add Description Section',
	'admin.inquirydesc.label.add_description_section_title' => 'Title',
	'admin.inquirydesc.label.add_description_section_picture' => 'Picture',
	'admin.inquirydesc.label.add_description_section_desc' => 'Overview',
	'admin.inquirydesc.label.brief' => 'Brief',
	'admin.inquirydesc.label.actions' => 'Actions',
	'admin.inquirydesc.label.display_text' => 'Display Text',
	'admin.inquirydesc.label.destination_url' => 'Destination URL',
	'admin.inquirydesc.label.destination_url_placeholder' => 'Enter destination URL',
	'admin.inquirydesc.label.action_add' => 'Add Action',
	'admin.inquirydesc.label.preview' => 'Preview (Mobile Only)',

	'admin.inquiryresult.label.csv_type_cd_1' => 'Bulk multiple answers',
	'admin.inquiryresult.label.csv_type_cd_2' => 'Split multiple answers',
	'admin.inquiryresult.lable.inquiry_name' => 'Inquiry Form Name: ',
	'admin.inquiryresult.label.type_total' => 'Inquiry Form Statistics',
	'admin.inquiryresult.label.type_detail' => 'Answer Details',
	'admin.inquiryresult.label.spam_display' => 'Display Spam',
	'admin.inquiryresult.label.deleted_display' => 'Display Deleted',
	'admin.inquiryresult.label.auto_refresh' => 'Auto Refresh',
	'admin.inquiryresult.label.fup_download' => 'Download',
	'admin.inquiryresult.label.ratio' => 'Ratio',
	'admin.inquiryresult.label.table' => 'Table',
	'admin.inquiryresult.label.pie_chart' => 'Pie Chart',
	'admin.inquiryresult.label.vertical_bar_chart' => 'Vertical Bar Chart',
	'admin.inquiryresult.label.horizontal_bar_chart' => 'Horizontal Bar Chart',
	'admin.inquiryresult.label.other_input' => 'Other Input',
	'admin.inquiryresult.label.other' => 'Other',
	'admin.inquiryresult.label.sales_amount' => 'Sales Amount:',
	'admin.inquiryresult.label.usage_limit' => 'Usage Limit',
	'admin.inquiryresult.label.to_usage_status' => 'To Usage Status',
	'admin.inquiryresult.label.contact' => 'Contact',
	'admin.inquiryresult.label.response_status' => 'Response Status',
	'admin.inquiryresult.label.response_history' => 'Response History',
	'admin.inquiryresult.label.result' => 'Detail',
	'admin.inquiryresult.label.reception_id' => 'Reception ID',
	'admin.inquiryresult.label.service_id' => 'Request ID',
	'admin.inquiryresult.label.conductor' => 'Conductor',
	'admin.inquiryresult.label.done' => 'Completed(New)',
	'admin.inquiryresult.label.modified' => 'Completed(Modified)',
	'admin.inquiryresult.label.modified_history' => 'Modify History',
	'admin.inquiryresult.label.modifycancel' => 'Cancelled by Modify',
	'admin.inquiryresult.label.pre_done' => 'Temporary Reservation',
	'admin.inquiryresult.label.cancel_done' => 'Cancelled',
	'admin.inquiryresult.label.deleted' => 'Deleted',
	'admin.inquiryresult.label.resultdetail' => 'Detail',
	'admin.inquiryresult.label.spam_user' => 'Spam User',
	'admin.inquiryresult.label.unspam_user' => 'Normal User',
	'admin.inquiryresult.label.auto_translate' => 'Auto Translate',
	'admin.inquiryresult.label.confirm_mail' => 'Resend Confirmation Email (to user)',
	'admin.inquiryresult.label.confirm_mail_admin' => 'Resend Confirmation Email (to admin)',
	'admin.inquiryresult.label.cancel_mail' => 'Resend Cancel Email (to user and admin)',
	'admin.inquiryresult.label.cancel_mail_admin' => 'Resend Cancel Email (to admin)',
	'admin.inquiryresult.label.2_step_pay_confirm' => 'Send Payment Request Email',
	'admin.inquiryresult.label.2_step_pay_refuse' => 'Send Payment Cancel Email',
	'admin.inquiryresult.label.add_memo' => 'Add Memo',
	'admin.inquiryresult.label.language_default' => 'All languages',
	'admin.inquiryresult.filter.change.language' => 'To filter by answers, please select the language of the form.',
	'admin.inquiryresult.label.saved_search_conditions' => 'Saved Search Conditions',
	'admin.inquiryresult.label.untitled_condition' => 'Untitled Conditions',
	'admin.inquiryresult.label.no_saved_search_conditions' => 'No saved search condition',
	'admin.inquiryresult.dialog.amount.title' => 'Payment amount settings',
	'admin.inquiryresult.dialog.amount.label' => 'Amount(JPY)',

	'admin.inquiryrefer.label.inflow_route' => 'Inflow route (Domain)',
	'admin.inquiryrefer.label.inflow_url' => 'Inflow route (URL)',
	'admin.inquiryrefer.label.referent' => 'Referent Domain',
	'admin.inquiryrefer.label.referent.url' => 'Referent URL',
	'admin.inquiryrefer.label.answer_start' => 'Answer start',
	'admin.inquiryrefer.label.answer_end' => 'Answer end',
	'admin.inquiryrefer.label.not_set' => 'Not set',
	'admin.inquiryrefer.label.location_detail' => 'City/town',

	'admin.inquirycalendars.title' => 'Inquiry calendar list',
	'admin.inquirycalendars.th.name' => 'Inquiry calendar name',
	'admin.inquirycalendars.label.public_url' => 'Public URL',//リンク
	'admin.inquirycalendar.label.calendar_setting' => 'Calendar setting',
	'admin.inquirycalendar.label.calendar_name' => 'Calendar name',
	'admin.inquirycalendar.label.add_edit' => 'Add/Edit inquiries',
	'admin.inquirycalendar.label.public_iframe_code' => 'Public iframe code',
	'admin.inquirycalendar.label.remark' => 'Remark',

	'admin.pay.client.id' => 'Order ID',
	'admin.pay.client.name' => 'Transaction Facility',
	'admin.pay.service.type' => 'Service Category',
	'admin.pay.service.name' => 'Product Name',
	'admin.pay.link.id' => 'Processing ID',
	'admin.pay.charge.date' => 'Sales Date',
	'admin.pay.charge.fix.date' => 'Transaction Date',
	'admin.pay.change.date' => 'Modify Date',
	'admin.pay.cancel.date' => 'Cancel Date',
	'admin.pay.transaction.time' => 'Transaction Time',
	'admin.pay.payment.type' => 'Payment Method',
	'admin.pay.payment.type.detail' => 'Payment Method Details',
	'admin.pay.payment.amount' => 'Amount',
	'admin.pay.payment.commision' => 'Commission Rate',
	'admin.pay.agency.commision' => 'Agency Commission',
	'admin.pay.result.cd' => 'Transaction Type',
	'admin.pay.transaction.result' => 'Transaction Result',
	'admin.pay.transaction.error' => 'Error',
	'admin.pay.transaction.detail' => 'Transaction Details',
	'admin.pay.transaction.client' => 'Transaction Client',
	'admin.pay.month.before' => 'Last Month',
	'admin.pay.this.month' => 'This Month',
	'admin.pay.transaction.name' => 'Transaction Name',
	'admin.pay.target.month' => 'Target Month',
	'admin.pay.tax' => 'Tax',
	'admin.pay.transfer.fee' => 'Transfer Fee',
	'admin.pay.transfer.fee.less_than_three' => 'under 30,000 yen',
	'admin.pay.transfer.fee.more_than_three' => '30,000 yen or more',
	'admin.pay.transaction.fee' => 'Transaction Fee',
	'admin.pay.aggregation.period' => 'Aggregation Period',
	'admin.pay.aggregation.date' => 'Aggregation Date',
	'admin.pay.sales.amount' => 'Sales Amount',
	'admin.pay.talkappi.commision' => 'talkappi Commission',
	'admin.pay.transfer.amount' => 'Transfer Amount',
	'admin.pay.mail.auto_send' => 'Auto Send',
	'admin.pay.mail.complete' => 'Send Complete',
	'admin.pay.mail.incomplete' => 'Not Sent',
	'admin.pay.transaction.detail.csv' => 'Output Transaction Details',
	'admin.payment.common.label.payment_month_1' => 'This month ',
	'admin.payment.common.label.payment_month_2' => 'Next month ',
	'admin.payment.common.label.payment_month_3' => 'Month after next ',
	'admin.payment.common.label.day' => '',
	'admin.payment.common.label.last_day' => 'Last day',
	'admin.payment.common.label.list' => 'Transaction master list',
	'admin.payment.common.label.detail' => 'Details',

	'admin.payment.label.list' => 'Transaction master list',
	'admin.payment.label.client_id' => 'Client ID',
	'admin.payment.lable.auto_generate' => 'Automatically generated',
	'admin.payment.label.name' => 'Client name',
	'admin.payment.label.postal_code' => 'Postal code',
	'admin.payment.label.address' => 'Address',
	'admin.payment.label.tel' => 'Phone number',
	'admin.payment.lable.person_in_charge' => 'Person in charge',
	'admin.payment.lable.accounting_person_in_charge' => 'Accounting person in charge',
	'admin.payment.label.bots_name' => 'Transaction facility',
	'admin.payment.lable.add_bots' => 'Add facility',
	'admin.payment.label.dealings_information' => 'Transaction information',
	'admin.payment.label.account_type' => 'Account type',
	'admin.payment.label.account_type_01' => 'General Account',
	'admin.payment.label.account_type_02' => 'Deposit Account',
	'admin.payment.label.account_number' => 'Account number',
	'admin.payment.label.contract_date' => 'Contract date',
	'admin.payment.lable.handling.period' => 'Handling Period',
	'admin.payment.lable.due_date' => 'Sales closing date',
	'admin.payment.label.payment_date' => 'Payment date',
	'admin.payment.label.acti_commision' => 'Commission rate 1',
	'admin.payment.label.acti_commision2' => 'Commission rate 2',
	'admin.payment.label.acti_commision2.tooltip' => 'Other than Visa and Mastercard',
	'admin.payment.label.term_information' => 'Payment term',
	'admin.payment.label.term_1time' => '1 time',
	'admin.payment.label.term_2time' => '2 times',
	'admin.payment.label.operation' => 'Operation',
	'admin.payment.label.operation.transactions' => 'Transaction details',
	'admin.payment.label.operation.monthlyreport' => 'Monthly report',
	'admin.payment.label.basic_information' => 'Basic information',

	'admin.congestionforecast.label.title' => 'Congestion Forecast Settings',
	'admin.congestion.label.current_status' => 'Current Congestion Status',
	'admin.congestion.label.restrict_time_undecided' => 'Time of Release Undecided',
	'admin.congestion.label.last_update' => 'Last Update',
	'admin.congestion.label.congestion_change' => 'Change Congestion Status',
	'admin.congestion.label.congestion_status.clowded' => 'Crowded',
	'admin.congestion.label.congestion_status.slightly_clowded' => 'Slightly Crowded',
	'admin.congestion.label.congestion_status.vacant' => 'Vacant',
	'admin.congestion.label.congestion_status.restricted' => 'Entry Restricted',
	'admin.congestion.label.restrict_setting_question' => 'Would you like to set entry restrictions?',
	'admin.congestion.label.restrict_setting_tips' => 'If no release time is set, it will be displayed as "Time of Release Undecided."',
	'admin.congestion.label.estimated_release_time' => 'Estimated Release Time',

	'admin.textmining.label.title' => 'Text mining',
	'admin.textmining.label.language' => 'Language',
	'admin.textmining.label.by_attribute' => 'By attribute',
	'admin.textmining.label.by_extracted_words' => 'By extracted words',

	'member.members.label.members' => 'Customer List',
	'member.members.label.basic_info' => 'Basic Information',
	'member.members.label.member_id' => 'Customer ID',
	'member.members.label.member_info' => 'Member Information',
	'member.members.label.detail' => 'View Details',
	'member.members.label.contact_info' => 'Contact Information',
	'member.members.label.mail' => 'Email',
	'member.members.label.phone' => 'Phone',
	'member.members.label.address' => 'Address',
	'member.members.label.add_info' => 'Additional Information',
	'member.members.label.add' => 'Add',
	'member.members.label.visit_history' => 'Visit History',
	'member.members.label.visits' => 'Visits ',
	'member.members.label.times' => ' Times',
	'member.members.label.room_assignment' => 'Room Assignment',
	'member.members.label.purpose' => 'Purpose',
	'member.members.label.reservation_channel' => 'Reservation Channel',
	'member.members.label.unregistered' => 'Unregistered',
	'member.members.label.meal_service' => 'Meal Service',
	'member.members.label.staff_service_history' => 'Staff Service History',
	'member.members.label.page' => 'Page',
	'member.members.label.visit_period' => 'Visit Period',
	'member.members.label.additional_conditions' => 'Additional Conditions',
	'member.members.label.check_all_conditions' => 'Check All Conditions',
	'member.members.label.notes' => 'Notes',
	'member.members.label.support' => 'Support',
	'member.memberdetail.label.memberdetail' => 'Customer Details',
	'member.memberdetail.label.no_data' => 'No Data',
	'member.memberdetail.label.basic_info' => 'Basic Information',
	'member.memberdetail.label.customer_id' => 'Customer ID',
	'member.memberdetail.label.name' => 'Name',
	'member.memberdetail.label.furigana' => 'Furigana',
	'member.memberdetail.label.region' => 'Region',
	'member.memberdetail.label.age_group' => 'Age Group',
	'member.memberdetail.label.notice' => 'Customer Notes (Latest)',
	'member.memberdetail.label.add_info' => 'Additional Information',
	'member.memberdetail.label.member_info' => 'Member Information',
	'member.memberdetail.label.tel' => 'Phone',
	'member.memberdetail.label.email' => 'Email',
	'member.memberdetail.label.birthday' => 'Birthday',
	'member.memberdetail.label.address' => 'Address',
	'member.memberdetail.label.dm_ok' => 'DM allowed',
	'member.memberdetail.label.dm_ng' => 'DM not allowed',
	'member.memberdetail.label.expand' => 'Expand',
	'member.memberdetail.label.modify' => 'Edit',
	'member.memberdetail.label.detail' => 'Detail',
	'member.memberdetail.label.delete' => 'Delete',
	'member.memberdetail.label.support_history' => 'Staff Support History',
	'member.memberdetail.label.visit_count' => 'Visit Count',
	'member.memberdetail.label.date' => 'Date',
	'member.memberdetail.label.task' => 'Task',
	'member.memberdetail.label.staff' => 'Staff',
	'member.memberdetail.label.response' => 'Response',
	'member.memberdetail.label.arrangement' => 'Arrangement',
	'member.memberdetail.label.notes' => 'Notes',
	'member.memberdetail.label.action' => 'Action',
	'member.memberdetail.label.additional_label.body' => 'Body',
	'member.memberdetail.label.additional_label.allergy' => 'Allergy',
	'member.memberdetail.label.additional_label.dislike' => 'Dislike',
	'member.memberdetail.label.additional_label.feature' => 'Feature',
	'member.memberdetail.label.support_arrange.arrangement' => 'Arrangement',
	'member.memberdetail.label.support_arrange_status.accept' => 'Accept',
	'member.memberdetail.label.support_arrange_status.dealing' => 'Dealing',
	'member.memberdetail.label.support_arrange_status.complete' => 'Done',
	'member.memberdetail.label.support_arrange_status.cancel' => 'Cancel',
	'member.visits.label.visits_management' => 'Visit Management',
	'member.visits.label.ref_manshitsuonrei' => 'Refer Manshitsuonrei',
	'member.visits.label.checkin_date' => 'Check-in date',
	'member.visits.label.reservation_count' => 'Number of Reservation',
	'member.visits.label.count_of_reservation' => '{count} group',
	'member.visits.label.room_list' => 'Room List',
	'member.visits.label.kitchen_remarks' => 'Kitchen remarks',
	'member.visits.label.export_all' => 'Batch export',

	// ユーザー動線
	'user_flow.faq.label.header.setting' => 'ヘッダー設定',
	'user_flow.faq.label.title.color' => '文字色',
	'user_flow.faq.label.title.backgroundcolor' => '背景色',
	'user_flow.faq.label.logo' => 'ロゴ画像',
	'user_flow.faq.label.logo.position' => 'ロゴ画像の位置',
	'user_flow.faq.label.pc' => 'パソコン',
	'user_flow.faq.label.title.text' => '施設名表示',
	'user_flow.faq.label.checkbox.show' => '表示する',
	'user_flow.faq.label.logo.size' => 'ロゴサイズ(px)',
	'user_flow.faq.label.mobile' => 'モバイル',
	'user_flow.faq.label.button.setting' => 'ボタン設定',
	'user_flow.faq.labe.button.color' => 'ボタン色',
	'user_flow.faq.label.hover' => 'ホバー時',
	'user_flow.faq.label.hover.color' => 'hover時の色',
	'user_flow.faq.label.button.inquiry' => 'お問合せボタン',
	'user_flow.faq.label.url.inquiry' => '問合せURL',
	'user_flow.faq.label.font.setting' => 'フォント設定',
	'user_flow.faq.label.fotter.setting' => 'フッター設定',
	'user_flow.faq.label.fotter.backgroundcolor' => '背景色',
	'user_flow.faq.label.footer.logo' => 'フッターロゴ',

	'user_flow.survey.label.show.setting' => '表示設定',
	'user_flow.survey.label.theme.color' => 'テーマ色',
	'user_flow.survey.label.text.label.color' => 'ラベル文字色',
	'user_flow.survey.label.favicon' => 'favicon画像',
	'user_flow.survey.label.close.color' => '閉じる✕色',
	'user_flow.survey.label.logo.radius' => 'ロゴradius',
	'user_flow.survey.label.button.color' => '色／枠線色',
	'user_flow.survey.label.text.color' => '文字色',
	'user_flow.survey.label.button1' => 'ボタン１',
	'user_flow.survey.label.button2' => 'ボタン２',
	'user_flow.survey.label.preview.pc' => 'プレビュー (ﾊﾟｿｺﾝ)',
	'user_flow.survey.label.preview.mobile' => 'プレビュー (モバイル)',
	'user_flow.survey.label.ex.title' => 'タイトル(例)',
	'user_flow.survey.label.ex.desc' => '説明文(例)',
	'user_flow.survey.label.question' => '質問文',
	'user_flow.survey.label.option' => '選択肢1',
	'user_flow.survey.label.free_space' => 'フリースペース',
	'user_flow.survey.label.title' => 'タイトル',
	'user_flow.survey.label.check' => '実際のUIはプレビューからご確認をお願いします',

	// ヘルススコア
	'admin.healthscoremenu.label.healthscore' => 'Health Score',
	'admin.healthscoremenu.label.healthscore_login' => 'Login Count',
	'admin.healthscoremenu.label.healthscore_statview' => 'Statistics View Count',
	'admin.healthscoremenu.label.healthscore_verybotuse' => 'Very Bot Usage Count',
	'admin.healthscoremenu.label.healthscore_veryupdate' => 'VERY Update Count',
	'admin.healthscoremenu.label.healthscore_faqadd' => 'FAQ Add Count',
	'admin.healthscoremenu.label.healthscore_faqedit' => 'FAQ Edit Count',
	'admin.healthscoremenu.label.healthscore_noticeview' => 'Notice View Count',
	'admin.healthscoremenu.label.healthscore_chatbot_users' => 'CHATBOT Usage Count',

	// Disaster Mode
	'admin.disaster.label.display_setup' => 'Display Setup',
	'admin.disaster.label.bubble_setup' => 'Bubble Setup',
	'admin.disaster.label.welcome_setup' => 'Welcom Message Setup',
	'admin.disaster.label.bubble_disaster_mode' => 'Bubble (Disaster Mode)',
	'admin.disaster.label.displaying' => 'Displaying',
	'admin.disaster.label.not_displaying' => 'Not Displaying',
	'admin.disaster.label.preview_content' => 'Preview Content',
	'admin.disaster.label.edit' => 'Edit',
	'admin.disaster.label.bot_introduction_disaster_mode' => 'Bot Introduction (Disaster Mode)',
	'admin.disaster.label.bot_introduction_nearby_disaster' => 'Bot Introduction (Disaster Mode_Nearby Disaster)',
	'admin.disaster.label.disaster_mode' => 'Disaster Mode',
	'admin.disaster.warning.now_applying' => 'Disaster Mode is in use',
	'admin.disaster.warning.link_to_cancel' => 'Turn off Disaster Mode',

	// チャットボット利用状況
	'admin.chatbot.report.user_statistics' => 'User statistics',
	'admin.chatbot.report.conversation_statistics' => 'Conversations statistics',

	// AdminBatch
    'admin.batch.label.execution_date_range' => 'Execution Date Range',
    'admin.batch.label.execution_date' => 'Execution Date',
    'admin.batch.label.batch' => 'Batch',
    'admin.batch.label.type' => 'Type',
    'admin.batch.label.status' => 'Status',
    'admin.batch.label.priority' => 'Priority',
    'admin.batch.label.details' => 'Details',
    'admin.batch.label.batch_selection' => 'Select Batch',
    'admin.batch.label.execution_results' => 'Execution Results',
    'admin.batch.label.execution_time' => 'Execution Time',
    'admin.batch.label.success' => 'Success',
    'admin.batch.label.failed' => 'Failed',
    'admin.batch.label.error_info' => 'Error Information',
    'admin.batch.label.process_id' => 'Process ID',
    'admin.batch.label.created_by' => 'Created By',
    'admin.batch.label.updated_by' => 'Updated By',
    'admin.batch.button.run.manual' => 'Manual Execution',
    'admin.batch.message.error.start_date_format' => 'Start date format is incorrect.',
    'admin.batch.message.error.end_date_format' => 'End date format is incorrect.',
    'admin.batch.message.error.date_range' => 'Start date must be before end date.',
    'admin.batch.message.error.select_batch_required' => 'Please select a batch.',
    'admin.batch.message.error.invalid_batch' => 'Invalid batch specified.',
    'admin.batch.message.error.manual_execution_not_allowed' => 'This batch cannot be executed manually.',
    'admin.batch.message.error.manual_execute_failed' => 'Failed to add manual execution task: :message',
    'admin.batch.message.error.system_error' => 'An error occurred while adding manual execution task. Please contact system administrator.',
    'admin.batch.message.success.manual_execute_added' => 'Manual execution task added, will start in next execution. Execution ID: :execution_id',
    'admin.batch.message.confirm.manual_execute_title' => 'Manual Batch Execution Confirmation',
    'admin.batch.message.confirm.manual_execute_detail' => 'Do you want to start manual batch execution: ',
    'admin.batch.message.confirm.running_batch_refresh_title' => 'Page Refresh Confirmation',
    'admin.batch.message.confirm.running_batch_refresh_detail' => 'There are running batches. Do you want to refresh the page?',
    'admin.batch.message.notification.running_batch_auto_refresh' => 'There are running batches. Refresh confirmation will be shown in 30 seconds.',
);
