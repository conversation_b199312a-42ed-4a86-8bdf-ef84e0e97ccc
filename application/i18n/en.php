<?php defined('SYSPATH') or die('No direct script access.');

return array(
	'error_page.exception' => 'An error occurred.',
	'error_page.url_not_exist' => 'Sorry, the URL you are looking for does not exist.',
	'error_page.url_delete' => 'The URL you are looking for has been deleted.',
	'error_page.receipt_cancelled' => 'You cannot download receipts for cancelled reservations.',
	'error_page.receipt_time' => 'You cannot download an invoice for an unused reservation.',

	'amazonpay.label.amount' => 'Total amount',
	'amazonpay.label.pay_method' => 'Payment',
	'amazonpay.label.change_method' => 'Change',
	'amazonpay.button.confirm' => 'Confirm',


	'faq.button.home' => 'TOP',
	'faq.button.switch' => 'Switch category',
	'faq.title.category' => 'Category',
	'faq.title.top' => 'FAQ',
	'faq.title.keywords' => 'Search by frequently used keywords',
	'faq.title.faq_ai' => 'AI recommended questions',
	'faq.title.faq_most' => 'Frequently asked questions',
	'faq.title.faq_result' => 'Search results',
	'faq.title.faq_tag' => 'Tag results',
	'faq.title.no_result' => 'No results',
	'faq.title.faq_category' => 'in FAQ',
	'faq.title.faq_relation' => 'Related questions',
	'faq.title.category_search' => 'Choose category',
	'faq.title.contents' => 'Check the recommended information below',
	'faq.placeholder.keyword' => 'Please enter your question',
	'faq.button.search' => 'Search',
	'faq.title.contact' => '※Please try here if you cannot find it in the FAQ',
	'faq.button.contact' => 'Inquiry→',
	'faq.button.up' => 'Back to TOP',
	'faq.title.page' => ' Page',
	'faq.title.page.total' => ' items found: displaying ',
	'faq.title.page.range' => ' items',
	'faq.title.fontsize' => 'Text Size',
	'faq.title.close' => 'Close',
	'faq.button.fontsize.large' => 'Large',
	'faq.button.fontsize.normal' => 'Normal',
	'faq.button.fontsize.small' => 'Small',
	'faq.title.detail' => 'Please click below for the details.',
	'faq.title.detail2' => 'We will show you information on each hotel.<br/>Please click on the "Details" button below for further details.',
	'faq.button.detail' => 'Details',
	'faq.title.survey1' => 'Please tell us how you felt so we can improve our service.',
	'faq.title.survey2' => 'Was the answer helpful?',
	'faq.title.survey_fin' => 'Thank you for answering the survey.',
	'faq.title.survey_input_1' => 'What did you think of the service?',
	'faq.title.survey_input_2' => 'Please do not post your questions here as we would not be able to respond through this channel.',
	'faq.button.back' => 'Back',
	'faq.button.survey.yes' => 'Good',
	'faq.button.survey.no' => 'Bad',
	'faq.button.survey.send' => 'Send',
	'faq.label.left_bracket' => '"',
	'faq.label.right_bracket' => '"',
	'faq.button.show_inquiry' => 'To Inquiry Form',
	'faq.text.show_inquiry' => 'If cannot find your question, please contact us here.',
	'faq.auto_translate_tip' => 'This content has been generated by machine translation and is for reference only.',

	// SURVEY INQUIRY 共通
	'survey.inquiry.common.edit' => 'Edit questions',
	'survey.inquiry.common.list' => 'Questions',
	'survey.inquiry.common.label.required' => 'Required',
	'survey.inquiry.common.label.item.add' => 'Add a question',
	'survey.inquiry.common.delete.item.title' => 'Do you want to delete the question?',
	'survey.inquiry.common.delete.item' => 'Delete the question',
	// 項目内
	'survey.inquiry.common.item.placeholder.title' => 'Please enter the question title',
	'survey.inquiry.common.item.to_edit_HTML' => 'Edit title',
	'survey.inquiry.common.item.edit_HTML.title' => 'Edit title',
	'survey.inquiry.common.item.image.add' => 'Add image',
	'survey.inquiry.common.item.image.add.title' => 'Add image',
	'survey.inquiry.common.item.image.confirm.display_none' => 'Do you want to hide "Add image"?',
	'survey.inquiry.common.item.image.confirm.delete' => 'Do you want to delete the image?',
	'survey.inquiry.common.item.image.confirm.delete.title' => 'The image saved will be deleted.',
	'survey.inquiry.common.item.title.new_page' => 'A new page will start from this question.',
	'survey.inquiry.common.item.select.max' => 'Maximum number of choices',
	'survey.inquiry.common.item.select.min' => 'Minimum number of choices',
	'survey.inquiry.common.item.add.option' => 'Add choices',
	'survey.inquiry.common.item.option.or' => 'or',
	'survey.inquiry.common.item.add.other' => 'add Other',
	'survey.inquiry.common.item.option.placeholder' => 'Choices {num}',

	// 形式
	'survey.inquiry.common.opt' => 'Single choice',
	'survey.inquiry.common.chk' => 'Multiple choice',
	'survey.inquiry.common.sel' => 'Dropdown',
	'survey.inquiry.common.txt' => 'Short answer',
	'survey.inquiry.common.txa' => 'Long answer',
	'survey.inquiry.common.fup' => 'Upload file',
	'survey.inquiry.common.frs' => 'Free space',
	'survey.inquiry.common.mtx' => 'Matrix',
	'survey.inquiry.common.fleq' => 'FAQ',
	'survey.inquiry.common.spl.address.prefecture_city' => 'Prefecture + City',

	// txt, txa
	'survey.inquiry.common.txt.text' => 'Free input',
	'survey.inquiry.common.txt.num' => 'Number',
	'survey.inquiry.common.txt.country' => 'Country/Region',
	'survey.inquiry.common.txt.postcode' => 'Postal code',
	'survey.inquiry.common.txt.prefecture' => 'Prefecture',
	'survey.inquiry.common.txt.city' => 'City',
	'survey.inquiry.common.txt.city.placeholder' => '〇〇 City',
	'survey.inquiry.common.txt.street' => 'Address 1',
	'survey.inquiry.common.txt.room' => 'Name of building/Room number',
	'survey.inquiry.common.txa.min' => 'minimum characters',
	'survey.inquiry.common.txa.max' => 'maximum characters',
	'survey.inquiry.common.txt.reconfirm.mail' => 'Confirm email address',
	'survey.inquiry.common.txt.mail.sendmail' => 'Send this mail',
	'survey.inquiry.common.txa.placeholder' => 'Long answer (within 2000 characters)',

	// マトリクス
	'survey.inquiry.common.mtx.title.column' => 'Title (row)',
	'survey.inquiry.common.mtx.option.column' => 'Choices (row)',
	'survey.inquiry.common.mtx.title.num' => 'Title {num}',
	'survey.inquiry.common.mtx.add.title' => 'Add a title',
	'survey.inquiry.common.mtx.select.num' => '{num}',

	// セクション
	'survey.inquiry.common.label.section' => 'Section',
	'survey.inquiry.common.section.delete' => 'Delete section',
	'survey.inquiry.common.section.delete.confirm' => 'Do you want to delete the section?',
	'survey.inquiry.common.section.fold_up' => 'Fold the section',
	'survey.inquiry.common.label.section.add' => 'Add a section',

	// 分岐条件
	'survey.inquiry.common.branch.set' => 'Set branches',
	'survey.inquiry.common.branch.select.title' => 'Please choose a queston',
	'survey.inquiry.common.branch.select.answer' => 'Please choose an answer',
	'survey.inquiry.common.branch.add.and' => 'Please add a branch (AND)',
	'survey.inquiry.common.branch.delete' => 'Do you want to delete the branch?',
	'survey.inquiry.common.branch.add' => 'Add a branch',
	'survey.inquiry.common.branch.label.or' => 'or',
	'survey.inquiry.common.branch.destination' => 'Transition to: ',

	'survey.html.title' => 'talkappi Survey',
	'survey.index.title' => 'talkappi Survey',
	'survey.index.title.name' => 'Survey name',
	'survey.index.label.content' => 'Introduction',
	'survey.index.label.content.name' => 'Survey summary',
	'survey.index.label.period' => 'Research period',
	'survey.index.label.count' => 'Number of questions',
	'survey.index.label.count.content' => 'Max. {count} questions',
	'survey.index.label.duration' => 'Survey length',
	'survey.index.label.duration.content' => 'about {duration} min',
	'survey.index.label.present' => 'Gift',
	'survey.index.label.remark' => 'This research is held by the operating company. The answers will be analyzed without identifying the individual.',
	'survey.index.label.expired' => 'This research has ended',
	'survey.index.label.limit' => 'New applications are no longer taken as the number of responses have reached the limit.',
	'survey.index.button.answer' => 'Answer',
	'survey.input.label.must' => '*Required',
	'survey.input.button.prev' => 'Back',
	'survey.input.button.next' => 'Next',
	'survey.input.button.send' => 'Send',
	'survey.input.label.required.not.input' => 'Please check that all answers are filled in.',
	'survey.input.label.required.not.input.eachquestion' => 'This is a required field.',
	'survey.input.text.length' => 'Must be between {min_length} ~ {max_length} letters.',
	'survey.input.text.max_length' => 'Must be under {max_length} characters.',
	'survey.input.text.min_length' => 'Must be at least {min_length} characters.',
	'survey.input.text.tel' => '08012345678',
	'survey.input.text.address' => '123-4567(Hyphen (-) can be omitted)',
	'survey.input.text.num' => 'Please enter a number',
	'survey.input.text.num.dash' => 'Please enter a number(Hyphen (-) can be omitted)',
	'survey.input.text.email' => 'Please enter your email address',
	'survey.input.text.email.confirm' => '(Re-enter)',
	'survey.input.text.email.confirm.error' => 'The email address does not match',
	'survey.input.text.mtx.error' => 'Please answer each row',
	'survey.input.text.mtx.chk.message' => 'Please choose {max_min} from each row',
	'survey.input.text.chk.message' => 'Please choose {max_min}',
	'survey.input.text.chk.error' => 'Please check the choices.',
	'survey.input.text.mtx.chk.min' => '{min} minimum',
	'survey.input.text.mtx.chk.max' => '{max} maximum',
	'survey.input.text.mtx.chk.num' => '{num}',
	'survey.input.text.date' => 'Please enter information following the example.',
	'survey.input.text.fup' => 'Select file',
	'survey.input.text.sel' => 'Please select',
	'survey.complete.title' => 'Survey complete',
	'survey.complete.label.thanks' => "Thank you for taking your time<br> in answering the survey",
	'survey.complete.label.unusable' => "This coupon is out of date. <br>Valid dates:{start_date}~{end_date}<br>Please send the coupon to your email address by clicking the 'Save/send coupon' button.",
	'survey.complete.button.coupon' => 'See coupons',
	'survey.complete.button.close' => 'Back to chat',
	'survey.complete.button.closewindow' => 'Close',
	'survey.coupon.label.period1' => '{days} days left',
	'survey.coupon.label.period2' => 'Expiration date: {date}',
	'survey.coupon.label.period3' => 'Stating date: {date}',
	'survey.coupon.label.period4' => 'Expiration date: {date} days after the coupon is issued<br>*Issued when the coupon is opened during the valid dates.',
	'survey.coupon.label.period5' => 'Valid dates: {date} days from today',
	'survey.coupon.label.unlimited.usable' => '※This coupon can be used multiple times during the available period.',
	'survey.coupon.label.limited.usable' => 'Usable: {num}times',
	'survey.coupon.label.present' => 'Gift coupon',
	'survey.coupon.label.remark' => 'The expiration date and time of the coupons are set at UTC+09:00.',
	'survey.coupon.button.use' => 'Use the coupon',
	'survey.coupon.dialog.title' => 'Would you like to use this coupon?',
	'survey.coupon.dialog.content' => 'The usage status will change once you click ”Yes”. Please show the screen to the staff before your payment.',
	'survey.coupon.label.expired' => 'This coupon has expired',
	'survey.coupon.label.notstart' => 'This coupon cannot be used yet. <br> Please try again after once the dates have become valid',
	'survey.coupon.label.used' => 'This coupons has been used',
	'survey.coupon.label.maxused' => 'This coupon has reached its use limit',
	'survey.coupon.label.canotuse' => 'You do not have this coupon',
	'survey.coupon.label.issue' => 'Gifts are no longer available as the number of coupons have reached the limit.',
	'survey.coupon.expire.after.issue' => '{date} days after issue',
	'survey.common.label.required' => 'Required',
	'survey.common.label.input' => 'Please enter your answer',
	'survey.common.label.other' => 'Other',
	'survey.common.label.other.input' => 'Please enter your answer',
	'survey.common.label.other.input.required' => '(Required)',
	'survey.common.label.other.input.optional' => '',
	'survey.branch.select.equal' => 'equal to',
	'survey.branch.select.not_equal' => 'not equal to',
	'survey.branch.select.include' => 'include',
	'survey.branch.select.not_include' => 'do not include',
	'survey.branch.select.greater' => 'Larger than',
	'survey.branch.select.smaller' => 'Smaller than',
	'survey.branch.select.equal_or_greater' => 'or more',
	'survey.branch.select.equal_or_smaller' => 'or less',

	'inquiry.html.title' => 'talkappi Inquiry',
	'inquiry.index.title' => 'talkappi Inquiry',
	'inquiry.index.label.content' => 'Introduction',
	'inquiry.index.label.period' => 'Service period',
	'inquiry.index.label.count' => 'Number of questions',
	'inquiry.index.label.count.content' => 'Max. {count} questions',
	'inquiry.index.label.duration' => 'Inquiry length',
	'inquiry.index.label.duration.content' => 'about {duration} min',
	'inquiry.index.label.present' => 'Gift',
	'inquiry.index.label.remark' => 'This research is held by the operating company. The answers will be analyzed without identifying the individual.',
	'inquiry.index.label.invalid.title' => 'Service Restriction',
	'inquiry.index.label.invalid.description' => 'We are sorry.<br>The form cannot be processed <br>as this service is currently unavailable.',
	'inquiry.index.label.versionup.description' => 'Unfortunately, this form has expired.<br>It will switch to the new form after 10 seconds.<br>Please enter the details again after the new form is displayed. We apologize for the inconvenience.',
	'inquiry.index.label.inquiry_answer_limit.description' => 'Entry has closed as the number of people has reached the limit.',
	'inquiry.index.label.answer_limit.description' => 'Your entry has been submitted already.<br>Thank you for your cooperation.',
	'inquiry.index.button.answer' => 'Answer',
	'inquiry.input.label.must' => '*Required',
	'inquiry.input.button.send' => 'Send',
	'inquiry.input.button.prev' => 'Edit',
	'inquiry.input.button.pay' => 'Pay',
	'inquiry.input.button.back' => 'Back',
	'inquiry.input.button.confirm' => 'Confirm information',
	'inquiry.input.button.modify' => 'Change reservation',
	'inquiry.input.button.cancel' => 'Cancel reservation',
	'inquiry.modify.error' => 'This reservation is not eligible for online changes. Please contact the facility.',
	'inquiry.cancel.error.check' => 'The cancellation period has ended. Please contact the facility directly.',
	'inquiry.cancel.error.canceled' => 'This reservation has been canceled already.',
	'inquiry.cancel.complete' => 'The reservation has been canceled.',
	'inquiry.cancel.complete.desc' => 'The details have been sent to your registered email address.<br>Thank you for using our service.',
	'inquiry.input.label.required.not.input' => 'Please check that all answers are filled in.',
	'inquiry.input.label.required.not.input.eachquestion' => 'This is a required field.',
	'inquiry.input.transition.error' => 'When using the automatic translation function of the browser, some contents of the form may not be displayed correctly. Please turn off the automatic translation function.',
	'inquiry.input.message.no-pay-reserve' => 'Your reservation payment is incomplete. Please complete your payment on the payment page. If you wish to cancel your reservation and make a new one, please choose “Make a new reservation”',
	'inquiry.input.button.cancel-no-pay-reserve' => 'Make a new reservation',
	'inquiry.input.button.continue-no-pay-reserve' => 'Continue reservation',
	'inquiry.input.button.close' => 'Close',
	'inquiry.input.text.length' => 'Must be between {min_length} ~ {max_length} letters.',
	'inquiry.input.text.max_length' => 'Must be under {max_length} characters.',
	'inquiry.input.text.min_length' => 'Must be at least {min_length} characters.',
	'inquiry.input.text.num.minmax' => 'Please enter a number between {min}~{max}.',
	'inquiry.input.text.num.max' => 'Please enter a number under {max}.',
	'inquiry.input.text.num.min' => 'Please enter a number over {min}.',
	'inquiry.input.text.tel' => '*****-123-4567',
	'inquiry.input.text.address' => '123-4567(Hyphen (-) can be omitted)',
	'inquiry.input.text.num' => 'Please enter a number',
	'inquiry.input.text.num.dash' => 'Please enter a number(Hyphen (-) can be omitted)',
	'inquiry.input.text.email' => 'Please enter your email address',
	'inquiry.input.text.email.confirm' => '(Re-enter)',
	'inquiry.input.text.email.confirm.error' => 'The email address does not match',
	'inquiry.input.text.clock' => 'Please enter the time correctly.',
	'inquiry.input.text.date' => 'Please enter information following the example.',
	'inquiry.input.text.fup' => 'Select file',
	'inquiry.input.text.sel' => 'Please select',
	'inquiry.input.text.other.confirm' => 'Other',
	'inquiry.input.text.txt.date' => 'Date',
	'inquiry.input.text.txt.time' => 'Time',
	'inquiry.input.text.txt.time1' => 'Time',
	'inquiry.input.text.txt.start_date' => 'Start date',
	'inquiry.input.text.txt.start_time' => 'Start time',
	'inquiry.input.text.txt.end_date' => 'End date',
	'inquiry.input.text.txt.end_time' => 'End time',
	'inquiry.input.spl.address.roomNo' => '(optional)',
	'inquiry.input.spl.address.country.label' => 'Country/Region',
	'inquiry.input.spl.address.postcode.label' => 'ZIP/Postal code',
	'inquiry.input.spl.address.prefecture.label' => 'Prefecture',
	'inquiry.input.spl.address.city.label' => 'City',
	'inquiry.input.spl.address.street.label' => 'Street address',
	'inquiry.input.spl.address.room.label' => 'Building, floor, room number',
	'inquiry.input.spl.name.full' => 'Full name',
	'inquiry.input.spl.name.first' => 'First name',
	'inquiry.input.spl.name.last' => 'Last name',
	'inquiry.input.price.tax' => '(Tax included)',
	'inquiry.input.price.tax-service' => '(Tax and service included)',
	'inquiry.complete.title' => 'Inquiry complete',
	'inquiry.complete.label.thanks' => "Thank you for taking your time<br> in answering the Inquiry",
	'inquiry.complete.label.period' => "Expiration date: Until {date}",
	'inquiry.complete.button.coupon' => 'See gift coupons',
	'inquiry.complete.button.close' => 'Back to chat',
	'inquiry.complete.button.closewindow' => 'Close',
	'inquiry.complete.error.overtime_title' => 'Reservaton error',
	'inquiry.complete.error.overtime' => 'The reservation has been canceled as the payment was not completed within the given time.',
	'inquiry.complete.button.input' => 'Back to Top',
	'inquiry.coupon.label.period1' => '{days} days left',
	'inquiry.coupon.label.period2' => 'Usable until {date}',
	'inquiry.coupon.label.present' => 'Gift coupon',
	'inquiry.coupon.label.remark' => 'The expiration date and time of the coupons are set at UTC+09:00.',
	'inquiry.coupon.button.use' => 'Use the coupon',
	'inquiry.coupon.dialog.title' => 'Would you like to use this coupon?',
	'inquiry.coupon.dialog.content' => 'The usage status will change once you click ”Yes”. Please show the screen to the staff before your payment.',
	'inquiry.coupon.label.expired' => 'This coupon has expired',
	'inquiry.coupon.label.notstart' => 'Unused coupons',
	'inquiry.coupon.label.used' => 'This coupons has been used',
	'inquiry.coupon.label.maxused' => 'This coupon has reached its use limit',
	'inquiry.coupon.label.canotuse' => 'You do not have this coupon',
	'inquiry.common.label.required' => 'Required',
	'inquiry.common.label.input' => 'Please enter your answer',
	'inquiry.common.label.input.longtext' => 'Please enter less than 2000 characters',
	'inquiry.common.label.input.longtext2' => 'The input text is too long. Please enter within 800 characters.',
	'inquiry.common.label.other' => 'Other',
	'inquiry.common.label.other.input' => 'Please enter your answer',
	'inquiry.common.label.other.input.error' => 'Please enter the details',
	'inquiry.common.label.limit.num.error' => 'The number of maximum reservations {unit} has been exceeded. Please select the quantity again.',
	'inquiry.common.label.limit.num.min.error' => 'ご予約可能な予約数{unit}を下回っているため、数量を選択し直してください。',
	'inquiry.common.label.reserve.alone.error' => 'Your selected type cannot be reserved independently.',
	'inquiry.common.label.other.input.required' => '(Required)',
	'inquiry.common.label.other.input.optional' => '',
	'inquiry.common.tab.step1' => 'Customer information',
	'inquiry.common.tab.step2' => 'Confirm',
	'inquiry.common.tab.step2.payment' => 'Confirm & pay',
	'inquiry.common.tab.step3' => 'Complete',
	'inquiry.common.complete.title' => 'Your request has been sent',
	'inquiry.common.complete.urlbtn' => 'Return to website',
	'inquiry.common.complete.description' => 'Thank you for your inquiry. We will usually reply within 3 business days.',
	'inquiry.login.title' => 'Restricted page',
	'inquiry.login.description' => 'Enter password',
	'inquiry.login.error' => 'Please enter the correct password.',
	'inquiry.login.placeholder' => 'Password',
	'inquiry.login.button' => 'Sign in',
	'inquiry.faq.button' => 'FAQ',
	'inquiry.faq.button.research' => 'Retrieve',
	'inquiry.label.num' => 'Quantity',
	'inquiry.label.close' => 'Close',
	'inquiry.label.expand' => 'Expand',
	'inquiry.button.coupon_apply' => 'Apply',

	'coupon.dialog.remark' => 'If you have not received the email, please check your spam folder or security settings and try again.',
	'coupon.dialog.title' => 'Save/send coupon',
	'coupon.dialog.content.link' => 'Copy and share the coupon link',
	'coupon.dialog.copy' => 'Copy',
	'coupon.dialog.content.send' => 'Send coupon by email',
	'coupon.dialog.mail' => 'Enter email address',
	'coupon.dialog.button' => 'Send',
	'coupon.dialog.mail.success' => 'The email has been sent.',
	'coupon.dialog.mail.fail' => 'Please enter a valid email address',
	'coupon.button.yes' => 'Use coupon',
	'coupon.button.no' => 'Not now',
	'coupon.dialog.use.store' => 'Place of use',
	'coupon.dialog.use.title' => "Please present this screen to the store staff.",
	'coupon.dialog.use.staff' => "<Store Staff>",
	'coupon.dialog.use.staff.remark' => "Pressing 'Use' will mark the coupon as used, and it cannot be undone. Store staff should press the 'Use' button to confirm the coupon's usage.",
	'coupon.facility.choose' => 'Select a facility',
	'coupon.dialog.copy.success' => 'The link has been copied.',
	'coupon.label.all.maxused' => 'This coupon has reached the maximum usage limit and cannot be used.',

	'coupon.title.usage_information' => 'Usage information',
	'coupon.title.usage_information_in_english' => 'Usage information',

	'coupon.lottery_challenge' => 'Try the Lottery',
	'coupon.lottery' => 'Drawing...',
	'coupon.lose' => 'Not a Winner...',
	'coupon.lose_message' => "Sorry, you didn't win this time...<br>Try again for another chance!",
	'coupon.lose_button' => 'Close',
	'coupon.lase.participation.message' => 'You have already participated in the lottery',

	'coupon.coupon_available.title' => 'You have received coupons',
	'coupon.error.deleted_error_title' => 'There are no usable coupons.',
	'coupon.error.deleted_error_description' => 'Unfortunately, <br/>the coupon has been deleted.',

	'common.error.front.title' => 'An error occurred.',
	'common.error.front.sub_title' => 'Please try again later.',

	'admin.batch.button.run.manual'=> 'Run Manually',
	'admin.batch.label.page_title' => 'Batch Execution Status List',
	'admin.batch.label.execution_date_range' => 'Execution Date Range',
	'admin.batch.label.batch_type' => 'Batch Type',
	'admin.batch.label.execution_status' => 'Execution Status',
	'admin.batch.label.batch_select' => 'Select Batch',
	'admin.batch.label.manual_execute' => 'Manual Execute',
	'admin.batch.label.execution_date' => 'Execution Date',
	'admin.batch.label.batch_name' => 'Batch',
	'admin.batch.label.type' => 'Type',
	'admin.batch.label.status' => 'Status',
	'admin.batch.label.priority' => 'Priority',
	'admin.batch.label.details' => 'Details',
	'admin.batch.label.keyword_placeholder' => 'Batch name, type, etc.',
	'admin.batch.label.status_pending' => 'Pending',
	'admin.batch.label.status_running' => 'Running',
	'admin.batch.label.status_success' => 'Success',
	'admin.batch.label.status_failed' => 'Failed',
	'admin.batch.label.status_skipped' => 'Skipped',
	'admin.batch.label.status_unknown' => 'Unknown',
	'admin.batch.message.error.start_date_format' => 'Start date format is incorrect.',
	'admin.batch.message.error.end_date_format' => 'End date format is incorrect.',
	'admin.batch.message.error.date_range' => 'Start date must be before end date.',
	'admin.batch.message.error.select_batch_required' => 'Please select a batch.',
	'admin.batch.message.error.invalid_batch' => 'Invalid batch specified.',
	'admin.batch.message.error.manual_execution_not_allowed' => 'This batch cannot be executed manually.',
	'admin.batch.message.error.manual_execution_failed' => 'Failed to add manual execution task: ',
	'admin.batch.message.error.system_error' => 'An error occurred while adding manual execution task. Please contact system administrator.',
	'admin.batch.message.success.manual_execute_added_with_id' => 'Manual execution task added, will start in next execution. Execution ID: ',
	'admin.batch.label.duration_hours_minutes_seconds' => '%d hours %d minutes %d seconds',
	'admin.batch.label.duration_minutes_seconds' => '%d minutes %d seconds',
	'admin.batch.label.duration_seconds' => '%d seconds',

);
