<?php defined('SYSPATH') or die('No direct script access.');

return array(

	// AdminBatch
    'admin.batch.label.execution_date_range' => 'Execution Date Range',
    'admin.batch.label.execution_date' => 'Execution Date',
    'admin.batch.label.batch' => 'Batch',
    'admin.batch.label.type' => 'Type',
    'admin.batch.label.status' => 'Status',
    'admin.batch.label.priority' => 'Priority',
    'admin.batch.label.details' => 'Details',
    'admin.batch.label.batch_selection' => 'Select Batch',
    'admin.batch.label.execution_results' => 'Execution Results',
    'admin.batch.label.execution_time' => 'Execution Time',
    'admin.batch.label.success' => 'Success',
    'admin.batch.label.failed' => 'Failed',
    'admin.batch.label.error_info' => 'Error Information',
    'admin.batch.label.process_id' => 'Process ID',
    'admin.batch.label.created_by' => 'Created By',
    'admin.batch.label.updated_by' => 'Updated By',
    'admin.batch.button.run.manual' => 'Manual Execution',
    'admin.batch.message.error.start_date_format' => 'Start date format is incorrect.',
    'admin.batch.message.error.end_date_format' => 'End date format is incorrect.',
    'admin.batch.message.error.date_range' => 'Start date must be before end date.',
    'admin.batch.message.error.select_batch_required' => 'Please select a batch.',
    'admin.batch.message.error.invalid_batch' => 'Invalid batch specified.',
    'admin.batch.message.error.manual_execution_not_allowed' => 'This batch cannot be executed manually.',
    'admin.batch.message.error.manual_execute_failed' => 'Failed to add manual execution task: :message',
    'admin.batch.message.error.system_error' => 'An error occurred while adding manual execution task. Please contact system administrator.',
    'admin.batch.message.success.manual_execute_added' => 'Manual execution task added, will start in next execution. Execution ID: :execution_id',
    'admin.batch.message.confirm.manual_execute_title' => 'Manual Batch Execution Confirmation',
    'admin.batch.message.confirm.manual_execute_detail' => 'Do you want to start manual batch execution: ',
    'admin.batch.message.confirm.running_batch_refresh_title' => 'Page Refresh Confirmation',
    'admin.batch.message.confirm.running_batch_refresh_detail' => 'There are running batches. Do you want to refresh the page?',
    'admin.batch.message.notification.running_batch_auto_refresh' => 'There are running batches. Refresh confirmation will be shown in 30 seconds.',
);
