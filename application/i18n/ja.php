<?php defined('SYSPATH') or die('No direct script access.');

return array(
	'error_page.exception' => '申し訳ございません。エラーが発生しました。',
	'error_page.url_not_exist' => '申し訳ございません。該当のURLは存在しません。',
	'error_page.url_delete' => 'このページは削除されました。',
	'error_page.receipt_cancelled' => 'キャンセル済予約の領収書はダウンロードできません。',
	'error_page.receipt_time' => '未使用の予約の請求書はダウンロードできません。',

	'amazonpay.label.amount' => 'お支払い金額',
	'amazonpay.label.pay_method' => '支払い方法',
	'amazonpay.label.change_method' => '変更',
	'amazonpay.button.confirm' => '支払する',

	'faq.button.home' => 'ホーム',
	'faq.button.switch' => '切替',
	'faq.title.category' => 'カテゴリ選択',
	'faq.title.top' => 'よくあるご質問',
	'faq.title.keywords' => 'よく使われるキーワードから検索',
	'faq.title.faq_ai' => 'AIがおすすめする質問',
	'faq.title.faq_most' => '閲覧の多い質問',
	'faq.title.faq_result' => 'で検索した結果',
	'faq.title.faq_tag' => 'タグの結果',
	'faq.title.no_result' => '検索結果がありませんでした',
	'faq.title.faq_category' => '内のFAQ',
	'faq.title.faq_relation' => '関連質問',
	'faq.title.category_search' => 'カテゴリ選択',
	'faq.title.contents' => 'おすすめの情報が見つかりました',
	'faq.placeholder.keyword' => 'ご質問を入力してください',
	'faq.button.search' => '検索する',
	'faq.title.contact' => '※FAQに無いご質問は、こちらをお試しください',
	'faq.button.contact' => '問合せ→',
	'faq.button.up' => 'TOPへ',
	'faq.title.page' => 'ページ',
	'faq.title.page.total' => '件中　',
	'faq.title.page.range' => '件を表示',
	'faq.title.fontsize' => '文字サイズ',
	'faq.title.close' => '閉じる',
	'faq.button.fontsize.large' => '大',
	'faq.button.fontsize.normal' => '中',
	'faq.button.fontsize.small' => '小',
	'faq.title.detail' => '詳細はこちらをご確認ください。',
	'faq.title.detail2' => '各ホテルごとの情報をご案内します。<br/>詳しくは下記の「詳細」ボタンよりチェックしてください。',
	'faq.button.detail' => '詳細へ',
	'faq.title.survey1' => '改善のために、ご意見をお聞かせください。',
	'faq.title.survey2' => 'このFAQの回答は役に立ちましたか？',
	'faq.title.survey_fin' => 'アンケートのご協力ありがとうございました。',
	'faq.title.survey_input_1' => 'ご意見・ご感想をお寄せください',
	'faq.title.survey_input_2' => '※ご返信はいたしかねますのでご了承ください',
	'faq.button.back' => '戻る',
	'faq.button.survey.yes' => 'はい',
	'faq.button.survey.no' => 'いいえ',
	'faq.button.survey.send' => '送信する',
	'faq.label.left_bracket' => '「',
	'faq.label.right_bracket' => '」',
	'faq.button.show_inquiry' => 'お問い合わせはこちら',
	'faq.text.show_inquiry' => 'お探しの質問が見つからない場合、こちらよりお問い合わせください。',
	'faq.auto_translate_tip' => 'この回答は自動翻訳によって生成されたものであり、参考用です。',

	// SURVEY INQUIRY 共通
	'survey.inquiry.common.edit' => '設問編集',
	'survey.inquiry.common.list' => '設問一覧',
	'survey.inquiry.common.label.required' => '回答必須',
	'survey.inquiry.common.label.item.add' => '設問を追加する',
	'survey.inquiry.common.delete.item.title' => '設問を削除しますか？',
	'survey.inquiry.common.delete.item' => '設問を削除する',
	// 項目内
	'survey.inquiry.common.item.placeholder.title' => '設問タイトルを入力してください',
	'survey.inquiry.common.item.to_edit_HTML' => 'タイトル編集へ',
	'survey.inquiry.common.item.edit_HTML.title' => 'タイトル編集',
	'survey.inquiry.common.item.image.add' => '画像追加',
	'survey.inquiry.common.item.image.add.title' => '画像を追加する',
	'survey.inquiry.common.item.image.confirm.display_none' => '画像追加を非表示にしますか？',
	'survey.inquiry.common.item.image.confirm.delete' => '画像を削除しますか？',
	'survey.inquiry.common.item.image.confirm.delete.title' => '保存した画像は削除されます。',
	'survey.inquiry.common.item.title.new_page' => 'この質問から新しいページを開始します',
	'survey.inquiry.common.item.select.max' => '最大選択数',
	'survey.inquiry.common.item.select.min' => '最低選択数',
	'survey.inquiry.common.item.add.option' => '選択肢を追加する',
	'survey.inquiry.common.item.option.or' => 'または',
	'survey.inquiry.common.item.add.other' => '「その他」を追加する',
	'survey.inquiry.common.item.option.placeholder' => '選択肢{num}',

	// 形式
	'survey.inquiry.common.opt' => '単一選択',
	'survey.inquiry.common.chk' => '複数選択',
	'survey.inquiry.common.sel' => 'プルダウン',
	'survey.inquiry.common.txt' => '短文',
	'survey.inquiry.common.txa' => '長文',
	'survey.inquiry.common.fup' => 'ファイルアップロード',
	'survey.inquiry.common.frs' => 'フリースペース',
	'survey.inquiry.common.mtx' => 'マトリクス',
	'survey.inquiry.common.fleq' => 'よく聞く質問',
	'survey.inquiry.common.spl.address.prefecture_city' => '都道府県 + 市区町村',

	// txt, txa
	'survey.inquiry.common.txt.text' => '自由入力',
	'survey.inquiry.common.txt.num' => '数字',
	'survey.inquiry.common.txt.country' => '国・地域',
	'survey.inquiry.common.txt.postcode' => '郵便番号',
	'survey.inquiry.common.txt.prefecture' => '都道府県',
	'survey.inquiry.common.txt.city' => '市区町村',
	'survey.inquiry.common.txt.city.placeholder' => '〇〇市',
	'survey.inquiry.common.txt.street' => '番地',
	'survey.inquiry.common.txt.room' => '建物名・部屋番号',
	'survey.inquiry.common.txa.min' => '最低文字数',
	'survey.inquiry.common.txa.max' => '最大文字数',
	'survey.inquiry.common.txt.reconfirm.mail' => 'メールアドレスの再確認',
	'survey.inquiry.common.txt.mail.sendmail' => 'お客様へ送信する',
	'survey.inquiry.common.txa.placeholder' => '長文(2000字以内)',

	// マトリクス
	'survey.inquiry.common.mtx.title.column' => 'タイトル（行）',
	'survey.inquiry.common.mtx.option.column' => '選択肢（行）',
	'survey.inquiry.common.mtx.title.num' => 'タイトル{num}',
	'survey.inquiry.common.mtx.add.title' => 'タイトルを追加する',
	'survey.inquiry.common.mtx.select.num' => '{num}つ',

	// セクション
	'survey.inquiry.common.label.section' => 'セクション',
	'survey.inquiry.common.section.delete' => 'セクションを削除する',
	'survey.inquiry.common.section.delete.confirm' => 'セクションを削除しますか？',
	'survey.inquiry.common.section.fold_up' => 'セクションを折り畳む',
	'survey.inquiry.common.label.section.add' => 'セクションを追加する',

	// 分岐条件
	'survey.inquiry.common.branch.set' => '分岐条件を設定する',
	'survey.inquiry.common.branch.select.title' => '設問を選択してください',
	'survey.inquiry.common.branch.select.answer' => '回答を選択してください',
	'survey.inquiry.common.branch.add.and' => '分岐条件（AND）を追加してください',
	'survey.inquiry.common.branch.delete' => '分岐条件を削除しますか？',
	'survey.inquiry.common.branch.add' => '分岐条件を追加する',
	'survey.inquiry.common.branch.label.or' => 'OR',
	'survey.inquiry.common.branch.destination' => '遷移先：',

	'survey.html.title' => 'talkappiアンケート',
	'survey.index.title' => 'アンケート',
	'survey.index.title.name' => 'アンケート名',
	'survey.index.label.content' => '概要',
	'survey.index.label.content.name' => 'アンケート概要',
	'survey.index.label.period' => '受付期間',
	'survey.index.label.count' => '回答数',
	'survey.index.label.count.content' => '最大{count}問',
	'survey.index.label.duration' => '所要時間',
	'survey.index.label.duration.content' => '{duration}分ほど',
	'survey.index.label.present' => 'クーポン',
	'survey.index.label.remark' => 'このリサーチはアカウント運営者が実施しています。アカウント運営者は個人を特定できない形で回答結果のみを確認します。',
	'survey.index.label.expired' => 'こちらのアンケートは実施期間外です',
	'survey.index.label.limit' => 'こちらのアンケートは回答人数制限に達したため、エントリーを終了しております。',
	'survey.index.button.answer' => '回答する',
	'survey.input.label.must' => '＊ 必須',
	'survey.input.button.prev' => '前へ',
	'survey.input.button.next' => '次へ',
	'survey.input.button.send' => '送信する',
	'survey.input.text.length' => '{min_length}~{max_length}文字でご入力ください',
	'survey.input.text.max_length' => '{max_length}文字以下でご入力ください',
	'survey.input.text.min_length' => '{min_length}文字以上でご入力ください',
	'survey.input.text.tel' => '08012345678',
	'survey.input.text.address' => '123-4567（ハイフン省略可）',
	'survey.input.text.num' => '半角数字で入力してください',
	'survey.input.text.num.dash' => '半角数字で入力してください（ハイフン省略可）',
	'survey.input.text.email' => 'メールアドレスを正しく入力してください',
	'survey.input.text.email.confirm' => '(確認用)',
	'survey.input.text.email.confirm.error' => 'メールアドレスが一致しません',
	'survey.input.text.mtx.error' => 'それぞれの列をご回答ください',
	'survey.input.text.mtx.chk.message' => '各行で{max_min}選んでください',
	'survey.input.text.chk.message' => '{max_min}選んでください',
	'survey.input.text.chk.error' => '指定された数をチェックしてください',
	'survey.input.text.mtx.chk.min' => '最低{min}つ',
	'survey.input.text.mtx.chk.max' => '最大{max}つ',
	'survey.input.text.mtx.chk.num' => '{num}つ',
	'survey.input.text.date' => '入力例に従って入力してください',
	'survey.input.text.fup' => 'ファイル選択',
	'survey.input.text.sel' => '選択してください',
	'survey.input.label.required.not.input' => '未回答箇所がございますのでご確認ください。',
	'survey.input.label.required.not.input.eachquestion' => 'こちらは回答必須です。',
	'survey.complete.title' => '回答完了',
	'survey.complete.label.thanks' => "お忙しい中<br>ご回答誠にありがとうございました",
	'survey.complete.label.answered' => "既にご回答いただいております。<br>お忙しい中ご回答ありがとうございました。",
	'survey.complete.label.unusable' => "このクーポンはご利用期間外です。<br>利用期間:{start_date}~{end_date}<br>「クーポンを保存・送信」ボタンから、ご自身のメールアドレス宛にクーポンを送信してください。",
	'survey.complete.button.coupon' => 'クーポンを表示する',
	'survey.complete.button.close' => 'チャットに戻る',
	'survey.complete.button.closewindow' => '閉じる',
	'survey.coupon.label.period1' => '残り {days}日',
	'survey.coupon.label.period2' => '利用期限: {date}',
	'survey.coupon.label.period3' => '利用開始: {date}',
	'survey.coupon.label.period4' => '利用期限：クーポン発行後{date}日間<br>※利用期間内にクーポンを開封した際に発行されます。',
	'survey.coupon.label.period5' => '利用期限: 本日より{date}日間',
	'survey.coupon.label.unlimited.usable' => '※このクーポンは利用可能期間中、何回でもご利用可能です。',
	'survey.coupon.label.limited.usable' => '利用可能回数: {num}回',
	'survey.coupon.label.present' => 'お礼のクーポン',
	'survey.coupon.label.remark' => 'クーポンの有効期間日時は、UTC+09:00を基準に表示しています。',
	'survey.coupon.button.use' => 'このクーポンを利用する',
	'survey.coupon.dialog.title' => 'このクーポンを利用しますか',
	'survey.coupon.dialog.content' => '「はい」に進むと、クーポンの利用状態が更新されます。会計の前に、必ずスタッフにこの画面を確認してお願いします。',
	'survey.coupon.label.expired' => 'このクーポンは有効期限切れです',
	'survey.coupon.label.notstart' => 'ご利用期間前のクーポンです。<br>利用期間開始後、再度アクセスしてください。',
	'survey.coupon.label.used' => 'このクーポンは利用済みです',
	'survey.coupon.label.maxused' => 'このクーポンは利用上限に達しました（利用不可）',
	'survey.coupon.label.canotuse' => 'お客様のクーポンではありません',
	'survey.coupon.label.issue' => 'クーポンの発行枚数が上限に達したため、終了いたしました。',
	'survey.coupon.expire.after.issue' => '発行後{date}日間',
	'survey.common.label.required' => '必須',
	'survey.common.label.input' => '入力してください',
	'survey.common.label.other' => 'その他',
	'survey.common.label.other.input' => '詳細を入力してください',
	'survey.common.label.other.input.required' => '（必須）',
	'survey.common.label.other.input.optional' => '',
	'survey.branch.select.equal' => 'に等しい',
	'survey.branch.select.not_equal' => 'に等しくない',
	'survey.branch.select.include' => 'を含む',
	'survey.branch.select.not_include' => 'に含まない',
	'survey.branch.select.greater' => 'より大きい',
	'survey.branch.select.smaller' => 'より小さい',
	'survey.branch.select.equal_or_greater' => '以上',
	'survey.branch.select.equal_or_smaller' => '以下',


	'inquiry.html.title' => 'talkappiアンケート',
	'inquiry.index.title' => 'アンケート',
	'inquiry.index.label.content' => '概要',
	'inquiry.index.label.period' => '実施期間',
	'inquiry.index.label.count' => '回答数',
	'inquiry.index.label.count.content' => '最大{count}問',
	'inquiry.index.label.duration' => '所要時間',
	'inquiry.index.label.duration.content' => '{duration}分ほど',
	'inquiry.index.label.present' => 'クーポン',
	'inquiry.index.label.remark' => 'このリサーチはアカウント運営者が実施しています。アカウント運営者は個人を特定できない形で回答結果のみを確認します。',
	'inquiry.index.label.invalid.title' => '実施制限',
	'inquiry.index.label.invalid.description' => '申し訳ございません、<br>このフォームは実施期間外になるため、<br>受付を対応しかねます。',
	'inquiry.index.label.versionup.description' => '申し訳ございません。<br>このフォームは実施期間外となりました。<br>１０秒後に最新フォームに遷移します。<br>お手数おかけいたしますが、遷移後にもう一度フォームのご入力をお願いいたします。',
	'inquiry.index.label.inquiry_answer_limit.description' => 'こちらはエントリー人数制限に達したため、エントリーを終了しております。',
	'inquiry.index.label.answer_limit.description' => 'こちらは既にエントリーいただいております。<br>ご協力ありがとうございました。',
	'inquiry.index.button.answer' => '回答する',
	'inquiry.input.label.must' => '＊ 必須',
	'inquiry.input.button.prev' => '修正する',
	'inquiry.input.button.pay' => '支払いへ',
	'inquiry.input.button.back' => '前へ',
	'inquiry.input.button.confirm' => '確認画面へ',
	'inquiry.input.button.send' => '送信する',
	'inquiry.input.button.modify' => '予約変更',
	'inquiry.input.button.cancel' => '予約キャンセル',
	'inquiry.modify.error' => '更新できる期限は過ぎました。施設にお問い合わせください。',
	'inquiry.cancel.error.check' => 'キャンセルできる期限は過ぎました。施設にお問い合わせください。',
	'inquiry.cancel.error.canceled' => '既にキャンセル済みです。',
	'inquiry.cancel.complete' => 'ご予約はキャンセルされました。',
	'inquiry.cancel.complete.desc' => 'ご予約時に登録いただいたメールアドレスに詳細を送信いたしましたのでご確認くださいませ。<br>ご利用いただき誠にありがとうございました。',
	'inquiry.input.text.length' => '{min_length}~{max_length}文字でご入力ください',
	'inquiry.input.text.max_length' => '{max_length}文字以下でご入力ください',
	'inquiry.input.text.min_length' => '{min_length}文字以上でご入力ください',
	'inquiry.input.text.num.minmax' => '{min}~{max}の半角数字でご入力ください',
	'inquiry.input.text.num.max' => '{max}以下の半角数字でご入力ください',
	'inquiry.input.text.num.min' => '{min}以上の半角数字でご入力ください',
	'inquiry.input.text.tel' => '080-1234-5678（ハイフン省略可）',
	'inquiry.input.text.address' => '123-4567（ハイフン省略可）',
	'inquiry.input.text.num' => '半角数字で入力してください',
	'inquiry.input.text.num.dash' => '半角数字で入力してください（ハイフン省略可）',
	'inquiry.input.text.email' => 'メールアドレスを正しく入力してください',
	'inquiry.input.text.email.confirm' => '(確認用)',
	'inquiry.input.text.email.confirm.error' => 'メールアドレスが一致しません',
	'inquiry.input.text.clock' => '時刻を正しく入力してください',
	'inquiry.input.text.date' => '入力例に従って入力してください',
	'inquiry.input.text.fup' => 'ファイル選択',
	'inquiry.input.text.sel' => '選択してください',
	'inquiry.input.text.other.confirm' => 'その他の記入',
	'inquiry.input.text.txt.date' => '日付',
	'inquiry.input.text.txt.time' => '時間帯',
	'inquiry.input.text.txt.time1' => '時間',
	'inquiry.input.text.txt.start_date' => '開始日',
	'inquiry.input.text.txt.start_time' => '開始時間',
	'inquiry.input.text.txt.end_date' => '終了日',
	'inquiry.input.text.txt.end_time' => '終了時間',
	'inquiry.input.spl.address.roomNo' => '（任意）',
	'inquiry.input.spl.address.country.label' => '国・地域',
	'inquiry.input.spl.address.postcode.label' => '郵便番号',
	'inquiry.input.spl.address.prefecture.label' => '都道府県',
	'inquiry.input.spl.address.city.label' => '市区町村',
	'inquiry.input.spl.address.street.label' => '番地',
	'inquiry.input.spl.address.room.label' => '建物名・部屋番号',
	'inquiry.input.spl.name.full' => '名前',
	'inquiry.input.spl.name.first' => '名',
	'inquiry.input.spl.name.last' => '姓',
	'inquiry.input.price.tax' => '(税込)',
	'inquiry.input.price.tax-service' => '(税サ込)',
	'inquiry.input.spl.fullname.kana' => '全角カナで入力してください(姓名間の空白省略可)',
	'inquiry.input.spl.sepname.kana' => '全角カナで入力してください(空白不可)',
	'inquiry.input.label.required.not.input' => '未回答箇所がございますのでご確認ください。',
	'inquiry.input.label.required.not.input.eachquestion' => 'こちらは回答必須です。',
	'inquiry.input.transition.error' => 'ブラウザーの自動翻訳機能を利用する場合、<br>フォームの一部内容は正しく表示されないことがあります。<br>自動翻訳機能をOFFにしてからご利用ください。',
	'inquiry.input.message.no-pay-reserve' => '決済が完了していない予約があります。決済画面で決済を完了してください。確保した予約枠を解放して予約し直す場合、「最初から予約」	を選択してください。',
	'inquiry.input.button.cancel-no-pay-reserve' => '最初から予約',
	'inquiry.input.button.continue-no-pay-reserve' => '予約再開',
	'inquiry.input.button.close' => '閉じる',
	'inquiry.complete.title' => '回答完了',
	'inquiry.complete.label.thanks' => "お忙しい中ご回答誠にありがとうございました",
	'inquiry.complete.label.answered' => "あなたは既にご回答いただいております。<br>ご協力ありがとうございました。",
	'inquiry.complete.label.period' => "有効期間：{date}まで",
	'inquiry.complete.button.coupon' => 'お礼のクーポンを見る',
	'inquiry.complete.button.close' => 'チャットに戻る',
	'inquiry.complete.button.closewindow' => '閉じる',
	'inquiry.complete.error.overtime_title' => '予約失敗',
	'inquiry.complete.error.overtime' => '決済は所定時間以内に完了していないため、予約内容は自動的にキャンセルされました。',
	'inquiry.complete.button.input' => '予約ページに戻る',
	'inquiry.coupon.label.period1' => '残り {days}日',
	'inquiry.coupon.label.period2' => '{date} まで利用可能',
	'inquiry.coupon.label.present' => 'お礼のクーポン',
	'inquiry.coupon.label.remark' => 'クーポンの有効期間日時は、UTC+09:00を基準に表示しています。',
	'inquiry.coupon.button.use' => 'このクーポンを利用する',
	'inquiry.coupon.dialog.title' => 'クーポンを利用しますか',
	'inquiry.coupon.dialog.content' => '「はい」に進むと、クーポンの利用状態が更新されます。会計の前に、必ずスタッフにこの画面を確認してお願いします。',
	'inquiry.coupon.label.expired' => 'このクーポンは有効期限切れです',
	'inquiry.coupon.label.notstart' => 'ご利用前のクーポンです',
	'inquiry.coupon.label.used' => 'このクーポンは利用済みです',
	'inquiry.coupon.label.maxused' => 'このクーポンは利用上限に達しました',
	'inquiry.coupon.label.canotuse' => 'お客様のクーポンではありません',
	'inquiry.common.label.required' => '必須',
	'inquiry.common.label.input' => '入力してください',
	'inquiry.common.label.input.longtext' => '2000文字以下でご入力ください',
	'inquiry.common.label.input.longtext2' => '文字数が多すぎます。800字以内で入力してください。',
	'inquiry.common.label.other' => 'その他',
	'inquiry.common.label.other.input' => '詳細を入力してください',
	'inquiry.common.label.other.input.error' => 'その他の詳細を入力してください。',
	'inquiry.common.label.limit.num.error' => 'ご予約可能な予約数{unit}を上回っているため、数量を選択し直してください。',
	'inquiry.common.label.limit.num.min.error' => 'ご予約可能な予約数{unit}を下回っているため、数量を選択し直してください。',
	'inquiry.common.label.reserve.alone.error' => 'ご選択した種類は単独で予約できません。',
	'inquiry.common.label.other.input.required' => '（必須）',
	'inquiry.common.label.other.input.optional' => '',
	'inquiry.common.tab.step1' => 'フォームのご入力',
	'inquiry.common.tab.step2' => '入力内容確認',
	'inquiry.common.tab.step2.payment' => '確認と支払い',
	'inquiry.common.tab.step3' => '送信完了',
	'inquiry.common.complete.title' => '受付完了しました',
	'inquiry.common.complete.urlbtn' => 'HPに戻る',
	'inquiry.common.complete.description' => 'お問い合わせありがとうございます。通常3営業日以内に返信いたします。',
	'inquiry.login.title' => '限定公開',
	'inquiry.login.description' => 'パスワードを入力してください',
	'inquiry.login.error' => '正しいパスワードを入力してください',
	'inquiry.login.placeholder' => 'パスワード',
	'inquiry.login.button' => 'ログイン',
	'inquiry.faq.button' => 'よくある質問へ',
	'inquiry.faq.button.research' => '再検索',
	'inquiry.label.num' => '数量',
	'inquiry.label.close' => '閉じる',
	'inquiry.label.expand' => '展開する',
	'inquiry.button.coupon_apply' => '適用',


	'coupon.dialog.remark' => 'メールが届かない場合は迷惑メールフォルダの確認、セキュリティ設定の調整をした上で再度お試しください。',
	'coupon.dialog.title' => 'クーポンを保存・送信',
	'coupon.dialog.content.link' => 'クーポンリンクをコピーする',
	'coupon.dialog.copy' => 'コピー',
	'coupon.dialog.content.send' => 'クーポンをメールで送信する',
	'coupon.dialog.mail' => 'メールアドレスを入力',
	'coupon.dialog.button' => '送信',
	'coupon.dialog.mail.success' => '送信完了いたしました。',
	'coupon.dialog.mail.fail' => 'メールアドレスを正しく入力してください',
	'coupon.button.yes' => '使用する',
	'coupon.button.no' => '今は使用しない',
	'coupon.dialog.use.store' => '利用先',
	'coupon.dialog.use.remark' => 'ご利用前に必ずスタッフにこの画面をご提示ください。',
	'coupon.dialog.use.title' => 'この画面を<br>店舗スタッフに提示してください。',
	'coupon.dialog.use.staff' => '＜店舗スタッフ様＞',
	'coupon.dialog.use.staff.remark' => '「使用する」を押すと、クーポンが使用済みの
		ステータスになってしまい、元に戻せません。
		店舗スタッフ様が「使用する」ボタンを押して、
		クーポン使用となります。',
	'coupon.facility.choose' => '利用施設を選択',
	'coupon.dialog.copy.success' => 'コピーしました',
	'coupon.label.all.maxused' => 'このクーポンは利用上限枚数に達しているため、利用できません。',

	'coupon.title.usage_information' => 'ご利用情報',
	'coupon.title.usage_information_in_english' => 'Usage information',

	'coupon.lottery_challenge' => '抽選にチャレンジ',
	'coupon.lottery' => '抽選中...',
	'coupon.lose' => 'ハズレ...',
	'coupon.lose_message' => "残念、ハズレてしまいました...<br>またの機会にチャレンジしてください！",
	'coupon.lose_button' => '閉じる',
	'coupon.lase.participation.message' => '既に抽選に参加されました',

	'coupon.coupon_available.title' => 'クーポンを獲得しました',
	'coupon.error.deleted_error_title' => '存在しないクーポンです',
	'coupon.error.deleted_error_description' => '申し訳ございません、<br/>このクーポンは管理者から削除されました。',

	'common.error.front.title' => '申し訳ございません。エラーが発生しました。',
	'common.error.front.sub_title' => 'しばらく時間をおいて、再度お試しください。',
	'common.error.url_delete' => 'このページは削除されました。',

	'admin.batch.button.run.manual'=> '手動実行',
	'admin.batch.label.page_title' => 'バッチ実行状況一覧',
	'admin.batch.label.execution_date_range' => '実行日範囲',
	'admin.batch.label.batch_type' => 'バッチタイプ',
	'admin.batch.label.execution_status' => '実行ステータス',
	'admin.batch.label.batch_select' => 'バッチ選択',
	'admin.batch.label.manual_execute' => '手動実行',
	'admin.batch.label.execution_date' => '実行日',
	'admin.batch.label.batch_name' => 'バッチ',
	'admin.batch.label.type' => 'タイプ',
	'admin.batch.label.status' => 'ステータス',
	'admin.batch.label.priority' => '優先度',
	'admin.batch.label.details' => '詳細情報',
	'admin.batch.label.keyword_placeholder' => 'バッチ名、タイプなど',
	'admin.batch.label.status_pending' => '待機中',
	'admin.batch.label.status_running' => '実行中',
	'admin.batch.label.status_success' => '成功',
	'admin.batch.label.status_failed' => '失敗',
	'admin.batch.label.status_skipped' => 'スキップ',
	'admin.batch.label.status_unknown' => '不明',
	'admin.batch.message.error.start_date_format' => '開始日の形式が正しくありません。',
	'admin.batch.message.error.end_date_format' => '終了日の形式が正しくありません。',
	'admin.batch.message.error.date_range' => '開始日は終了日より前に設定してください。',
	'admin.batch.message.error.select_batch_required' => 'バッチを選択してください。',
	'admin.batch.message.error.invalid_batch' => '無効なバッチが指定されました。',
	'admin.batch.message.error.manual_execution_not_allowed' => 'このバッチは手動実行できません。',
	'admin.batch.message.error.manual_execution_failed' => '手動実行タスク追加に失敗しました: ',
	'admin.batch.message.error.system_error' => '手動実行タスク追加にエラーが発生しました。システム管理者にお問い合わせください。',
	'admin.batch.message.success.manual_execute_added_with_id' => '手動実行タスクを追加しました、次の実行に開始されます。実行ID: ',
	'admin.batch.label.duration_hours_minutes_seconds' => '%d時間%d分%d秒',
	'admin.batch.label.duration_minutes_seconds' => '%d分%d秒',
	'admin.batch.label.duration_seconds' => '%d秒',
);
