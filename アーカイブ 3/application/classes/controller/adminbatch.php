<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Adminbatch extends Controller_Template_Adminbase
{
    public $_auth_required = TRUE;
    public $_action_path = '/adminbatch/';
    public $_view_path = 'admin/batch/';

    public $_model;
    public $_batch_model;

    public function __construct(Request $request, Response $response) {
        parent::__construct($request, $response);

        $this->_model = new Model_Adminmodel();
        $this->_batch_model = new Model_Adminbatchmodel();
        $this->_model->init($this->_bot_id);
        ini_set('max_execution_time', 300);
    }

    public function action_index()
    {
        $this->redirect(uri: $this->_action_path . 'batchlist');
    }

    public function action_batchlist()
    {
        if ($this->request->post()) {
            $page_action = $this->request->post('page_action');
            if ($page_action === 'manual_execute') {
                $this->_handle_manual_execute();
            } else {
                $this->_handle_search();
            }
        }

        // Handle pagination parameters
        $paging = [];
        $page_size = Session::instance()->get('batchlist_page_size', 100);
        $paging_data = $this->request->post('paging');
        if ($paging_data) {
            $paging = json_decode($paging_data, true);
            $page_size = (int)($paging['size'] ?? $page_size);
        } else {
            $paging = [
                'count' => 0,
                'size' => $page_size,
                'page' => 0,
                'all' => 1
            ];
        }
        Session::instance()->set('batchlist_page_size', $page_size);

        // Get filter conditions from session or POST
        $filters = $this->_get_filters_from_session();
        
        $offset = $paging['page'] * $page_size;
        
        // Get executions with pagination
        $executions = $this->_batch_model->get_batch_executions($this->_bot_id, $filters, $offset, $page_size);
        $paging['count'] = $this->_batch_model->get_batch_executions_count($this->_bot_id, $filters);

        $view = View::factory($this->_view_path .'batchlist');
        $view->paging = $paging;

        // Format execution data for display
        foreach ($executions as &$execution) {
            $execution['status_info'] = $this->_batch_model->format_execution_status($execution['execution_status']);
            $execution['priority_label'] = $this->_batch_model->format_execution_priority($execution['execution_priority']);
            $execution['type_label'] = $this->_batch_model->format_execution_type($execution['execution_type']);
            $execution['duration_formatted'] = $this->_format_duration($execution['duration_seconds']);
        }

        // Pass data to view
        $view->executions = $executions;
        $view->filters = $filters;

        // Filter options
        $view->batch_type_options = $this->_batch_model->get_batch_type_options($this->_bot_id);
        $view->execution_status_options = $this->_batch_model->get_execution_status_options();
        // $view->result_status_options = $this->_batch_model->get_result_status_options();

        $this->template->content = $view;
    }

    private function _handle_search()
    {     
        // Only update filters if we have form data (not just pagination)
        if ($this->request->post('start_date') !== null || 
            $this->request->post('end_date') !== null || 
            $this->request->post('batch_type') !== null || 
            $this->request->post('execution_status') !== null || 
            // $this->request->post('result_status') !== null || 
            $this->request->post('keyword') !== null) {
            
            $filters = [
                'start_date' => $this->request->post('start_date'),
                'end_date' => $this->request->post('end_date'),
                'batch_type' => $this->request->post('batch_type'),
                'execution_status' => $this->request->post('execution_status'),
                // 'result_status' => $this->request->post('result_status'),
                'keyword' => trim($this->request->post('keyword'))
            ];

            // Validate date format
            if (!empty($filters['start_date']) && !$this->_is_valid_date($filters['start_date'])) {
                $this->_set_error_message('開始日の形式が正しくありません。');
                return;
            }

            if (!empty($filters['end_date']) && !$this->_is_valid_date($filters['end_date'])) {
                $this->_set_error_message('終了日の形式が正しくありません。');
                return;
            }

            // Validate date range
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                if (strtotime($filters['start_date']) > strtotime($filters['end_date'])) {
                    $this->_set_error_message('開始日は終了日より前に設定してください。');
                    return;
                }
            }

            // Save filters to session
            Session::instance()->set('adminbatch_filters', $filters);
        }
    }

    private function _handle_manual_execute()
    {
        $batch_type = trim($this->request->post('manual_batch_type'));
        
        if (empty($batch_type)) {
            $this->_set_error_message('バッチを選択してください。');
            return;
        }

        // Validate batch_type against allowed values
        $allowed_types = array_keys($this->_batch_model->get_batch_type_options($this->_bot_id));
        if (!in_array($batch_type, $allowed_types)) {
            $this->_set_error_message('無効なバッチが指定されました。');
            return;
        }

        try {
            // Check if manual execution is allowed for this batch type
            $batch_definitions = $this->_batch_model->get_batch_definitions();
            $manual_allowed = false;
            foreach ($batch_definitions as $definition) {
                if ($definition['batch_type'] === $batch_type && $definition['manual_execution_enabled'] == 1) {
                    $manual_allowed = true;
                    break;
                }
            }

            if (!$manual_allowed) {
                $this->_set_error_message('このバッチは手動実行できません。');
                return;
            }

            $result = $this->_batch_model->trigger_manual_execution($this->_bot_id, $definition['batch_type'], $definition['batch_name'], $definition['max_retry_attempts']);
            
            if ($result['success']) {
                $this->_set_success_message('手動実行タスクを追加しました、次の実行に開始されます。実行ID: ' . ($result['execution_id'] ?? 'N/A'));
            } else {
                $this->_set_error_message('手動実行タスク追加に失敗しました: ' . $result['message']);
            }
        } catch (Exception $e) {
            error_log('Manual execution failed: ' . $e->getMessage());
            $this->_set_error_message('手動実行タスク追加にエラーが発生しました。システム管理者にお問い合わせください。');
        }
    }

    private function _get_filters_from_session()
    {
        $default_filters = [
            'start_date' => date('Y-m-d', strtotime('-7 days')),
            'end_date' => date('Y-m-d'),
            'batch_type' => '',
            'execution_status' => '',
            // 'result_status' => '',
            'keyword' => ''
        ];

        $session_filters = Session::instance()->get('adminbatch_filters', []);
        return array_merge($default_filters, $session_filters);
    }

    private function _is_valid_date($date)
    {
        $format = 'Y-m-d';
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    private function _format_duration($seconds)
    {
        if (empty($seconds) || $seconds <= 0) {
            return '-';
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%d時間%d分%d秒', $hours, $minutes, $secs);
        } elseif ($minutes > 0) {
            return sprintf('%d分%d秒', $minutes, $secs);
        } else {
            return sprintf('%d秒', $secs);
        }
    }

    private function _build_pagination($current_page, $total_pages)
    {
        if ($total_pages <= 1) {
            return [];
        }

        $pagination = [];
        $start = max(1, $current_page - 2);
        $end = min($total_pages, $current_page + 2);

        // Previous page
        if ($current_page > 1) {
            $pagination[] = array(
                'page' => $current_page - 1,
                'label' => '前へ',
                'type' => 'prev'
            );
        }

        // Page numbers
        for ($i = $start; $i <= $end; $i++) {
            $pagination[] = array(
                'page' => $i,
                'label' => $i,
                'type' => 'page',
                'current' => ($i == $current_page)
            );
        }

        // Next page
        if ($current_page < $total_pages) {
            $pagination[] = array(
                'page' => $current_page + 1,
                'label' => '次へ',
                'type' => 'next'
            );
        }

        return $pagination;
    }

    private function _set_success_message($message)
    {
        Session::instance()->set('adminbatch_success_message', $message);
    }

    private function _set_error_message($message)
    {
        Session::instance()->set('adminbatch_error_message', $message);
    }

}
