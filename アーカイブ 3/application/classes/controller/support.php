<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Support extends Controller_Template_Adminbase {

	public $_auth_required = TRUE;
	public $_transactional = true;
	public $_model;
	public $_support_model;
	public $_aws_model;
	public $_view_path = 'support/';
	public $_action_path = '/support/';

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		$this->_support_model = new Model_Supportmodel();
		$this->_aws_model = new Model_Aws();
		$this->_model->init($this->_bot_id);
		ini_set('max_execution_time', 300); // 300 seconds = 5 minutes
	}

	public function action_translaterequests()
	{
		$userCd = $this->_user->role_cd;
		if ($userCd == "99" || $userCd == "80") {
			$translate_requests = $this->_support_model->get_translate_requests();
		} else {
			$translate_requests = $this->_support_model->get_translate_requests($this->_bot_id);
		}
		$formatted_translate_requests = $this->transformData($translate_requests);
		$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
		$view = View::factory ($this->_view_path . 'translaterequests');
		$view->formatted_translate_requests = $formatted_translate_requests;
		$view->isParentBot = $grp_bot_id == 0;
		$this->template->content = $view;
	}
	
	public function action_translaterequest()
	{
		$operation_user_role = ["80","99"]; // 翻訳担当者に設定できるユーザーのロール
		if (isset($_GET['request_id'])) {
			$request_id = $_GET['request_id']; 
		}
		$userCd = $this->_user->role_cd;
		if ($userCd == "99" || $userCd == "80") {
			$translate_information = $this->_support_model->get_translate_information($request_id);
			$translate_operation = $this->_support_model->get_translate_operation($request_id);
			$operation_users = $this->_model->get_users_by_roles($operation_user_role);
		}
		$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', 'ja');
		$config_button = json_decode($btn_name, true);
		$view = View::factory ($this->_view_path . 'translaterequest');
		$view->request_id = $request_id;
		$view->translate_information = $translate_information;
		$view->translate_operation = $translate_operation;
		$view->operation_users = $operation_users;
		$view->config_button = $config_button;
		$view->login_user_id = $this->_user->user_id;
		$this->template->content = $view;
	}

	// 新翻訳管理画面（item_idごとに情報を表示）
	public function action_translaterequestitems()
	{
		$userCd = $this->_user->role_cd;
		if ($userCd == "99" || $userCd == "80") {
			$translate_requests = $this->_support_model->get_translate_requests();
		} else {
			$translate_requests = $this->_support_model->get_translate_requests($this->_bot_id);
		}
		$formatted_translate_requests = $this->merge_translate_requests_by_item($translate_requests);
		$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
		$view = View::factory ($this->_view_path . 'translaterequestitems');
		$view->formatted_translate_requests = $formatted_translate_requests;
		$view->isParentBot = $grp_bot_id == 0;
		$this->template->content = $view;
	}

	public function action_translaterequestitem()
	{
		$operation_user_role = ["80","99"]; // 翻訳担当者に設定できるユーザーのロール
		if (isset($_GET['item_id'])) {
			$item_id = $_GET['item_id']; 
		}
		$userCd = $this->_user->role_cd;
		if ($userCd == "99" || $userCd == "80") {
			// item_idに基づいて関連するリクエストを取得
			$translate_requests = $this->_support_model->get_translate_requests_by_item_id($item_id);
			$merged_translate_requests = $this->merge_translate_requests_by_item_detail($translate_requests);
			$translate_information = $merged_translate_requests[0];
			$translate_operation = $this->_support_model->get_translate_operation_by_item_id($item_id);
			$merged_translate_operation = $this->merge_translate_operations_by_lang($translate_operation);
			$operation_users = $this->_model->get_users_by_roles($operation_user_role);
		}
		$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', 'ja');
		$config_button = json_decode($btn_name, true);
		$request_ids = $translate_information['request_ids'];
		$view = View::factory ($this->_view_path . 'translaterequestitem');
		$view->item_id = $item_id;
		$view->request_ids = $request_ids;
		$view->translate_information = $translate_information;
		$view->login_user_id = $this->_user->user_id;
		$view->translate_operation = $merged_translate_operation;
		$view->operation_users = $operation_users;
		$view->config_button = $config_button;
		$this->template->content = $view;
	}
	
	/**
	 * 一覧画面初期化用のAPI
	 * デフォルト絞込み条件での一覧データ、ログインユーザーがRedmineユーザーかどうか、カテゴリー一覧、担当者一覧データーを取得して返却する
	 */
	public function action_issuesapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$issues = $this->_support_model->get_redmine_issues($this->_bot_id);
			$is_redmine_user = $this->_support_model->check_if_redmineuser($this->_bot_id, $this->_user->email);
			$categories = $this->_support_model->get_categories($this->_bot_id);
			$assignees = $this->_support_model->get_assignees($this->_bot_id);
			$response_data = [
				'issues' => $issues['issues'], 
				'limit' => $issues['limit'],
				'offset' => $issues['offset'],
				'is_redmine_user' => $is_redmine_user, 
				'categories' => $categories, 
				'assignees' => $assignees
			];
			$this->response->status(200)->body(json_encode($response_data, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()]));
		}
	}

	/**
	 * 一覧画面絞込み用のAPI、ステータス、カテゴリー、担当者で絞り込んだ一覧データを取得して返却する
	 * そして、ページング用のオフセット、リミットを指定して取得する
	 */
	public function action_filterissuesapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$post = $this->request->query();
			$filter = [];
			if (isset($post['status'])) {
				$filter['status_id'] = $post['status'];
			}
			if (isset($post['category'])) {
				$filter['tracker_id'] = $post['category'];
			}
			if (isset($post['assignee'])) {
				$filter['assignee_email'] = $post['assignee'];
			}
			$offset = 0;
			if (isset($post['offset'])) {
				$offset = $post['offset'];
			}
			$limit = 100;
			if (isset($post['limit'])) {
				$limit = $post['limit'];
			}
			$issues = $this->_support_model->get_redmine_issues($this->_bot_id, $filter, $offset, $limit);
			$response_data = [
				'issues' => $issues['issues'], 
				'limit' => $issues['limit'],
				'offset' => $issues['offset']
			];
			$this->response->status(200)->body(json_encode($response_data, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
		}
	}

	private function isNotificationSuppressed($issue_attributes) {
		
		// 社内アカウントの場合：通知しない
		if (in_array($this->_user->role_cd, ['99', '88'])) {
			return true;
		}
		
		// 指定の単語が含まれている場合：通知しない
		$notification_excluded_words = ['「FAQ追加」']; // 単語リスト
		foreach ($notification_excluded_words as $word) {
			if (strpos($issue_attributes['subject'], $word) !== false) {
				return true;
			}
		}
	
		// notes が存在し、"" の場合：通知しない
		if (isset($issue_attributes['notes']) && $issue_attributes['notes'] == "") {
			return true;
		}
	
		return false;
	}

	public function action_createissueapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$post = $this->request->post();
			if (!$post['api_key']) {
				$apikey = $this->_support_model->get_redmine_user_apikey_by_email($this->_bot_id, $this->_user->email);
			} else {
				$apikey = $post['api_key'];
			}
			$issue_attributes = [];
			$issue_attributes['tracker_id'] = $post['category'];
			$issue_attributes['status_id'] = $post['status'];
			$issue_attributes['priority_id'] = $post['priority'];
			$issue_attributes['subject'] = $post['subject'];
			$issue_attributes['description'] = $post['description'];
			$issue_attributes['due_date'] = $post['due_date'];
			$issue_attributes['assigned_to_id'] = $post['assigned_to'];
			$issue_attributes['custom_fields'] = [
				[
					'id' => 14,
					'value' => $post['service_tag']
				]
			];
			// 自社担当者の設定
			$company_staff_field = $this->_support_model->get_company_staff_custom_field($this->_bot_id);
			if ($company_staff_field !== null) {
				$issue_attributes['custom_fields'][] = $company_staff_field;
			}
			if (isset($post['uploads'])) {
				$issue_attributes['uploads'] = json_decode($post['uploads'], true);
			}
			$response = $this->_support_model->create_redmine_issue($this->_bot_id, $this->_user->email, $issue_attributes, $apikey);
			// 通知
			$decoded_notify_link = json_decode($post['notify_link'], true);

			$no_notification = $this->isNotificationSuppressed($issue_attributes);

			if ($decoded_notify_link && in_array('slack', $decoded_notify_link) && !$no_notification) {
				$msg = ORM::factory('botmsg')->where('bot_id', '=', '0')->where('msg_cd', '=', 'redmine_settings')->find();
				$redmine_settings = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $msg->msg_id)->where('lang_cd', '=', 'ja')->find()->content;
				$redmine_settings_obj = json_decode($redmine_settings);
				
				$technical_categories = [1, 8];
				
				$webhook_url = null;
				if (in_array($issue_attributes['tracker_id'], $technical_categories)) {
					$webhook_url = $redmine_settings_obj->technical_slack_webhook_url;
				} else {
					$json_redmine = json_decode($this->_model->get_bot_setting($this->_bot_id, 'json_redmine'), true);
					if ($json_redmine && isset($json_redmine['cs_sale_contents_assigned_to_id'])) {
						$assignee_id = (string)$json_redmine['cs_sale_contents_assigned_to_id'];
						if (isset($redmine_settings_obj->client_team_slack_notifications)) {
							$client_notifications = $redmine_settings_obj->client_team_slack_notifications;
							if (isset($client_notifications->$assignee_id)) {
								$webhook_url = $client_notifications->$assignee_id;
							} elseif (isset($client_notifications->others)) {
								$webhook_url = $client_notifications->others;
							}
						}
					}
				}
				
				if ($webhook_url != null) {
					$header = ['Content-Type: application/json',];
					
					$redmine_data = $issue_attributes;
					$redmine_data['bot_id'] = $this->_bot_id;
					$redmine_data['bot_name'] = $this->_bot->bot_name;
					$redmine_data['url'] = 'https://admin.talkappi.com/support/issues/'.$response['id'];
					foreach (json_decode($post['redmineUsers'], true) as $assignee) {
						if ($assignee['value'] == $post['assigned_to']) {
							$redmine_data['assignee'] = $assignee['label'];
						}
					}
					foreach (json_decode($post['priorities'], true) as $priority) {
						if ($priority['value'] == $post['priority']) {
							$redmine_data['priority'] = $priority['label'];
						}
					}
					foreach (json_decode($post['categories'], true) as $category) {
						if ($category['value'] == $post['category']) {
							$redmine_data['category'] = $category['label'];
						}
					}
					foreach (json_decode($post['services'], true) as $service) {
						if ($service['value'] == $post['service_tag']) {
							$redmine_data['service_tag'] = $service['label'];
						}
					}

					$redmine_notify = ORM::factory('botmsg')->where('bot_id', '=', '0')->where('msg_cd', '=', 'redmine_notify')->find();
					$message = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $redmine_notify->msg_id)->where('lang_cd', '=', 'ja')->find()->content;
					foreach($redmine_data as $k=>$v) {
						if(!is_array($v)){
							$message = str_replace('{' . $k . '}', $v, $message);
						}
					}
					$data = ['text'=> $message, 'username' => '新規依頼通知'];
					$data = $this->_model->curl($webhook_url, $header, $data);
				}
			}
			$this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_updateissueapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$post = $this->request->post();
			if (!$post['api_key']) {
				$apikey = $this->_support_model->get_redmine_user_apikey_by_email($this->_bot_id, $this->_user->email);
			} else {
				$apikey = $post['api_key'];
			}
			$issue_attributes = [];
			$issue_attributes['status_id'] = $post['status'];
			$issue_attributes['priority_id'] = $post['priority'];
			$issue_attributes['subject'] = $post['subject'];
			$issue_attributes['description'] = $post['description'];
			$issue_attributes['due_date'] = $post['due_date'];
			$issue_attributes['tracker_id'] = $post['category'];
			$issue_attributes['assigned_to_id'] = $post['assigned_to'];
			$issue_attributes['custom_fields'] = [
				[
					'id' => 14,
					'value' => $post['service_tag']
				]
			];
			// 自社担当者の設定
			$company_staff_field = $this->_support_model->get_company_staff_custom_field($this->_bot_id);
			if ($company_staff_field !== null) {
				$issue_attributes['custom_fields'][] = $company_staff_field;
			}
			if (isset($post['uploads'])) {
				$issue_attributes['uploads'] = json_decode($post['uploads'], true);
			}
			if (isset($post['notes'])) {
				$issue_attributes['notes'] = $post['notes'];
			}

			$response = $this->_support_model->update_redmine_issue($this->_bot_id, $this->_user->email, $post['issue_id'], $issue_attributes, $apikey);
			
			// 翻訳チケットは翻訳リクエストテーブルのstatusを更新
			try {
				$sql = "SELECT * FROM t_translate_request WHERE redmine_issue_id = :issue_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':issue_id' => $post['issue_id'],
				));
				$results = $query->execute()->as_array();
				if (count($results) > 0) {
					$redmine_status = $this->_model->mapping_from_redmine_status_to_translate_request_status($post['status']);
					$this->_support_model->update_translate_request_status($results[0]['request_id'], $redmine_status);
				}
			} catch (Throwable $th) {
				
			}

			$no_notification = $this->isNotificationSuppressed($issue_attributes);

			// 通知
			$decoded_notify_link = json_decode($post['notify_link'], true);
			if ($decoded_notify_link && in_array('slack', $decoded_notify_link) && !$no_notification) {
				$redmine_settings = $this->_model->get_bot_tpl_message(0, 'redmine_settings', 'ja');
				$redmine_settings_obj = json_decode($redmine_settings);
				
				$technical_categories = [1, 8];
				
				$webhook_url = null;
				if (in_array($issue_attributes['tracker_id'], $technical_categories)) {
					$webhook_url = $redmine_settings_obj->technical_slack_webhook_url;
				} else {
					$json_redmine = json_decode($this->_model->get_bot_setting($this->_bot_id, 'json_redmine'), true);
					if ($json_redmine && isset($json_redmine['cs_sale_contents_assigned_to_id'])) {
						$assignee_id = (string)$json_redmine['cs_sale_contents_assigned_to_id'];
						if (isset($redmine_settings_obj->client_team_slack_notifications)) {
							$client_notifications = $redmine_settings_obj->client_team_slack_notifications;
							if (isset($client_notifications->$assignee_id)) {
								$webhook_url = $client_notifications->$assignee_id;
							} elseif (isset($client_notifications->others)) {
								$webhook_url = $client_notifications->others;
							}
						}
					}
				}
				
				if ($webhook_url != null) {
					$header = ['Content-Type: application/json'];
					
					$redmine_data = $issue_attributes;
					$redmine_data['bot_id'] = $this->_bot_id;
					$redmine_data['bot_name'] = $this->_bot->bot_name;
					$redmine_data['url'] = 'https://admin.talkappi.com/support/issues/'.$post['issue_id'];
					foreach (json_decode($post['redmineUsers'], true) as $assignee) {
						if ($assignee['value'] == $post['assigned_to']) {
							$redmine_data['assignee'] = $assignee['label'];
						}
					}
					foreach (json_decode($post['priorities'], true) as $priority) {
						if ($priority['value'] == $post['priority']) {
							$redmine_data['priority'] = $priority['label'];
						}
					}
					foreach (json_decode($post['categories'], true) as $category) {
						if ($category['value'] == $post['category']) {
							$redmine_data['category'] = $category['label'];
						}
					}
					foreach (json_decode($post['services'], true) as $service) {
						if ($service['value'] == $post['service_tag']) {
							$redmine_data['service_tag'] = $service['label'];
						}
					}

					$message_template = $this->_model->get_bot_tpl_message(0, 'redmine_reply_notify', 'ja');
					foreach($redmine_data as $k=>$v) {
						if(!is_array($v)){
							$message_template = str_replace('{' . $k . '}', $v, $message_template);
						}
					}
					$data = ['text'=> $message_template, 'username' => '返信通知'];
					$data = $this->_model->curl($webhook_url, $header, $data);
				}
			}
			$this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
		}
	}
	
	public function action_uploadattachmentapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$post = $this->request->post();
			if (!$post['api_key']) {
				$apikey = $this->_support_model->get_redmine_user_apikey_by_email($this->_bot_id, $this->_user->email);
			} else {
				$apikey = $post['api_key'];
			}
			// base64エンコードされたファイルを処理
			if (isset($post['attachment']) && isset($post['filename']) && isset($post['content_type'])) {
				$base64Attachment = $post['attachment'];
				$filename = $post['filename'];
				$contentType = $post['content_type'];
				// base64デコード
				$decodedFile = base64_decode($base64Attachment);
				$tempFilePath = sys_get_temp_dir() . '/' . $filename;
				file_put_contents($tempFilePath, $decodedFile);
				$attachment = [
					'tmp_name' => $tempFilePath,
					'name' => $filename,
					'size' => filesize($tempFilePath),
					'type' => $contentType
				];
			} else {
				$attachment = $_FILES['attachment'];
			}
			$response = $this->_support_model->upload_attachment($attachment, $this->_bot_id, $apikey);
			$this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_deleteattachmentapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$json = file_get_contents('php://input');
			$post = json_decode($json, true);
			if (!$post['api_key']) {
				$apikey = $this->_support_model->get_redmine_user_apikey_by_email($this->_bot_id, $this->_user->email);
			} else {
				$apikey = $post['api_key'];
			}
			$response = $this->_support_model->delete_attachment($post['token'], $this->_bot_id, $apikey);
			$this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_getattachmentapi() {
		try {
			$redmine_config = $this->_support_model->get_redmine_setting($this->_bot_id);
			$api_key = $redmine_config['api_key'];
			$redmine_url = $redmine_config['url'];
			$json = $this->request->body();
			$data = json_decode($json, true);
			$attachment_id = isset($data['attachment_id']) ? $data['attachment_id'] : null;
			if (empty($attachment_id) || !is_numeric($attachment_id)) {
				throw new Exception('Invalid or missing attachment ID: ' . var_export($attachment_id, true));
			}
			// Redmine APIでファイル情報を取得
			$ch = curl_init("{$redmine_url}/attachments/{$attachment_id}.json");
			$headers = [
				'X-Redmine-API-Key: ' . $api_key,
				'Content-Type: application/json'
			];
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			$response = curl_exec($ch);
			if (curl_errno($ch)) {
				throw new Exception('cURL error: ' . curl_error($ch));
			}
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			if ($http_code != 200) {
				throw new Exception('HTTP error: ' . $http_code);
			}
			curl_close($ch);
			$attachment_info = json_decode($response, true);
			if (!isset($attachment_info['attachment']) || !isset($attachment_info['attachment']['content_url'])) {
				throw new Exception('Invalid attachment information');
			}
			$file_url = $attachment_info['attachment']['content_url'];
			$file_name = $attachment_info['attachment']['filename'];
			$content_type = $attachment_info['attachment']['content_type'];
			// ファイルを取得
			$ch = curl_init($file_url);
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_BINARYTRANSFER, true); 
			$file_content = curl_exec($ch);
			if (curl_errno($ch)) {
				throw new Exception('cURL error: ' . curl_error($ch));
			}
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			if ($http_code != 200) {
				throw new Exception('HTTP error: ' . $http_code);
			}
			curl_close($ch);
			$file_name_encoded = rawurlencode($file_name);
			ob_clean();
			header('Content-Type: ' . $content_type);
			header("Content-Disposition: attachment; filename*=UTF-8''" . $file_name_encoded);
			header('Content-Length: ' . strlen($file_content));
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Expires: 0');
			echo $file_content;
			exit();
		} catch (Exception $e) {
			http_response_code(500);
			header('Content-Type: application/json');
			echo json_encode(['error' => $e->getMessage()]);
		}
	}
	
	/**
	 * Transform input data by sorting items based on request_id and creating target_langs.
	 * 
	 * @param array $inputData The input data to transform.
	 * @return array The transformed data with sorted items and target_langs created.
	 */
	private function transformData($inputData) {
		// Create an empty array to hold the transformed data
		$transformedData = array();
	
		// Loop through each item in the input data
		foreach ($inputData as $item) {
			// Spread the common properties into the new array
			$transformedData[$item['request_id']] ??= [
				...$item, // Spread all properties from $item
				'target_langs' => [] // Initialize target_langs array
			];
	
			// Add the language and its status to the target_langs array
			$transformedData[$item['request_id']]['target_langs'][$item['lang_cd']] = $item['status'];
		}
	
		return $transformedData;
	}

	/**
	 * 同じitem_idのデータをマージする関数
	 * 
	 * @param array $requests 取得した翻訳リクエストのデータ
	 * @return array マージされた翻訳リクエストのデータ
	 */
	private function merge_translate_requests_by_item($requests) {
		$merged_requests = [];
	
		foreach ($requests as $request) {
			$item_id = $request['item_id'];
			// ステータスが完了済みのリクエストはマージの対象外
			if ($request['status'] === '9') {
				continue;
			}
			if (!isset($merged_requests[$item_id])) {
				// 初めてのitem_idの場合、新しいエントリを作成
				$merged_requests[$item_id] = $request;
				$merged_requests[$item_id]['priority'] = $request['priority'];
				$merged_requests[$item_id]['source_status'] = $request['source_status'];
				$merged_requests[$item_id]['target_langs'] = [
					$request['lang_cd'] => $request['status']
				];
				$merged_requests[$item_id]['request_date'] = $request['request_date'];
				$merged_requests[$item_id]['deadline_date'] = $request['deadline_date'];
			} else {
				// 既に存在するitem_idの場合、データをマージ
				$merged_requests[$item_id]['priority'] = min($merged_requests[$item_id]['priority'], $request['priority']);
				$merged_requests[$item_id]['source_status'] = max($merged_requests[$item_id]['source_status'], $request['source_status']);
				if (!isset($merged_requests[$item_id]['target_langs'][$request['lang_cd']])) {
					$merged_requests[$item_id]['target_langs'][$request['lang_cd']] = $request['status'];
				} else {
					$merged_requests[$item_id]['target_langs'][$request['lang_cd']] = min($merged_requests[$item_id]['target_langs'][$request['lang_cd']], $request['status']);
				}
				$merged_requests[$item_id]['request_date'] = min($merged_requests[$item_id]['request_date'], $request['request_date']);
				$merged_requests[$item_id]['deadline_date'] = min($merged_requests[$item_id]['deadline_date'], $request['deadline_date']);
			}
		}
	
		return array_values($merged_requests);
	}

	private function merge_translate_requests_by_item_detail($requests) {
		$merged_requests = [];
		foreach ($requests as $request) {
			$item_id = $request['item_id'];
			// ステータスが完了済みのリクエストはマージの対象外
			// if ($request['status'] === '9') {
			// 	continue;
			// }
			if (!isset($merged_requests[$item_id])) {
				// 初めての item_id の場合、新しいエントリを作成
				$merged_requests[$item_id] = $request;
				$merged_requests[$item_id]['priority'] = $request['priority'];
				$merged_requests[$item_id]['source_status'] = $request['source_status'];
				$merged_requests[$item_id]['target_langs'] = [$request['lang_cd']];
				$merged_requests[$item_id]['request_date'] = $request['request_date'];
				$merged_requests[$item_id]['deadline_date'] = $request['deadline_date'];
				$merged_requests[$item_id]['request_detail'] = json_decode($request['request_detail'], true);
				$merged_requests[$item_id]['request_ids'] = [$request['request_id']];
			} else {
				// 既に存在する item_id の場合、データをマージ
				$merged_requests[$item_id]['priority'] = min($merged_requests[$item_id]['priority'], $request['priority']);
				$merged_requests[$item_id]['source_status'] = max($merged_requests[$item_id]['source_status'], $request['source_status']);
				if (!in_array($request['lang_cd'], $merged_requests[$item_id]['target_langs'])) {
					$merged_requests[$item_id]['target_langs'][] = $request['lang_cd'];
				}
				$merged_requests[$item_id]['request_date'] = min($merged_requests[$item_id]['request_date'], $request['request_date']);
				$merged_requests[$item_id]['deadline_date'] = min($merged_requests[$item_id]['deadline_date'], $request['deadline_date']);
				$current_request_detail = json_decode($request['request_detail'], true);
				if ($merged_requests[$item_id]['request_date'] == $request['request_date']) {
					$merged_requests[$item_id]['request_detail']['diff']['before'] = $current_request_detail['diff']['before'];
				}
				if ($merged_requests[$item_id]['request_date'] != $request['request_date']) {
					$merged_requests[$item_id]['request_detail']['diff']['after'] = $current_request_detail['diff']['after'];
				}
				if (!in_array($request['request_id'], $merged_requests[$item_id]['request_ids'])) {
					$merged_requests[$item_id]['request_ids'][] = $request['request_id'];
				}
			}
		}
		// target_langs をカンマ区切りの文字列に変換
		foreach ($merged_requests as &$merged_request) {
			$merged_request['target_langs'] = implode(',', $merged_request['target_langs']);
			$merged_request['request_detail'] = json_encode($merged_request['request_detail']);
		}
		unset($merged_request);
		return array_values($merged_requests);
	}

	private function merge_translate_operations_by_lang($operations) {
		$merged_operations = [];
		foreach ($operations as $operation) {
			$lang_cd = $operation['lang_cd'];
	
			if (!isset($merged_operations[$lang_cd])) {
				$merged_operations[$lang_cd] = $operation;
			} else {
				$merged_operations[$lang_cd]['status'] = min($merged_operations[$lang_cd]['status'], $operation['status']);
				// 最新のコメントを保持する
				if ($merged_operations[$lang_cd]['upd_date'] < $operation['upd_date']) {
					$merged_operations[$lang_cd]['notes'] = $operation['notes'];
					$merged_operations[$lang_cd]['release_date'] = $operation['release_date'];
				}
			}
		}
	
		return array_values($merged_operations);
	}

	// チケット一覧
	public function action_issues() {
		// 施設未選択の場合
		if ($this->_bot_id == NULL) {
			$view = View::factory('support/issues');
			$view->show_bot_box = true;
			$this->template->content = $view;
			return;
		}
		$filter = [];
		$status = null;
		$category = null;
		$assignee = null;
		$show_all_items = '0';
		if ($this->request->post() && $this->_page_action !== 'clear') {
			$status = $this->request->post('status');
			$category = $this->request->post('category');
			$assignee = $this->request->post('assignee');
			$show_all_items = $this->request->post('show_all_items');
	
			if ($status && $status !== '99') {
				$filter['status_id'] = $status;
			}
			if ($status === '1' || $status === '2' || $status === '3' || $status === '4'){
				$show_all_items = '0';
			} else if ($status === '5' || $status === '6'){
				$show_all_items = '1';
			}
			if ($category && $category !== '0') {
				$filter['tracker_id'] = $category;
			}
			if ($assignee && $assignee !== '0') {
				$filter['assignee_email'] = $assignee;
			}
		}

		// リダイレクト処理
		// issues/11111のようなURLにアクセスした時、チケットを閲覧可能な施設に自動で切り替え、チケットの詳細画面を開く
		// 該当するfacility_cdが見つからない場合はsupport/issuesにリダイレクト
		$issue_id = null;
		$path = $this->request->uri();
		if (preg_match('/issues\/(\d+)/', $path, $matches)) {
			$issue_id = $matches[1];
		}
		if ($issue_id) {
			$default_facility_cd = $this->_support_model->get_default_facility_cd_by_redmine_issue_id($issue_id, $this->_bot_id);
			if ($default_facility_cd) {
				$this->redirect("/support/issue?id={$issue_id}&facility_cd={$default_facility_cd}");
				exit;
			} else {
				$this->redirect("/support/issues");
				exit;
			}
		}

		$view = View::factory ($this->_view_path . 'issues');
		$view->show_all_items = $show_all_items;

		// redmine連携が設定されているか確認
		$redmine_setting_error = !$this->_support_model->check_redmine_project_id($this->_bot_id);
		if ($redmine_setting_error) {
			$view->redmine_setting_error = true;
			$this->template->content = $view;
			return;
		} else {
			$view->redmine_setting_error = false;
		}

		// redmineユーザーかどうか確認
		$redmine_user = !$this->_support_model->get_redmine_user_by_email($this->_bot_id, $this->_user->email);
		if($redmine_user){
			$view->redmine_user_error = true;
			$this->template->content = $view;
			return;
		} else {
			$view->redmine_user_error = false;
		}

		$retired_users = $this->_model->get_redmine_users(false, 3); // 退職者ユーザ

		// チケット
		$issues = $this->_support_model->get_redmine_issues($this->_bot_id, $filter);
		// ステータス一覧と絞り込み
		$statuses = $this->_support_model->redmine_status_list();
		$formatted_statuses = array_combine(array_column($statuses, 'code'), array_column($statuses, 'name'));
		$formatted_statuses = $formatted_statuses + [0 => '-'];
		// カテゴリ一覧と絞り込み条件
		$categories = array(
			0 => "-",
			1 => "不具合に関する報告",
			8 => "管理画面の設定方法に関する質問・相談",
			9 => "サービスに関する質問・相談",
			12 => "導入準備に関する連絡",
		);

		// 担当者一覧と絞り込み
		// $assignees = $this->_support_model->get_assignees($this->_bot_id);
		// $formatted_assignees = array_combine(array_column($assignees, 'user_id'), array_column($assignees, 'name'));
		// $formatted_assignees = $formatted_assignees + [0 => '-'];
		$service_categories = $this->_support_model->get_redmine_custom_fields($this->_bot_id, '14')['possible_values'];
		$msg = ORM::factory('botmsg')->where('bot_id', '=', '0')->where('msg_cd', '=', 'redmine_settings')->find();
		$redmine_settings = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $msg->msg_id)->where('lang_cd', '=', 'ja')->find()->content;
		$technical_assignees = json_decode($redmine_settings)->technical_assignees; // 自動アサインするテクニカルチームユーザ
		// view
		$view->issues_data = $issues['issues'];
		$view->categories = json_encode($categories, JSON_UNESCAPED_UNICODE);
		$view->category = $category;
		// $view->assignees = json_encode($formatted_assignees, JSON_UNESCAPED_UNICODE);
		// $view->assignee = $assignee;
		$view->technical_assignees = $technical_assignees;
		$view->service_categories = $service_categories;
		$view->retired_users = $retired_users;
		$view->statuses = json_encode($formatted_statuses, JSON_UNESCAPED_UNICODE);
		$view->status = $status;
		$this->template->content = $view;
	}

	public function action_issue() {
		
		// Loading表示
		if (!isset($_SESSION['issue_page_loaded'])) {
			$_SESSION['issue_page_loaded'] = true;
			$view = View::factory('support/issue');
			$view->loading = true;
			$this->template->content = $view;
			return;
		}
		unset($_SESSION['issue_page_loaded']);

		// 施設未選択の場合
		if ($this->_bot_id == NULL) {
			$view = View::factory('support/issue');
			$view->show_bot_box = true;
			$this->template->content = $view;
			return;
		}
		$view = View::factory ($this->_view_path . 'issue');
		// redmine連携が設定されているか確認
		$redmine_setting_error = !$this->_support_model->check_redmine_project_id($this->_bot_id);
		if ($redmine_setting_error) {
			$view->redmine_setting_error = true;
			$this->template->content = $view;
			return;
		} else {
			$view->redmine_setting_error = false;
		}

		// redmineユーザーかどうか確認
		$redmine_user = $this->_support_model->get_redmine_user_by_email($this->_bot_id, $this->_user->email);
		if($redmine_user == null){
			$view->redmine_user_error = true;
			$this->template->content = $view;
			return;
		} else {
			$view->redmine_user_error = false;
		}

		// redmineのユーザー一覧取得
		$redmine_users = $this->_support_model->get_redmine_users_by_bot($this->_bot_id);
		$service_categories = $this->_support_model->get_redmine_custom_fields($this->_bot_id, '14')['possible_values'];
		$base_bot= '0';
		// 翻訳管理チケット専用ボット
		if ($this->_bot_id == 99) {
			$base_bot = '99'; // 管理者用のbot_id
		}
		$msg = ORM::factory('botmsg')->where('bot_id', '=', $base_bot)->where('msg_cd', '=', 'redmine_settings')->find();
		$redmine_settings = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $msg->msg_id)->where('lang_cd', '=', 'ja')->find()->content;
		$assign_excluded_users = json_decode($redmine_settings)->assign_excluded_users; // 除外するユーザ
		$technical_assignees = json_decode($redmine_settings)->technical_assignees; // 自動アサインするテクニカルチームユーザ
		$filtered_redmine_users = array_filter($redmine_users, function ($user) use ($assign_excluded_users) {
			return !in_array($user['user']['id'], $assign_excluded_users);
		});
		$filtered_redmine_users = array_values($filtered_redmine_users); // 配列のインデックスを再設定
		// redmineのシステム管理者一覧取得
		$redmine_admin_users = $this->_model->get_redmine_users(true, 1);

		$retired_users = $this->_model->get_redmine_users(false, 3); // 退職者ユーザ

		// issue_id取得
		$issue_id = $this->request->query('id');
		if (!$issue_id) {
			$this->redirect('/support/issues');
		}
		try {
			$issue = $this->_support_model->get_redmine_issue($this->_bot_id, $issue_id);
		} catch (\Throwable $th) {
			Log::instance()->add(Log::ERROR, "Issue retrieval failed: " . $th->getMessage());
			$this->redirect('/support/issues');
		}
		$isAdmin = true;
		if ($this->_user->role_cd != '99' && $this->_user->role_cd != '88' && !isset($redmine_admin_users[$redmine_user['id']])) {
			$isAdmin = false;
		}

		$view->issue = $issue;
		$view->redmine_users = $filtered_redmine_users;
		$view->technical_assignees = $technical_assignees;
		$view->redmine_admin_users = $redmine_admin_users;
		$view->service_categories = $service_categories;
		$view->retired_users = $retired_users;
		$view->isAdmin = $isAdmin;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"9000","url":"/support/issues"}, {"f":"9001"}]');
	}

	public function action_issuenew() {
		// 施設未選択の場合
		if ($this->_bot_id == NULL) {
			$view = View::factory('support/issuenew');
			$view->show_bot_box = true;
			$this->template->content = $view;
			return;
		}
		$view = View::factory ($this->_view_path . 'issuenew');
		// redmine連携が設定されているか確認
		$redmine_setting_error = !$this->_support_model->check_redmine_project_id($this->_bot_id);
		if ($redmine_setting_error) {
			$view->redmine_setting_error = true;
			$this->template->content = $view;
			return;
		} else {
			$view->redmine_setting_error = false;
		}

		// redmineユーザーかどうか確認
		$redmine_user = !$this->_support_model->get_redmine_user_by_email($this->_bot_id, $this->_user->email);			
		if($redmine_user){
			$view->redmine_user_error = true;
			$this->template->content = $view;
			return;
		} else {
			$view->redmine_user_error = false;
		}

		$retired_users = $this->_model->get_redmine_users(false, 3); // 退職者ユーザ

		// redmineのユーザー一覧取得
		$redmine_users = $this->_support_model->get_redmine_users_by_bot($this->_bot_id);
		$service_categories = $this->_support_model->get_redmine_custom_fields($this->_bot_id, '14')['possible_values'];
		$msg = ORM::factory('botmsg')->where('bot_id', '=', '0')->where('msg_cd', '=', 'redmine_settings')->find();
		$redmine_settings = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $msg->msg_id)->where('lang_cd', '=', 'ja')->find()->content;
		$assign_excluded_users = json_decode($redmine_settings)->assign_excluded_users; // 除外するユーザ
		$technical_assignees = json_decode($redmine_settings)->technical_assignees; // 自動アサインするテクニカルチームユーザ
		$filtered_redmine_users = array_filter($redmine_users, function ($user) use ($assign_excluded_users) {
			return !in_array($user['user']['id'], $assign_excluded_users);
		});
		$filtered_redmine_users = array_values($filtered_redmine_users); // 配列のインデックスを再設定
		if ($this->request->post()){
			$post = $this->request->post();
			$slack = json_decode($post['notify_link'], true);
		}

		$view->redmine_users = $filtered_redmine_users;
		$view->technical_assignees = $technical_assignees;
		$view->service_categories = $service_categories;
		$view->retired_users = $retired_users;
		$view->isAdmin = ($this->_user->role_cd == '99');
		$this->template->content = $view;
		$this->_page_navi('[{"f":"9000","url":"/support/issues"}, {"f":"9002"}]');
	}

	public function action_getredmineusersapi() {
		$this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
		try {
			$apikey = $this->_support_model->get_redmine_user_apikey_by_email($this->_bot_id, $this->_user->email);
			$this->response->status(200)->body(json_encode(['key' => $apikey], JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
		}
	}

	// ヘルススコア確認
	public function action_healthscore() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$current_date = new DateTime();
		$start_date = (new DateTime())->modify('-2 months')->modify('first day of this month')->format('Y-m-01');
		$end_date = $current_date->format('Y-m-t');
		if ($this->request->post()) {
			$post = $this->request->post();
			// 年月を年月日に変換
			$start_date = $post['start_date'] !== "" ? $post['start_date'] . '-01' : "";
			$end_date = $post['end_date'] !== "" ? date("Y-m-t", strtotime($post['end_date'] . '-01')) : "";
		}
		$view = View::factory ($this->_view_path . 'healthscore');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->months = $this->_support_model->get_all_months($start_date, $end_date);
		$view->data = $this->_support_model->get_healthscore_report_data($start_date, $end_date);
		$this->template->content = $view;
	}

	// ログイン回数確認
	public function action_healthscorelogin() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$view = View::factory($this->_view_path . 'healthscorelogin');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$this->template->content = $view;
	}

	// ベリーくん利用回数
	public function action_healthscoreverybotuse() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		
		// 絞り込み期間のデフォルトは1年前〜今月
		$current_date = new DateTime();
		$start_date = (new DateTime())->modify('-1 year')->modify('first day of next month')->format('Y-m-01');
		$end_date = $current_date->format('Y-m-t');
		if ($this->request->post()) {
			$post = $this->request->post();
			// 年月を年月日に変換
			$start_date = $post['start_date'] !== "" ? $post['start_date'] . '-01' : "";
			$end_date = $post['end_date'] !== "" ? date("Y-m-t", strtotime($post['end_date'] . '-01')) : "";
		}
	
		$data = $this->_support_model->get_verybotuse_data($start_date, $end_date);
		$months = $this->_support_model->get_all_months($start_date, $end_date);
		$view = View::factory($this->_view_path . 'healthscoreverybotuse');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->months = $months;
		$view->data = $data;
		$this->template->content = $view;
	}

	// VERY更新回数
	public function action_healthscoreveryupdate() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$view = View::factory($this->_view_path . 'healthscoreveryupdate');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$this->template->content = $view;
	}

	// FAQ新規追加数
	public function action_healthscorefaqadd() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$view = View::factory($this->_view_path . 'healthscorefaqadd');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$this->template->content = $view;
	}

	// FAQ編集回数
	public function action_healthscorefaqedit() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$view = View::factory($this->_view_path . 'healthscorefaqedit');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$this->template->content = $view;
	}

	// お知らせ閲覧回数
	public function action_healthscorenoticeview() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		// デフォルトは最新の5つのticket_id
		$latest_tickets = $this->_support_model->get_latest_notice_ids(5);
		$start_ticket_id = !empty($latest_tickets) ? min($latest_tickets) : 0;
		$end_ticket_id = !empty($latest_tickets) ? max($latest_tickets) : 0;
		// 施設ごとの合計表示（デフォルトは非表示）
		$facility_total_view = '0';
		if ($this->request->post()) {
			$post = $this->request->post();
			$start_ticket_id = $post['start_ticket_id'] !== "" ? intval($post['start_ticket_id']) : $start_ticket_id;
			$end_ticket_id = $post['end_ticket_id'] !== "" ? intval($post['end_ticket_id']) : $end_ticket_id;
			$facility_total_view = $post['facility_total_view'] === "" ? '0' : $post['facility_total_view'];
		}
		$notice_ids = $this->_support_model->get_notice_ids($start_ticket_id, $end_ticket_id);
		$result = $this->_support_model->get_notice_view_data($start_ticket_id, $end_ticket_id);
		$data = $facility_total_view === '1' ? $result['bot_totals'] : $result['user_data'];
		$view = View::factory($this->_view_path . 'healthscorenoticeview');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$view->menu = $menu;
		$view->notice_ids = $notice_ids;
		$view->data = $data;
		$view->start_ticket_id = $start_ticket_id;
		$view->end_ticket_id = $end_ticket_id;
		$view->facility_total_view = $facility_total_view;
		$this->template->content = $view;
	}

	// ヘルススコア統計画面（管理画面利用）
	public function action_healthscoremgtaccess() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$div = $this->request->query('div', NULL);
		if ($div == NULL) {
			$this->redirect('/admin/top');
		}
		$current_date = new DateTime();
		$start_date = (new DateTime())->modify('-2 months')->modify('first day of this month')->format('Y-m-01');
		$end_date = $current_date->format('Y-m-t');
		// 施設ごとの合計表示（デフォルトは非表示）
		$facility_total_view = '0';
		$selected_bot_id = 1;

		if ($this->request->post()) {
			$post = $this->request->post();
			// 年月を年月日に変換
			$start_date = $post['start_date'] !== "" ? $post['start_date'] . '-01' : "";
			$end_date = $post['end_date'] !== "" ? date("Y-m-t", strtotime($post['end_date'] . '-01')) : "";
			$facility_total_view = $post['facility_total_view'] === "" ? '0' : $post['facility_total_view'];
			if ($facility_total_view === '0') {
				$selected_bot_id = isset($post['bot']) && $post['bot'] !== "" ? $post['bot'] : $selected_bot_id;
			} else {
				$selected_bot_id = null;
			}
		}

		$data = [];
		$months = [];
		$div_title = '';
		switch ($div) {
			case "1":
				$result = $this->_support_model->get_mgt_access_data($start_date, $end_date, 'all_access_count', $selected_bot_id);
				$data = $facility_total_view === '1' ? $result['bot_totals'] : $result['user_data'];
				$months = $this->_support_model->get_all_months($start_date, $end_date);
				$div_title = "ログイン回数";
				break;
			case "3":
				$result = $this->_support_model->get_mgt_access_data($start_date, $end_date, 'faq_modify_count', $selected_bot_id);
				$data = $facility_total_view === '1' ? $result['bot_totals'] : $result['user_data'];
				$months = $this->_support_model->get_all_months($start_date, $end_date);
				$div_title = "FAQ編集回数";
				break;
			case "4":
				$result = $this->_support_model->get_mgt_access_data($start_date, $end_date, 'verytop_save_count', $selected_bot_id);
				$data = $facility_total_view === '1' ? $result['bot_totals'] : $result['user_data'];
				$months = $this->_support_model->get_all_months($start_date, $end_date);
				$div_title = "VERY更新回数";
				break;
			case "5":
				$result = $this->_support_model->get_mgt_access_data($start_date, $end_date, 'statistics_view_count', $selected_bot_id);
				$data = $facility_total_view === '1' ? $result['bot_totals'] : $result['user_data'];
				$months = $this->_support_model->get_all_months($start_date, $end_date);
				$div_title = "統計情報確認回数";
				break;
			default:
				break;
		}
		
		$view = View::factory($this->_view_path . 'healthscoremgtaccess');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$menu->div = $div;
		$view->menu = $menu;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->months = $months;
		$view->data = $data;
		$view->div_title = $div_title;
		$view->facility_total_view = $facility_total_view;
		$view->bots = $this->_support_model->get_active_bots();
		$view->selected_bot_id = $selected_bot_id;
		$this->template->content = $view;
	}

	// ヘルススコア統計画面（ユーザー画面利用統計）
	public function action_healthscoreserviceusage() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		$div = $this->request->query('div', NULL);
		if ($div == NULL) {
			$this->redirect('/admin/top');
		}
		$current_date = new DateTime();
		$start_date = (new DateTime())->modify('-2 months')->modify('first day of this month')->format('Y-m-01');
		$end_date = $current_date->format('Y-m-t');
		if ($this->request->post()) {
			$post = $this->request->post();
			// 年月を年月日に変換
			$start_date = $post['start_date'] !== "" ? $post['start_date'] . '-01' : "";
			$end_date = $post['end_date'] !== "" ? date("Y-m-t", strtotime($post['end_date'] . '-01')) : "";
		}
		$data = [];
		$months = [];
		$div_title = '';
		$active_bots = $this->_support_model->get_active_bots();
		switch ($div) {
			case "1": // CHATBOT利用者数
				$data = $this->_support_model->get_chatbot_report_data($start_date, $end_date, $active_bots, 'monthly', 'new_member');
				$div_title = "CHATBOT利用者数（新規ユーザー数）";
				break;
			case "2": // CHATBOT会話数
				$data = $this->_support_model->get_chatbot_report_data($start_date, $end_date, $active_bots, 'monthly', 'new_conversation');
				$div_title = "CHATBOT会話数";
				break;
			case "3": // INQUIRY回答数
				$data = $this->_support_model->get_inquiry_report_data($start_date, $end_date, $active_bots, 'monthly', 'inquiry_answer');
				$div_title = "INQUIRY回答数";
				break;
			case "4": // SURVEY回答数
				$data = $this->_support_model->get_survey_report_data($start_date, $end_date, $active_bots, 'monthly', 'survey_answer');
				$div_title = "SURVEY回答数";
				break;
			case "5": // VERY利用者数
				$data = $this->_support_model->get_very_report_data($start_date, $end_date, $active_bots, 'monthly', 'very_member');
				$div_title = "VERY利用者数";
				break;
			default:
				break;
		}
		$view = View::factory($this->_view_path . 'healthscoreserviceusage');
		$menu = View::factory($this->_view_path . 'healthscoremenu');
		$menu->div = $div;
		$view->menu = $menu;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->months = $this->_support_model->get_all_months($start_date, $end_date);;
		$view->data = $data;
		$view->div_title = $div_title;
		$view->bots = $active_bots;
		$this->template->content = $view;
	}

	// csレポート
	public function action_clientreport() {
		if($this->_user->role_cd != '99' && $this->_user->role_cd != '80'){
			$this->redirect('/admin/top');
		}

		// タイトルスライド
		$title_slide_data = [
			'client_name' => $this->_bot->bot_name,
			'range' => date('Y/m/d', strtotime('first day of last month')) . ' 〜 ' . date('Y/m/d', strtotime('last day of last month')),
		];
	
		// よく聞かれる質問TOP10
		// 先月と先々月のよく聞かれる質問TOP10を取得
		$lang_cd = '';
		$context_id = '';
		$scene_cd = '';
		$last_month_start = date('Y-m-01', strtotime('first day of last month'));
		$last_month_end = date('Y-m-t', strtotime('last day of last month'));
		$two_months_ago_start = date('Y-m-01', strtotime('first day of -2 month'));
		$two_months_ago_end = date('Y-m-t', strtotime('last day of -2 month'));
		list($last_month_report_data, ) = $this->_model->get_report1($this->_bot_id, $lang_cd, $last_month_start, $last_month_end, $scene_cd, $context_id);
		list($two_months_ago_report_data, ) = $this->_model->get_report1($this->_bot_id, $lang_cd, $two_months_ago_start, $two_months_ago_end, $scene_cd, $context_id);
		$intent_dict = $this->_model->get_bot_talk_dict($this->_bot_id, $this->_lang_cd);
		// 先月と先々月のFAQランキングデータをコンパイル
		function _compile_faq_data($intent_dict, $report_data) {
			$data = [];
			foreach($report_data as $r) {
				$k = $r['intent_cd'] . '#' . $r['sub_intent_cd'] . '#' . $r['area_cd'];
				$question = array_key_exists($k, $intent_dict) ? $intent_dict[$k] : '';
				if ($question !== '') {
					$data[$question] = $r['c'];
				}
			}
			return $data;
		}
		$last_month_faq_ranking = _compile_faq_data($intent_dict, $last_month_report_data);
		$two_months_ago_faq_ranking = _compile_faq_data($intent_dict, $two_months_ago_report_data);	
		// 上位10件に制限し、降順にソート
		arsort($last_month_faq_ranking);
		arsort($two_months_ago_faq_ranking);
		$last_month_faq_ranking = array_slice($last_month_faq_ranking, 0, 10, true);
		$two_months_ago_faq_ranking = array_slice($two_months_ago_faq_ranking, 0, 10, true);
		// データを整形
		$last_month_faq_data = [];
		$index = 1;
		$rank = 1;
		$prev_count = null;
		foreach ($last_month_faq_ranking as $question => $count) {
			if ($prev_count !== null && $count < $prev_count) {
				$rank = $index;
			}
			$last_month_faq_data[] = [
				'rank' => $rank,
				'question' => $question,
				'count' => (int)$count
			];
			$prev_count = $count;
			$index++;
		}
		$two_months_ago_faq_data = [];
		$index = 1;
		$rank = 1;
		$prev_count = null;
		foreach ($two_months_ago_faq_ranking as $question => $count) {
			if ($prev_count !== null && $count < $prev_count) {
				$rank = $index;
			}
			$two_months_ago_faq_data[] = [
				'rank' => $rank,
				'question' => $question,
				'count' => (int)$count
			];
			$prev_count = $count;
			$index++;
		}

		// コンテンツTOP10
		// 先月と先々月のコンテンツ表示回数TOP10を取得
		$last_month_start = date('Y-m-01', strtotime('first day of last month'));
		$last_month_end = date('Y-m-t', strtotime('last day of last month'));
		$two_months_ago_start = date('Y-m-01', strtotime('first day of -2 month'));
		$two_months_ago_end = date('Y-m-t', strtotime('last day of -2 month'));
		$ref_div = 1;
		$last_month_results = $this->_model->get_item_ref($this->_bot_id, $this->_lang_cd, $ref_div, $last_month_start, $last_month_end, $lang_cd);
		$two_months_ago_results = $this->_model->get_item_ref($this->_bot_id, $this->_lang_cd, $ref_div, $two_months_ago_start, $two_months_ago_end, $lang_cd);
		$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $this->_lang_cd);
		$config_button = json_decode($btn_name, true);
		// $ref_type（ボタン名） を取得
		$ref_type = [];
		$report_data = [];
		$all_results = array_merge($last_month_results, $two_months_ago_results);
		foreach ($all_results as $data) {
			$key = strval($data['item_id']);
			if (array_key_exists($key, $report_data)) {
				$report_data[$key][$data['ref_type_cd']] = $data['access_count'];
			} else {
				$report_data[$key] = [
					'item_name' => $data['item_name'],
					'class_cd' => $data['class_cd'],
					$data['ref_type_cd'] => $data['access_count']
				];
			}
			if (!array_key_exists($data['ref_type_cd'], $ref_type)) {
				if (array_key_exists('BTN_' . strtoupper($data['ref_type_cd']), $config_button)) {
					$ref_type[$data['ref_type_cd']] = $config_button['BTN_' . strtoupper($data['ref_type_cd'])];
				} else if ($data['ref_type_cd'] == 'SHOW') {
					$ref_type[$data['ref_type_cd']] = __('admin.common.label.impressions');
				} else {
					$ref_type[$data['ref_type_cd']] = $data['ref_type_cd'];
				}
			}
		}
		function _process_content_ranking_data($results, $config_button) {
			$report_data = [];
			$button_counts = [];
		
			foreach ($results as $data) {
				$key = strval($data['item_id']);
				if (!array_key_exists($key, $report_data)) {
					$report_data[$key] = [
						'item_name' => $data['item_name'],
						'class_cd' => $data['class_cd'],
						$data['ref_type_cd'] => $data['access_count']
					];
				} else {
					if (!isset($report_data[$key][$data['ref_type_cd']])) {
						$report_data[$key][$data['ref_type_cd']] = 0;
					}
					$report_data[$key][$data['ref_type_cd']] += $data['access_count'];
				}
				// ボタンクリック数
				if (strpos($data['ref_type_cd'], 'BTN_') !== false) {
					$button_name = $config_button['BTN_' . strtoupper($data['ref_type_cd'])] ?? $data['ref_type_cd'];
					if (!isset($report_data[$key][$button_name])) {
						$report_data[$key][$button_name] = 0;
					}
					$report_data[$key][$button_name] += $data['access_count'];
					// ボタンのクリック数をカウント
					if (!isset($button_counts[$button_name])) {
						$button_counts[$button_name] = 0;
					}
					$button_counts[$button_name] += $data['access_count'];
				}
			}
			// ボタンのクリック数が1つもないボタンを除外
			foreach ($button_counts as $button_name => $count) {
				if ($count == 0) {
					$button_key = array_search($button_name, $config_button);
					if ($button_key !== false) {
						unset($config_button[$button_key]);
					}
				}
			}
			// 表示回数順にソート
			usort($report_data, function($a, $b) {
				return ($b['SHOW'] ?? 0) - ($a['SHOW'] ?? 0);
			});
			// 上位10個を取得
			$report_data = array_slice($report_data, 0, 10);
			// 順位付け
			$rank = 1;
			$prev_show = null;
			$skip = 0;
			foreach ($report_data as &$item) {
				if ($prev_show !== null && $item['SHOW'] < $prev_show) {
					$rank += $skip + 1;
					$skip = 0;
				} else if ($prev_show !== null && $item['SHOW'] == $prev_show) {
					$skip++;
				}
				$item['rank'] = $rank;
				$prev_show = $item['SHOW'];
			}
			return $report_data;
		}
		$last_month_report_data = _process_content_ranking_data($last_month_results, $config_button);
		$two_months_ago_report_data = _process_content_ranking_data($two_months_ago_results, $config_button);
		// データから必要なキーを動的に取得
		$required_keys = [];
		foreach ([$last_month_report_data, $two_months_ago_report_data] as $data) {
			foreach ($data as $item) {
				foreach ($item as $ref_type_key => $value) {
					if ($ref_type_key !== 'item_name' && $ref_type_key !== 'class_cd' && $ref_type_key !== 'rank' && !in_array($ref_type_key, $required_keys)) {
						$required_keys[] = $ref_type_key;
					}
				}
			}
		}
		function _convert_to_js_data($report_data, $ref_type, $required_keys) {
			$js_data = [];
			foreach ($report_data as $key => $item) {
				$js_item = [
					"#" => $item['rank'],
					"名称" => $item['item_name']
				];
				foreach ($required_keys as $required_key) {
					$js_item[$ref_type[$required_key] ?? $required_key] = $item[$required_key] ?? "-";
				}
				$js_data[] = $js_item;
			}
			return $js_data;
		}
		$last_month_content_data = _convert_to_js_data($last_month_report_data, $ref_type, $required_keys);
		$two_months_ago_content_data = _convert_to_js_data($two_months_ago_report_data, $ref_type, $required_keys);
		
		// view
		$view = View::factory($this->_view_path . 'clientreport');
		// タイトルスライド
		$view->title_slide_data = $title_slide_data;
		// よく聞かれる質問TOP10
		$view->last_month_faq_data = $last_month_faq_data;
		$view->two_months_ago_faq_data = $two_months_ago_faq_data;
		// コンテンツTOP10
		$view->last_month_content_data = $last_month_content_data;
		$view->two_months_ago_content_data = $two_months_ago_content_data;
		$this->template->content = $view;

	}

	// csレポートv2
	public function action_summaryreport() {
		// 管理画面に表示するデータのリスト
		$selected_data_list = ['questionsTop10', 'facilityContentsTop10', 'unsatisfactionSurveysTop10'];
		// ある程度完成するまでは社内のみに公開
		if (!in_array($this->_user->role_cd, ['99', '80'])) {
			$this->redirect('/admin/top');
		}
		$params = ['bot_id' => $this->_bot_id, 'target_date'=>date('Y-m')];
		$is_report_generated = false;
		if ($this->request->post()) {
			$post = $this->request->post();
			if ($this->_page_action == 'generate') {
				$params['target_date'] = $post['target_date'] !== "" ? $post['target_date'] : date('Y-m');
				$is_report_generated = true;
			}
		}
		$response_data = [];
		$response_data = $this->_model->call_admin_api('report', 'summaryreport', 'GET', $params);

		$params['start_date'] = $params['target_date'] . '-01';
		$params['end_date'] = date("Y-m-d",strtotime("+1 month",strtotime($params['start_date'])));
		$params['reports'] = ['unanswer_questions_ranking', 'faq_survey_result'];
		$params['answer'] = 0;
		$api_result = $this->_model->call_admin_api('report', 'faqreport', 'GET', $params);
		$response_data['unanswerTop10']['components'][] = $this->_create_report_table($api_result['unanswer_questions_ranking'], '未回答質問 TOP10（表）', ['#'=>'', '質問内容'=>'question', '回数'=>'count']);

		$total_satisfied = 0;
		$total_unsatisfied = 0;
		foreach($api_result['faq_survey_result'] as $k=>$v) {
			$total_satisfied = $total_satisfied + (int)$v['yes'];
			$total_unsatisfied = $total_unsatisfied + (int)$v['no'];
		}
		$survey_data = [
			['item'=>'満足調査回答数', 'value'=>$total_satisfied + $total_unsatisfied . '名'],
			['item'=>'十分と答えた方', 'value'=>$total_satisfied . '名'],
			['item'=>'不十分と答えた方', 'value'=>$total_unsatisfied . '名'],
		];
		if ($total_satisfied + $total_unsatisfied > 0) {
			$survey_data[] = ['item'=>'十分率', 'value'=>round(($total_satisfied / ($total_satisfied + $total_unsatisfied)) * 100, 1) . '%'];
			$survey_data[] = ['item'=>'不十分率', 'value'=>round(($total_unsatisfied / ($total_satisfied + $total_unsatisfied)) * 100, 1) . '%'];
		} else {
			$survey_data[] = ['item'=>'十分率', 'value'=>'計算不可'];
			$survey_data[] = ['item'=>'不十分率', 'value'=>'計算不可'];
		}
		$response_data['faqSurveyResult']['components'][] = $this->_create_report_table($survey_data, '回答満足度（表）', ['項目'=>'item', '値'=>'value']);
    
    $params['reports'] = ['channel_new_user_data', 'channel_session_all_data'];
		$channel_data = $this->_model->call_admin_api('report', 'chatbotreport', 'GET', $params);
		$channel_regist_user_data = $channel_data['channel_new_user_data'];
		$channel_session_all_data = $channel_data['channel_session_all_data'];
		$channel_user_report = [
			'fb_user' => 0, 
			'ln_user' => 0,
			'wc_user' => 0,
			'wb_user' => 0
		];
		$total_user = 0;
		foreach ($channel_regist_user_data as $data) {
			$sns_type_cd = $data['sns_type_cd'];
			$type = $sns_type_cd . '_user';
			$channel_user_report[$type] += intval($data['count']);
			$total_user += intval($data['count']);
		}
		$channel_user_report['total_user'] = $total_user;
		$channel_user_pie_data = [
			['label' => 'Facebook', 'usage' => $channel_user_report['fb_user']],
			['label' => 'LINE', 'usage' => $channel_user_report['ln_user']],
			['label' => 'WeChat', 'usage' => $channel_user_report['wc_user']],
			['label' => 'Web', 'usage' => $channel_user_report['wb_user']]
		];
		$response_data['channelUserReport']['components'][] = $this->_create_report_chart($channel_user_pie_data, 'チャネル別利用状況_ユーザー数（円グラフ）', 'piechart');
		$response_data['channelUserReport']['components'][] = $this->_create_report_table([$channel_user_report], 'チャネル別利用状況_ユーザー数（表）', [
			'Facebook' => 'fb_user',
			'LINE' => 'ln_user',
			'WeChat' => 'wc_user',
			'Web' => 'wb_user',
			'合計' => 'total_user',
		]);
		$channel_session_report = [
			'fb_session' => 0,
			'ln_session' => 0,
			'wc_session' => 0,
			'wb_session' => 0,
		];
		$total_session = 0;
		foreach ($channel_session_all_data as $data) {
			$sns_type_cd = $data['sns_type_cd'];
			$type = $sns_type_cd . '_session';
			$channel_session_report[$type] += intval($data['count']);
			$total_session += intval($data['count']);
		}
		$channel_session_report['total_session'] = $total_session;
		$channel_session_pie_data = [
			['label' => 'Facebook', 'usage' => $channel_session_report['fb_session']],
			['label' => 'LINE', 'usage' => $channel_session_report['ln_session']],
			['label' => 'WeChat', 'usage' => $channel_session_report['wc_session']],
			['label' => 'Web', 'usage' => $channel_session_report['wb_session']]
		];
		$response_data['channelSessionReport']['components'][] = $this->_create_report_chart($channel_session_pie_data, 'チャネル別利用状況_会話数（円グラフ）', 'piechart');
		$response_data['channelSessionReport']['components'][] = $this->_create_report_table([$channel_session_report], 'チャネル別利用状況_会話数（表）', [
			'Facebook' => 'fb_session',
			'LINE' => 'ln_session',
			'WeChat' => 'wc_session',
			'Web' => 'wb_session',
			'合計' => 'total_session'
		]);

    $lang_array = $this->_model->get_code('02', $this->_lang_cd);
		$display_lang = explode(',', $this->_bot->lang_cd);
		$chatbot_lang_user_report = [];
		$chatbot_lang_session_report = [];
		$lang_user_headers = [];
		$lang_session_headers = [];
		foreach ($display_lang as $lang) {
			if (isset($lang_array[$lang])) {
				$lang_name = $lang_array[$lang];
				$chatbot_lang_user_report[$lang.'_user'] = '-';
				$lang_user_headers[$lang_name] = $lang . '_user';
				$chatbot_lang_session_report[$lang.'_session'] = '-';
				$lang_session_headers[$lang_name] = $lang . '_session';
			}
		}
		$lang_user_headers['合計'] = 'total_user';
		$lang_session_headers['合計'] = 'total_session';
		$total_user = 0;
		$total_session = 0;
		foreach ($channel_regist_user_data as $data) {
			$total_user += $data['count'];
			if ($chatbot_lang_user_report[$data['lang_cd'].'_user'] === '-') {
				$chatbot_lang_user_report[$data['lang_cd'].'_user'] = 0;
			}
			$chatbot_lang_user_report[$data['lang_cd'].'_user'] += intval($data['count']);
		}
		$chatbot_lang_user_report['total_user'] = $total_user;
		$lang_user_pie_data = [];
		foreach ($lang_user_headers as $lang_name => $key) {
			if ($key !== 'total_user' && $chatbot_lang_user_report[$key] !== '-' && $chatbot_lang_user_report[$key] > 0) {
				$lang_user_pie_data[] = ['label' => $lang_name, 'usage' => $chatbot_lang_user_report[$key]];
			}
		}
		if (!empty($lang_user_pie_data)) {
			$response_data['chatbotLangReport']['components'][] = $this->_create_report_chart($lang_user_pie_data, '言語別利用状況_ユーザー数（円グラフ）', 'piechart');
		}
		$response_data['chatbotLangReport']['components'][] = $this->_create_report_table([$chatbot_lang_user_report], '言語別利用状況_ユーザー数（表）', $lang_user_headers);
		foreach ($channel_session_all_data as $data) {
			$total_session += $data['count'];
			if ($chatbot_lang_session_report[$data['lang_cd'].'_session'] === '-') {
				$chatbot_lang_session_report[$data['lang_cd'].'_session'] = 0;
			}
			$chatbot_lang_session_report[$data['lang_cd'].'_session'] += intval($data['count']);
		}
		$chatbot_lang_session_report['total_session'] = $total_session;
		$lang_session_pie_data = [];
		foreach ($lang_session_headers as $lang_name => $key) {
			if ($key !== 'total_session' && $chatbot_lang_session_report[$key] !== '-' && $chatbot_lang_session_report[$key] > 0) {
				$lang_session_pie_data[] = ['label' => $lang_name, 'usage' => $chatbot_lang_session_report[$key]];
			}
		}
		if (!empty($lang_session_pie_data)) {
			$response_data['chatbotLangReport']['components'][] = $this->_create_report_chart($lang_session_pie_data, '言語別利用状況_会話数（円グラフ）', 'piechart');
		}
		$response_data['chatbotLangReport']['components'][] = $this->_create_report_table([$chatbot_lang_session_report], '言語別利用状況_会話数（表）', $lang_session_headers);
		$very_data = $this->_model->call_admin_api('report', 'veryreport', 'GET', $params);
		$user_count_data = [['項目' => 'VERY利用者数', '値' => $very_data['user_count'] ?? 0]];
		$response_data['veryUserCount']['components'][] = $this->_create_report_table($user_count_data, 'VERY利用者数（表）', ['項目' => '項目', '値' => '値']);
		// $very_lang_user_report = ['ja_user' => 0, 'en_user' => 0, 'cn_user' => 0, 'tw_user' => 0, 'ko_user' => 0, 'other_user' => 0];
		$very_lang_click_report = ['ja_click' => 0, 'en_click' => 0, 'cn_click' => 0, 'tw_click' => 0, 'ko_click' => 0, 'other_click' => 0];
		// $total_user = 0;
		$total_click = 0;
		// if (!empty($very_data['user_count_by_language'])) {
		// 	foreach ($very_data['user_count_by_language'] as $lang => $count) {
		// 		$total_user += $count;
		// 		if (in_array($lang, ['ja', 'en', 'cn', 'tw', 'ko'])) {
		// 			$very_lang_user_report[$lang.'_user'] = $count;
		// 		} else {
		// 			$very_lang_user_report['other_user'] += $count;
		// 		}
		// 	}
		// }
		if (!empty($very_data['click_count'])) {
			foreach ($very_data['click_count'] as $lang => $click_data) {
				$total_clicks = 0;
				foreach (['categories', 'functions'] as $type) {
					if (isset($click_data[$type])) {
						foreach ($click_data[$type] as $clicks) {
							$total_clicks += is_array($clicks) ? array_sum($clicks) : $clicks;
						}
					}
				}
				$total_click += $total_clicks;
				if (in_array($lang, ['ja', 'en', 'cn', 'tw', 'ko'])) {
					$very_lang_click_report[$lang.'_click'] = $total_clicks;
				} else {
					$very_lang_click_report['other_click'] += $total_clicks;
				}
			}
		}
		// $very_lang_user_report['total_user'] = $total_user;
		$very_lang_click_report['total_click'] = $total_click;
		// $very_user_headers = ['日本語' => 'ja_user', '英語' => 'en_user', '中国語（簡体）' => 'cn_user', '中国語（繁体）' => 'tw_user', '韓国語' => 'ko_user', 'その他' => 'other_user', '合計' => 'total_user'];
		$very_click_headers = ['日本語' => 'ja_click', '英語' => 'en_click', '中国語（簡体）' => 'cn_click', '中国語（繁体）' => 'tw_click', '韓国語' => 'ko_click', 'その他' => 'other_click', '合計' => 'total_click'];
		$very_click_pie_data = [];
		foreach ($very_click_headers as $lang_name => $key) {
			if ($key !== 'total_click' && $very_lang_click_report[$key] !== '-' && $very_lang_click_report[$key] > 0) {
				$very_click_pie_data[] = ['label' => $lang_name, 'usage' => $very_lang_click_report[$key]];
			}
		}
		if (!empty($very_click_pie_data)) {
			$response_data['veryLangReport']['components'][] = $this->_create_report_chart($very_click_pie_data, 'VERY言語別クリック数（円グラフ）', 'piechart');
		}
		// $response_data['veryLangReport']['components'][] = $this->_create_report_table([$very_lang_user_report], 'VERY言語別利用者数（表）', $very_user_headers);
		$response_data['veryLangReport']['components'][] = $this->_create_report_table([$very_lang_click_report], 'VERY言語別クリック数（表）', $very_click_headers);
		$view = View::factory($this->_view_path . 'summaryreport');
		$view->data = $response_data;
		$view->target_date = $params['target_date'];
		$view->is_report_generated = $is_report_generated;
		$this->template->content = $view;
	}

	private function _create_report_table($data, $title, $header, $merge = [])
	{
		$table_data = [];
		for($i = 0; $i < count($data); $i++) {
			$row = [];
			foreach($header as $key => $value) {
				if ($key == '#') {
					if ($i > 0 && $data[$i]['count'] == $data[$i-1]['count']) {
						$row[$key] = $table_data[$i-1]['#'];
					}
					else {
						$row[$key] = $i+1;
					}
				} else if ($value == 'count') {
					$row[$key] = $data[$i]['count'];
				} else {
					$row[$key] = $data[$i][$value];
				}
			}
			$table_data[] = $row;
		}
		$columns = [];
		foreach($header as $key => $value) {
			$columns[] = ['Header' => $key, 'accessor' => $key];
		}
		$table = [
			'type' => 'table',
			'title' => $title,
			'headers' => array_keys($header),
			'rows' => $table_data,
			'columns' => $columns,
			'customSettings' => [
				'mergeCells' => $merge
			]
		];
		return $table;
	}

	private function _create_report_chart($data, $title, $chart_type)
	{
		$chart = [
			'type' => $chart_type,
			'title' => $title,
			'data' => $data
		];
		return $chart;
	}

	// メール検索
	public function action_mailsearch() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		// 絞り込み期間のデフォルトは1日前〜現在
		$current_date = new DateTime();
		$start_date = (new DateTime())->modify('-1 day')->format('Y-m-d H:i:s');
		$end_date = $current_date->format('Y-m-d H:i:s');
		$bot_id = null;
		$mail = null;
		$limit = 500;

		if ($this->request->post()) {
			$post = $this->request->post();
			// 年月日と日時を適切な形に変換
			if (!empty($post['range'])) {
				$range = json_decode($post['range'], true);
				$start_date = !empty($range['startDate']) && !empty($range['startTime']) ? $range['startDate'] . ' ' . $range['startTime'] : $start_date;
				$end_date = !empty($range['endDate']) && !empty($range['endTime']) ? $range['endDate'] . ' ' . $range['endTime'] : $end_date;
				$limit = $post['limit'] !== "" ? $post['limit'] : 100;
			}
			$bot_id = $post['bot_id'] !== "" ? $post['bot_id'] : null;
			$mail = $post['mail'] !== "" ? $post['mail'] : null;
		}
		// 期間
		$range = [
			'startDate' => date('Y-m-d', strtotime($start_date)),
			'startTime' => date('H:i', strtotime($start_date)),
			'endDate' => date('Y-m-d', strtotime($end_date)),
			'endTime' => date('H:i', strtotime($end_date))
		];
		$logs = $this->_support_model->get_mail_logs($range['startDate'], $range['startTime'], $range['endDate'], $range['endTime'], $bot_id, null, $mail, $limit);
		$view = View::factory($this->_view_path . 'mailsearch');
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->bot_id = $bot_id;
		$view->mail = $mail;
		$view->logs = $logs;
		$view->range = $range;
		$view->limit = $limit;
		$this->template->content = $view;
	}

	// 担当案件確認
	public function action_assignments() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}
		// 担当案件数を取得
		$assignments = $this->_support_model->get_assigned_bot_counts();
		// ユーザー一覧を取得
		$users = $this->_model->get_redmine_users(true, 1);
		// データの結合
		$combined_data = [];
		$unknown_count = 0;
		foreach ($assignments as $assignment) {
			$assignee_id = $assignment['assignee_id'];
			if ($assignee_id == 0 || !isset($users[$assignee_id])) {
				$unknown_count += $assignment['bot_count'];
				continue;
			}
			$combined_data[] = [
				'user_id' => $assignee_id,
				'user_name' => $users[$assignee_id],
				'active_bot_count' => $assignment['active_bot_count'],
				'inactive_bot_count' => $assignment['inactive_bot_count'],
				'total_bot_count' => $assignment['total_bot_count']
			];
		}
		// 不明のユーザー
		if ($unknown_count > 0) {
			$combined_data[] = [
				'user_id' => 0,
				'user_name' => '不明',
				'active_bot_count' => $unknown_count,
				'inactive_bot_count' => 0,
				'total_bot_count' => $unknown_count
			];
		}
		$view = View::factory($this->_view_path . 'assignments');
		$view->assignments = $combined_data;
		$this->template->content = $view;
	}

	// NGワード
	public function action_ngwords() {
		if ($this->_user->role_cd != '99') {
			$this->redirect('/admin/top');
		}

		if ($this->request->post()) {
			$post = $this->request->post();
		}

		$ng_words = $this->_support_model->get_bot_ngwords($this->_bot_id);
		$lang_codes = [];
		foreach ($ng_words as &$ng_word) {
			if ($ng_word["txt_match"] !== "exact") {
				$ng_word["txt_match"] = "partial"; // exact以外ならpartialに変更
			}
			if (!in_array($ng_word["lang_cd"], $lang_codes)) {
				$lang_codes[] = $ng_word["lang_cd"]; // データにある言語を抽出して配列を作成
			}
		}

		// 言語プルダウン
		$lang_cd_list = [];
		foreach($lang_codes as $lang) {
			$lang_cd_list[$lang] = $this->_codes['02'][$lang];
		}

		// ヘッダー
		$headers = [
			"No" => "No",
			"lang_cd" => "言語",
			"type" => "データ型",
			"txt_match" => "一致条件",
			"ng_word" => "NGワード",
			"operation" => "操作"
		];		
		$formatted_headers = [];
		foreach ($headers as $accessor => $header) {
			$formatted_headers[] = [
				"Header" => $header,
				"accessor" => $accessor
			];
		}

		$view = View::factory($this->_view_path . 'ngwords');
		$view->ng_words_json_encoded = json_encode($ng_words);
		$view->lang_cd_list_json_encoded = json_encode($lang_cd_list);
		$view->formatted_headers_json_encoded = json_encode($formatted_headers);
		$this->template->content = $view;
	}
}
