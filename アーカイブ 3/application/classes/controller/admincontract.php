<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Admincontract extends Controller_Template_Adminbase {

  public $_auth_required = TRUE;
	public $_transactional = true;
  public $_model;
  public $_contract_model;
  public $_view_path = 'admin/contract/';
  public $_action_path = '/admincontract/';

  public function __construct(Request $request, Response $response) {
    parent::__construct($request, $response);
    $this->_model = new Model_Adminmodel();
    $this->_contract_model = new Model_Admincontractmodel();
    $this->_model->init($this->_bot_id);
    ini_set('max_execution_time', 300);
  }

  // Controller
  public function action_clients() {
    $post = NULL;
    if ($this->request->post()) {
      $post = $this->request->post();
      if ($post['representative'] == "") {
        $post['representative'] = NULL;
      }
      if ($post['check_status'] == "") {
        $post['check_status'] = NULL;
      }
      if (!isset($post['show_invalid'])) {
        $post['show_invalid'] = NULL;
      }
      if (!isset($post['show_delete'])) {
        $post['show_delete'] = NULL;
      }
    } else {
      $post['representative'] = Session::instance()->get('contract_clients_representative', NULL);
      $post['check_status'] = Session::instance()->get('contract_clients_check_status', NULL);
      $post['show_invalid'] = Session::instance()->get('contract_clients_show_invalid', NULL);
      $post['show_delete'] = Session::instance()->get('contract_clients_show_delete', NULL);
    }
    Session::instance()->set('contract_clients_representative', $post['representative']);
    Session::instance()->set('contract_clients_check_status', $post['check_status']);
    Session::instance()->set('contract_clients_show_invalid', $post['show_invalid']);
    Session::instance()->set('contract_clients_show_delete', $post['show_delete']);
    $country_codes = $this->_model->get_code('55');
    $segments = $this->_contract_model->get_master_code_options(888811);
    $admin_users = $this->_contract_model->get_admin_users();
    $clients = $this->_contract_model->get_clients($post['representative'], $post['check_status'], $post['show_invalid'], $post['show_delete']);
    foreach ($clients as &$client) {
      $client['country'] = $country_codes[$client['country']] ?? '';
      $client['segment'] = $segments[$client['segment']] ?? '';
    }
    $view = View::factory($this->_view_path . 'clients');
    $view->clients = $clients;
    $view->admin_users = $admin_users;
    $view->post = $post;
    $this->template->content = $view;
  }

  public function action_client() {
    $client_code = $this->request->query('id', NULL);
    $post['client_code'] = '';
    $post['client_name'] = '';
    $post['client_name_for_search'] = '';
    $post['department_name'] = '';
    $post['department_name_for_search'] = '';
    $post['country'] = 'ja';
    $post['segment'] = '';
    $post['sales_representative'] = '';
    $post['cs_representative'] = '';
    if ($client_code != null) {
      $client = $this->_contract_model->get_client_by_code($client_code);
      if (count($client) > 0) {
        $client = $client[0];
        $post['client_code'] = $client['client_code'];
        $post['client_name'] = $client['client_name'];
        $post['client_name_for_search'] = $client['client_name_for_search'];
        $post['department_name'] = $client['department_name'];
        $post['department_name_for_search'] = $client['department_name_for_search'];
        $post['country'] = $client['country'] ?? '';
        $post['segment'] = $client['segment'] ?? '';
        $post['sales_representative'] = $client['sales_representative'] ?? '';
        $post['cs_representative'] = $client['cs_representative'] ?? '';
      } else {
        $this->redirect($this->_action_path . 'client');
        return;
      }
    }
    // country options
    $country_codes = $this->_model->get_code('55');
    $country_options = array();
    foreach ($country_codes as $key => $value) {
      $country_options[] = ['code' => $key, 'text' => $value];
    }
    // segment options
    $segment_options = $this->_contract_model->get_master_code_options(888811, true);
    // persons in charge options
    $users = $this->_model->get_users_by_roles(array(99));
    $user_options = array();
    foreach ($users as $user) {
      $user_options[] = ['code' => $user['user_id'], 'name' => $user['name']];
    }
    $client_diff = $this->_contract_model->get_diffs($client_code, 'client');
    $can_delete = $this->_contract_model->check_if_can_delete('client', $client_code);
    $view = View::factory($this->_view_path . 'client');
    $view->client_code = $client_code;
    $view->post = $post;
    $view->country_options = $country_options;
    $view->segment_options = $segment_options;
    $view->segment_maps = $this->_contract_model->get_master_code_options(888811, false);
    $view->user_options = $user_options;
    $view->client_diff = $client_diff;
    $view->can_delete = $can_delete;
    $this->template->content = $view;
  }

  public function action_invoices() {
    // paging が必要かな
    $post = NULL;
    if ($this->request->post()) {
      $post = $this->request->post();
      if ($post['representative'] == "") {
        $post['representative'] = NULL;
      }
      if ($post['check_status'] == "") {
        $post['check_status'] = NULL;
      }
      if (!isset($post['show_invalid'])) {
        $post['show_invalid'] = NULL;
      }
      if (!isset($post['show_delete'])) {
        $post['show_delete'] = NULL;
      }
      // if (!isset($post['show_expired'])) {
      //   $post['show_expired'] = NULL;
      // }
    } else {
      $post['representative'] = Session::instance()->get('contract_invoices_representative', NULL);
      $post['check_status'] = Session::instance()->get('contract_invoices_check_status', NULL);
      $post['show_invalid'] = Session::instance()->get('contract_invoices_show_invalid', NULL);
      $post['show_delete'] = Session::instance()->get('contract_invoices_show_delete', NULL);
      // $post['show_expired'] = Session::instance()->get('contract_invoices_show_expired', NULL);
    }
    Session::instance()->set('contract_invoices_representative', $post['representative']);
    Session::instance()->set('contract_invoices_check_status', $post['check_status']);
    Session::instance()->set('contract_invoices_show_invalid', $post['show_invalid']);
    Session::instance()->set('contract_invoices_show_delete', $post['show_delete']);
    // Session::instance()->set('contract_invoices_show_expired', $post['show_expired']);
    $invoices = $this->_contract_model->get_invoices(
      $post['representative'], 
      $post['check_status'], 
      NULL, NULL,
      $post['show_invalid'], 
      $post['show_delete']);
    $send_methods = [
      'mail' => 'メール',
      'postal' => '郵送',
      'informart' => 'インフォマート',
      'special' => '特殊対応'
    ];
    $payment_methods = [
      'bank' => '銀行振込',
      'robot_payment' => 'ロボットペイ'
    ];
    $invoice_send_timings = [
      '1' => '請求開始月 最初日',
      '2' => '請求開始月 最終日',
      '4' => '請求終了月 最初日',
      '5' => '請求終了月 最終日',
      '3' => '請求終了月 翌月初日',
      '6' => '請求開始前月 最終日'
    ];
    $invoice_spans = [
      '0' => '日割り',
      '1' => '１ヶ月',
      '2' => '２ヶ月',
      '3' => '３ヶ月',
      '4' => '４ヶ月',
      '5' => '５ヶ月',
      '6' => '６ヶ月',
      '7' => '７ヶ月',
      '8' => '８ヶ月',
      '9' => '９ヶ月',
      '10' => '１０ヶ月',
      '11' => '１１ヶ月',
      '12' => '１２ヶ月',
      '24' => '２４ヶ月',
      '36' => '３６ヶ月',
    ];
    $payment_timings = [
      '1' => '翌月末日',
      '2' => '当月末日',
      '3' => '翌々月5日',
      '4' => '翌々月末日',
      '5' => '翌々月10日',
      '6' => '翌月10日'
    ];
    $living_invoices = [];
    $expired_invoices = [];
    foreach ($invoices as $invoice) {
      if ($invoice['ver_end_date']) {
        if (strtotime($invoice['ver_end_date']) < strtotime(date('Y-m-d'))) {
          $invoice['expired'] = true;
          $expired_invoices[] = $invoice;
          continue;
        }
      }
      $invoice['expired'] = false;
      $living_invoices[] = $invoice;
    }
    $invoices = array_merge($living_invoices, []);
    // if ($post['show_expired']) {
    $invoices = array_merge($invoices, $expired_invoices, []);
    // }
    $admin_users = $this->_contract_model->get_admin_users();
    $view = View::factory($this->_view_path . 'invoices');
    $view->invoices = $invoices;
    $view->send_methods = $send_methods;
    $view->payment_methods = $payment_methods;
    $view->invoice_send_timings = $invoice_send_timings;
    $view->invoice_spans = $invoice_spans;
    $view->payment_settings = $payment_timings;
    $view->admin_users = $admin_users;
    $view->post = $post;
    $this->template->content = $view;
  }

  public function action_invoice() {
    $invoice_code = $this->request->query('id', NULL);
    // $can_edit = true;
    $seq = $this->request->query('seq', NULL);
    if ($seq == null) {
      $seq = 1;
    }
    $post['invoice_code'] = '';
    $post['seq'] = $seq;
    $post['ver_start_date'] = '';
    $post['ver_end_date'] = '';
    $post['invoice_name'] = '';
    $post['department_name'] = '';
    $post['invoice_recipient_to'] = '';
    $post['invoice_recipient_cc'] = '';
    $post['invoice_address'] = '';
    $post['send_method'] = 'mail';
    $post['invoice_send_timing'] = '1';
    $post['invoice_span'] = '1';
    $post['payment_timing'] = '1';
    $post['payment_method'] = 'bank';
    if ($invoice_code != null) {
      // $can_edit = $this->_contract_model->can_edit_invoice($invoice_code);
      $invoice = $this->_contract_model->get_invoice_by_code($invoice_code, $seq);
      if ($invoice != null) {
        $post['invoice_code'] = $invoice['invoice_code'];
        $post['seq'] = $invoice['seq'];
        $post['ver_start_date'] = $invoice['ver_start_date'];
        $post['ver_end_date'] = $invoice['ver_end_date'];
        $post['invoice_name'] = $invoice['invoice_name'];
        $post['department_name'] = $invoice['department_name'];
        $post['invoice_recipient_to'] = $invoice['invoice_recipient_to'];
        $post['invoice_recipient_cc'] = $invoice['invoice_recipient_cc'];
        $post['invoice_address'] = $invoice['invoice_address'];
        $post['send_method'] = $invoice['send_method'];
        $post['invoice_send_timing'] = $invoice['invoice_send_timing'];
        $post['invoice_span'] = $invoice['invoice_span'];
        $post['payment_timing'] = $invoice['payment_timing'];
        $post['payment_method'] = $invoice['payment_method'];
      } else {
        $this->redirect($this->_action_path . 'invoice');
      }
    }

    $send_methods = [
      ['code' => 'mail', 'text' => 'メール'],
      ['code' => 'postal', 'text' => '郵送'],
      ['code' => 'informart', 'text' => 'インフォマート'],
      ['code' => 'special', 'text' => '特殊対応']
    ];
    $payment_methods = [
      ['code' => 'bank', 'text' => '銀行振込'],
      ['code' => 'robot_payment', 'text' => 'ロボットペイ'],
    ];
    $invoice_send_timings = [
      ['code' => '1', 'text' => '請求開始月 最初日'],
      ['code' => '2', 'text' => '請求開始月 最終日'],
      ['code' => '4', 'text' => '請求終了月 最初日'],
      ['code' => '5', 'text' => '請求終了月 最終日'],
      ['code' => '3', 'text' => '請求終了月 翌月初日'],
      ['code' => '6', 'text' => '請求開始前月 最終日']
    ];
    $invoice_spans = [
      ['code' => '0', 'text' => '日割り'],
      ['code' => '1', 'text' => '１ヶ月'],
      ['code' => '2', 'text' => '２ヶ月'],
      ['code' => '3', 'text' => '３ヶ月'],
      ['code' => '4', 'text' => '４ヶ月'],
      ['code' => '5', 'text' => '５ヶ月'],
      ['code' => '6', 'text' => '６ヶ月'],
      ['code' => '7', 'text' => '７ヶ月'],
      ['code' => '8', 'text' => '８ヶ月'],
      ['code' => '9', 'text' => '９ヶ月'],
      ['code' => '10', 'text' => '１０ヶ月'],
      ['code' => '11', 'text' => '１１ヶ月'],
      ['code' => '12', 'text' => '１２ヶ月'],
      ['code' => '24', 'text' => '２４ヶ月'],
      ['code' => '36', 'text' => '３６ヶ月'],
    ];
    $payment_timings = [
      ['code' => '1', 'text' => '翌月末日'],
      ['code' => '6', 'text' => '翌月10日'],
      ['code' => '2', 'text' => '当月末日'],
      ['code' => '3', 'text' => '翌々月5日'],
      ['code' => '5', 'text' => '翌々月10日'],
      ['code' => '4', 'text' => '翌々月末日'],
    ];
    $existed_invoices = $this->_contract_model->get_invoices_option();
    $existed_codes = [];
    foreach ($existed_invoices as $invoice) {
      if (!in_array($invoice['code'], $existed_codes)) {
        $existed_codes[] = $invoice['code'];
      }
    }
    $invoice_diff = $this->_contract_model->get_diffs($invoice_code, 'invoice', $seq);
    $can_delete = $this->_contract_model->check_if_can_delete('invoice', $invoice_code);
    $generation_options = [];
    if ($invoice_code !== NULL) {
      $generations = $this->_contract_model->get_invoice_gens($invoice_code);
      foreach ($generations as $gen) {
        $generation_options[] = ['code' => $gen['seq'], 'text' => '第' . $gen['seq'] . '世代 - ' . $gen['name']];
      }
    }

    $view = View::factory($this->_view_path . 'invoice');
    $view->invoice_code = $post['invoice_code'];
    $view->seq = $post['seq'];
    $view->post = $post;
    $view->send_methods = $send_methods;
    $view->payment_methods = $payment_methods;
    $view->invoice_send_timings = $invoice_send_timings;
    $view->invoice_spans = $invoice_spans;
    $view->payment_timings = $payment_timings;
    $view->invoice_diff = $invoice_diff;
    $view->existed_codes = $existed_codes;
    $view->can_delete = $can_delete;
    $view->generation_options = $generation_options;
    $this->template->content = $view;
  }

  public function action_contracts() {
    $post = NULL;
    if ($this->request->post()) {
      $post = $this->request->post();
      if ($post['representative'] === "") {
        $post['representative'] = NULL;
      }
      if ($post['check_status'] === "") {
        $post['check_status'] = NULL;
      }
      if (!isset($post['show_invalid'])) {
        $post['show_invalid'] = NULL;
      }
      if (!isset($post['show_delete'])) {
        $post['show_delete'] = NULL;
      }
    } else {
      $post['representative'] = Session::instance()->get('contract_contracts_representative', NULL);
      $post['check_status'] = Session::instance()->get('contract_contracts_check_status', NULL);
      $post['show_invalid'] = Session::instance()->get('contract_contracts_show_invalid', NULL);
      $post['show_delete'] = Session::instance()->get('contract_contracts_show_delete', NULL);
    }
    Session::instance()->set('contract_contracts_representative', $post['representative']);
    Session::instance()->set('contract_contracts_check_status', $post['check_status']);
    Session::instance()->set('contract_contracts_show_invalid', $post['show_invalid']);
    Session::instance()->set('contract_contracts_show_delete', $post['show_delete']);
    $contracts = $this->_contract_model->get_contracts($post['representative'], $post['check_status'], $post['show_invalid'], $post['show_delete']);
    $items = $this->_contract_model->get_master_code_options(888812);
    $clients = $this->_contract_model->get_clients_option();
    $invoices = $this->_contract_model->get_invoices_option();
    $segments = $this->_contract_model->get_master_code_options(888811);
    $cost_type_options = [
      '1' => '初期費用',
      '2' => '月額費用',
      '3' => 'その他（単発）',
      '4' => 'その他（継続発生）'
    ];
    $bots = $this->_model->get_bots();
    foreach ($contracts as &$contract) {
      $client = array_filter($clients, function($client) use ($contract) {
        return $client['code'] == $contract['client_code'];
      });
      $client = array_merge($client, []);
      $contract['client_name'] = count($client) > 0 ? $client[0]['text'] : '';
      $contract['client_link'] = '/admincontract/client?id=' . $contract['client_code'];

      $invoice = array_filter($invoices, function($invoice) use ($contract) {
        return $invoice['code'] == $contract['invoice_code'];
      });
      $invoice = array_merge($invoice, []);
      $contract['invoice_name'] = count($invoice) > 0 ? $invoice[0]['text'] . ($invoice[0]['seq'] >= 2 ? ' - ' . '第'  . $invoice[0]['seq'] . '世代':'') : '';
      $contract['invoice_link'] = '/admincontract/invoice?id=' . $contract['invoice_code'] . '&seq=' . $invoice[0]['seq'];
      
      $billing_account = explode(',', $contract['billing_account']);
      if (count($billing_account) > 0) {
        $billing_account = array_map(function($account) use ($bots) {
          return $bots[$account];
        }, $billing_account);
        $contract['billing_account_name'] = implode(',', $billing_account);
      } else {
        $contract['billing_account_name'] = '';
      }
      $contract['item_name'] = $items[$contract['item']] ?? '';
      $contract['segment'] = $segments[$contract['segment']] ?? '';
    }
    $admin_users = $this->_contract_model->get_admin_users();
    $view = View::factory($this->_view_path . 'contracts');
    $view->contracts = $contracts;
    $view->cost_type_options = $cost_type_options;
    $view->admin_users = $admin_users;
    $view->post = $post;
    $this->template->content = $view;
  }

  public function action_details() {
    $representatives = NULL;
    $check_status = NULL;
    if ($this->request->post()) {
      $post = $this->request->post();
      $representatives = $post['representative'] == "" ? NULL : $post['representative'];
      $check_status = $post['check_status'] == "" ? NULL : $post['check_status'];
    }
    $post['representative'] = $representatives;
    $post['check_status'] = $check_status;
    $client_code = $this->_contract_model->get_client_code_by_bot_id($this->_bot_id);
    $contracts = $this->_contract_model->get_contracts_by_client_code($client_code);
    $items = $this->_contract_model->get_master_code_options(888812);
    $clients = $this->_contract_model->get_clients_option();
    $invoices = $this->_contract_model->get_invoices_option();
    $segments = $this->_contract_model->get_master_code_options(888811);
    $cost_type_options = [
      '1' => '初期費用',
      '2' => '月額費用',
      '3' => 'その他（単発）',
      '4' => 'その他（継続発生）',
      '5' => 'その他（周期発生）'
    ];
    $bots = $this->_model->get_bots();

    foreach ($contracts as &$contract) {
      $client = array_filter($clients, function($client) use ($contract) {
        return $client['code'] == $contract['client_code'];
      });
      $client = array_merge($client, []);
      $contract['client_name'] = count($client) > 0 ? $client[0]['text'] : '';
      $contract['client_link'] = '/admincontract/client?id=' . $contract['client_code'];

      $invoice = array_filter($invoices, function($invoice) use ($contract) {
        return $invoice['code'] == $contract['invoice_code'];
      });
      $invoice = array_merge($invoice, []);
      $contract['invoice_name'] = count($invoice) > 0 ? $invoice[0]['text'] . ($invoice[0]['seq'] >= 2 ? ' - ' . '第'  . $invoice[0]['seq'] . '世代':'') : '';
      $contract['invoice_link'] = '/admincontract/invoice?id=' . $contract['invoice_code'] . '&seq=' . $invoice[0]['seq'];

      $billing_account = explode(',', $contract['billing_account']);
      if (count($billing_account) > 0) {
        $billing_account = array_map(function($account) use ($bots) {
          return $bots[$account];
        }, $billing_account);
        $contract['billing_account_name'] = implode(',', $billing_account);
      } else {
        $contract['billing_account_name'] = '';
      }
      $contract['item_name'] = $items[$contract['item']] ?? '';
      $contract['segment'] = $segments[$contract['segment']] ?? '';
    }
    $admin_users = $this->_contract_model->get_admin_users();
    $view = View::factory($this->_view_path . 'details');
    $view->contracts = $contracts;
    $view->cost_type_options = $cost_type_options;
    $view->admin_users = $admin_users;
    $view->post = $post;
    $this->template->content = $view;
  }

  public function action_contract() {
    $seq_index = NULL;
    $contract_id = $this->request->query('id');
    $client_options = $this->_contract_model->get_clients_option();
    $invoice_options = $this->_contract_model->get_invoices_option();
    $item_options = $this->_contract_model->get_master_code_options(888812, true);
    $cost_type_options = [
      ['code' => '1', 'text' => '初期費用'],
      ['code' => '2', 'text' => '月額費用'],
      ['code' => '3', 'text' => 'その他（単発）'],
      ['code' => '4', 'text' => 'その他（継続発生）'],
      ['code' => '5', 'text' => 'その他（周期発生）'],
    ];
    $bots = $this->_model->get_bots();
    $bot_options = [];
    foreach ($bots as $bot_id => $bot_name) {
      $bot_options[] = ['code' => strval($bot_id), 'name' => $bot_name];
    }
    $formated_contract_data = [
      'client_code' => '',
      'invoice_code' => '',
      'tax_rate' => '10',
      'items' => NULL
    ];
    $servicein_data = [];
    if ($contract_id !== NULL) {
      $contract_data = $this->_contract_model->get_contract_by_id($contract_id);
      if ($contract_data == null) {
        $this->redirect($this->_action_path . 'contract');
        return;
      }
      $seq_index = $this->_contract_model->get_contract_setting($contract_id, 'seq_index');
      $contract_set = $this->_contract_model->get_contract_setting($contract_id, 'contract_set');
      $bot_ids = [];
      foreach ($contract_data as $data) {
        $billing_account = $data['billing_account'];
        if ($billing_account) {
          $billing_account = explode(',', $billing_account);
          $bot_ids = array_merge($bot_ids, $billing_account);
        }
      }
      $bot_ids = array_merge(array_unique($bot_ids), []);
      $servicein_data = $this->_contract_model->get_servicein_datas($bot_ids);
      $formated_contract_data = $this->_contract_model->format_contract_data($contract_data);
    }
    $contract_diff = $this->_contract_model->get_diffs($contract_id, 'contract');
     // persons in charge options
    $users = $this->_model->get_users_by_roles(array(99));
    $user_options = array();
    foreach ($users as $user) {
      $user_options[] = ['code' => $user['user_id'], 'name' => $user['name']];
    }
    $can_delete = $this->_contract_model->check_if_can_delete('contract', $contract_id);
    $view = View::factory($this->_view_path . 'contract');
    $view->seq_index = $seq_index ?? 0;
    $view->contract_set = $contract_set;
    $view->contract_id = $contract_id;
    $view->contract_data = $formated_contract_data;
    $view->servicein_data = $servicein_data;
    $view->client_options = $client_options;
    $view->invoice_options = $invoice_options;
    $view->bot_options = $bot_options;
    $view->item_options = $item_options;
    $view->cost_type_options = $cost_type_options;
    $view->contract_diff = $contract_diff;
    $view->user_options = $user_options;
    $view->can_delete = $can_delete;
    $this->template->content = $view;
  }

  public function action_invoicepayments() {
    $invoice_options = $this->_contract_model->get_all_invoices();
    $invoice_payments = $this->_contract_model->get_invoice_payments();
    $invoice_checks = $this->_contract_model->get_all_checks('invoice');
    $contract_checks = $this->_contract_model->get_all_checks('contract');
    $view = View::factory($this->_view_path . 'invoicepayments');
    $view->invoice_payments = $invoice_payments;
    $view->invoice_options = $invoice_options;
    $view->invoice_checks = $invoice_checks;
    $view->contract_checks = $contract_checks;
    $this->template->content = $view;
  }

  public function action_billing() {
    $invoice_code = $this->_contract_model->get_invoice_code_by_bot_id($this->_bot_id);
    $invoice_options = $this->_contract_model->get_invoices_by_invoice_code($invoice_code);
    $invoice_payments = $this->_contract_model->get_invoice_payments_by_invoice_code($invoice_code);
    $invoice_checks = $this->_contract_model->get_all_checks('invoice');
    $contract_checks = $this->_contract_model->get_all_checks('contract');
    $view = View::factory($this->_view_path . 'billing');
    $view->invoice_payments = $invoice_payments;
    $view->invoice_options = $invoice_options;
    $view->invoice_checks = $invoice_checks;
    $view->contract_checks = $contract_checks;
    $this->template->content = $view;
  }

  public function action_contractattachment() {
    $client_code = $this->request->query('client_code', NULL);
    if (is_null($client_code)) {
      $this->redirect($this->_action_path . 'clients');
      return;
    }
    $client = $this->_contract_model->get_client_by_code($client_code);
    if (count($client) == 0) {
      $this->redirect($this->_action_path . 'clients');
      return;
    }
    $client = $client[0];
    $results = $this->_contract_model->get_contract_attachment_data($client_code);
    $diff_data = $results['diff_data'];
    $diff_messages = $this->_contract_model->format_contractattachment_diff($diff_data);
    $bots = $this->_model->get_bots();
    $existed_attachments = $this->_contract_model->get_created_contractattachments($client_code);
    $attachment_table = $this->_contract_model->generate_attachment_table($results['monthly_attachment_data'], $results['initial_attachment_data']);
    $view = View::factory($this->_view_path . 'contractattachment');
    $view->client_code = $client_code;
    $view->client = $client;
    $view->attachment_table = $attachment_table;
    $view->existed_attachments = $existed_attachments;
    $view->diff_data = $diff_data;
    $view->diff_messages = $diff_messages;
    $view->bots = $bots;
    $this->template->content = $view;
  }

  public function action_createcontractattachment() {
    $client_code = $this->request->query('client_code', NULL);
    $lastest_version = $this->_contract_model->get_created_contractattachments($client_code, true);
    $view = View::factory($this->_view_path . 'createcontractattachment');
    $view->client_code = $client_code;
    $view->lastest_version = $lastest_version;
    $this->template->content = $view;
  }

  public function action_editcontractattachment() {
    $client_code = $this->request->query('client_code', NULL);
    $version = $this->request->query('version', NULL);
    if (is_null($client_code)) {
      $this->redirect($this->_action_path . 'clients');
    }
    if (is_null($version)) {
      $this->redirect($this->_action_path . 'clients');
    }
    $contract_attachment_data = $this->_contract_model->get_created_contractattachment_by_code($client_code, $version);
    if (is_null($contract_attachment_data)) {
      $this->redirect($this->_action_path . 'clients');
    }
    $contract_data = json_decode($contract_attachment_data['contract_data'], true);
    $diff_data = json_decode($contract_attachment_data['diff_data'], true);
    $common_data = [
      'attach_date' => $contract_attachment_data['attach_date'],
      'create_date' => $contract_attachment_data['create_date'],
      'remark' => '',
      'very_remark' => ''
    ];
    if ($contract_attachment_data['remark']) {
      $remarks = json_decode($contract_attachment_data['remark'], true);
      $common_data['remark'] = $remarks['remark'] ?? '';
      $common_data['very_remark'] = $remarks['very_remark'] ?? '';
    }
    $view = View::factory($this->_view_path . 'editcontractattachment');
    $view->client_code = $client_code;
    $view->version = $version;
    $view->common_data = $common_data;
    $view->contract_data = $contract_data;
    $view->diff_data = $diff_data;
    $this->template->content = $view;
  }

  public function action_checklist() {
    $post = NULL;
    if ($this->request->post()) {
      $post = $this->request->post();
      if ($post['representative'] == "") {
        $post['representative'] = NULL;
      }
      $post['exclude_admin'] = isset($post['exclude_admin']) ? true : false;
      $post['exclude_other'] = isset($post['exclude_other']) ? true : false;
    } else {
      $post['representative'] = Session::instance()->get('contract_checklist_representative', NULL);
      $post['exclude_admin'] = Session::instance()->get('contract_checklist_exclude_admin', NULL);
      $post['exclude_other'] = Session::instance()->get('contract_checklist_exclude_other', NULL);
    }
    Session::instance()->set('contract_checklist_representative', $post['representative']);
    Session::instance()->set('contract_checklist_exclude_admin', $post['exclude_admin']);
    Session::instance()->set('contract_checklist_exclude_other', $post['exclude_other']);
    $items = $this->_contract_model->get_master_code_options(888812);
    $admin_users = $this->_contract_model->get_admin_users();
    $checklist = $this->_contract_model->get_checklist($post['representative'], $post['exclude_admin'], $post['exclude_other']);
    $view = View::factory($this->_view_path . 'checklist');
    $view->checklist = $checklist;
    $view->items = $items;
    $view->post = $post;
    $view->admin_users = $admin_users;
    $this->template->content = $view;
  }
  // Controller End


  // API
  
  public function action_clientpost() {
    $this->auto_render = FALSE;
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $client_code = $this->request->query('id', NULL);
        $post = $this->request->post();
        if ($client_code == '') {
          $code = $this->_contract_model->create_new_client($post);
          // add check flg
          $this->_contract_model->add_checkflg($code, 'client', NULL, 'admin');
          $this->response->status(200)->body(json_encode(['result' => 'success', 'client_code' => $code], JSON_UNESCAPED_UNICODE));
        } else {
          $client_diff = $this->_contract_model->diff_client($client_code, $post);
          if (count($client_diff) > 0) {
            $this->_contract_model->record_diff($client_code, 'client', $client_diff, $this->_user_id);
            $this->_contract_model->update_client($client_code, $post);
            if ($this->_contract_model->get_checkstatus($client_code, 'client') == NULL) {
              $this->_contract_model->add_checkflg($client_code, 'client', NULL, 'admin');
            } else if (!$this->_contract_model->is_in_check($client_code, 'client')) {
              $this->_contract_model->add_checkflg($client_code, 'client', NULL, 'admin');
            }
          }
          $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
        }
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_clientdelete() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $client_code = $post['id'];
        $type = $post['type'];
        $this->_contract_model->delete_client_by_code($client_code, $type, $this->_user_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_invoicepost() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $invoice_code = $this->request->query('id', NULL);
        $post = $this->request->post();
        if ($post['ver_start_date'] == '') {
          $post['ver_start_date'] = null;
        }
        if ($post['ver_end_date'] == '') {
          $post['ver_end_date'] = null;
        }
        if ($invoice_code == '') {
          $code = $this->_contract_model->create_new_invoice($post);
          // add check flg
          $this->_contract_model->add_checkflg($code, 'invoice', $post['seq'], 'admin');
          $this->response->status(200)->body(json_encode(['result' => 'success', 'invoice_code' => $code], JSON_UNESCAPED_UNICODE));
        } else {
          if ($invoice_code != $post['invoice_code']) {
            $is_exist = $this->_contract_model->check_invoice_existed($post['invoice_code']);
            if ($is_exist) {
              $this->response->status(400)->body(json_encode(['result' => 'fail', 'error' => 'invoice_code exists'], JSON_UNESCAPED_UNICODE));
              return;
            }
          }
          $upd_diff = $this->_contract_model->diff_invoice($invoice_code, $post['seq'], $post);
          if (count($upd_diff) > 0) {
            $this->_contract_model->record_diff($invoice_code, 'invoice', $upd_diff, $this->_user_id, $post['seq']);
            $this->_contract_model->update_invoice($invoice_code, $post);
            if ($this->_contract_model->get_checkstatus($invoice_code, 'invoice', $post['seq']) == NULL) {
              $this->_contract_model->add_checkflg($invoice_code, 'invoice', $post['seq'], 'admin');
            } else if (!$this->_contract_model->is_in_check($invoice_code, 'invoice', $post['seq'])) {
              $this->_contract_model->add_checkflg($invoice_code, 'invoice', $post['seq'], 'admin');
            }
          }
          $this->response->status(200)->body(json_encode(['result' => 'success', 'new_invoice_code' => $post['invoice_code']], JSON_UNESCAPED_UNICODE));
        }
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_invoicedelete() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $invoice_code = $post['invoice_code'];
        $seq = $post['seq'];
        $type = $post['type'];
        if (!$invoice_code || !$seq) {
          $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
          return;
        }
        $this->_contract_model->delete_invoice_by_code($invoice_code, $seq, $type, $this->_user_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_new_invoice_generation() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $invoice_code = $post['invoice_code'];
        $old_generation_end_date = $post['end_date'];
        $new_generation_start_date = $post['start_date'];
        $newseq = $this->_contract_model->create_new_generation_for_invoice($invoice_code, $old_generation_end_date, $new_generation_start_date);
        $this->response->status(200)->body(json_encode(['result' => 'success', 'data' => ['invoice_code' => $invoice_code, 'seq' => $newseq]], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_contractpost() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $contract_id = $this->request->query('id');
        if ($contract_id == NULL) {
          $code = $this->_contract_model->create_contract($post);
          $this->_contract_model->upsert_contract_setting($code, $post['contract_setting'], $this->_user_id);
          $upd_diff = $this->_contract_model->newcontract_log($post);
          $this->_contract_model->record_diff($code, 'contract', $upd_diff, $this->_user_id);
          $this->_contract_model->add_checkflg($code, 'contract', NULL, 'admin');
          $this->response->status(200)->body(json_encode(['result' => 'success', 'contract_id' => $code], JSON_UNESCAPED_UNICODE));
        } else {
          $upd_diff = $this->_contract_model->diff_contract($contract_id, $post);
          $this->_contract_model->upsert_contract_setting($contract_id, $post['contract_setting'], $this->_user_id);
          if (count($upd_diff) > 0) {
            // 契約は変更があります
            $this->_contract_model->record_diff($contract_id, 'contract', $upd_diff, $this->_user_id);
            $this->_contract_model->update_contract($contract_id, $post, $upd_diff);
            $need_recheck = $this->_contract_model->check_if_need_recheck($upd_diff);
            if ($need_recheck) {
              // add: check_flg
              if ($this->_contract_model->is_in_check($contract_id, 'contract')) {
                $this->_contract_model->reset_checkstatus($contract_id, 'contract', NULL, 'admin');
              } else {
                $this->_contract_model->add_checkflg($contract_id, 'contract', NULL, 'admin');
              }
            } else {
              if (count($upd_diff) >= 1 || !isset($upd_diff['memo'])) {
                if ($this->_contract_model->get_checkstatus($contract_id, 'contract') == NULL) {
                  $this->_contract_model->add_checkflg($contract_id, 'contract', NULL, 'admin');
                } else if (!$this->_contract_model->is_in_check($contract_id, 'contract')) {
                  $this->_contract_model->add_checkflg($contract_id, 'contract', NULL, 'admin');
                }
              }
            }
          }
          $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
        }
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\ErrorException $error) {
      $this->response->status(500)->body(json_encode(['result' => 'fail', 'error' => $error->getMessage()], JSON_UNESCAPED_UNICODE));
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_contractdelete() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $contract_id = $post['id'];
        $type = $post['type'];
        $this->_contract_model->delete_contract_by_id($contract_id, $type, $this->_user_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_invoicepaymentdelete() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $invoice_id = $post['id'];
        $this->_contract_model->delete_invoice_payment_by_id($invoice_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_invoicepaymentregenerate() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $invoice_id = $post['id'];
        // 再生成の仕様が決まったらコメントアウトを外す
        // $this->_contract_model->regenerate_invoicepayment($invoice_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_sendinvoicepayment() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $invoice_id = $post['id'];
        $this->_contract_model->sendpaymentmail($invoice_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  // 送付状態を「送付済」に変更
  public function action_invoicepaymentchangestatustodone() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        if (!isset($post['id'])) {
          throw new Exception("no invoice id");
        }
        $this->_contract_model->set_invoicepayment_status($post['id'], 'status', 1);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  // 請求書を無効にする
  public function action_setinvoicepaymentinvalid() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        if (!isset($post['id'])) {
          throw new Exception("no invoice id");
        }
        $this->_contract_model->set_invoicepayment_status($post['id'], 'invalid_flg', 1);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  // 請求書の入金状況を変更
  public function action_setinvoicepaymentreceipt() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        if (!isset($post['id'])) {
          throw new Exception("no invoice id");
        }
        $this->_contract_model->set_invoicepayment_status($post['id'], 'receipt_flg', 1);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_getdiffs() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->query()) {
        $code = $this->request->query('code', NULL);
        if (is_null($code)) {
          throw new Exception("no code");
        }
        $type = $this->request->query('type', NULL);
        if (is_null($type)) {
          throw new Exception("no type");
        }
        $seq = $this->request->query('seq', NULL);
        $diffs = $this->_contract_model->get_diffs($code, $type, $seq);
        $this->response->status(200)->body(json_encode(['result' => 'success', 'data' => $diffs], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_deletediff() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->_user->role != 99) {
        throw new Exception("no permission");
      }
      if ($this->request->post()) {
        $diff_id = $this->request->post('id', NULL);
        if (is_null($diff_id)) {
          throw new Exception("no diff id");
        }
        $this->_contract_model->delete_diff_by_id($diff_id, $this->_user_id);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_getcheckstatus() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->query()) {
        $code = $this->request->query('code');
        $type = $this->request->query('type');
        $seq = $this->request->query('seq', NULL);
        $check_status = $this->_contract_model->get_checkstatus($code, $type, $seq);
        $this->response->status(200)->body(json_encode(['result' => 'success', 'data' => $check_status], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_checktoggle() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $id = $post['id'];
        $type = $post['type'];
        $warnings = isset($post['warnings']) ? $post['warnings'] : NULL;
        $this->_contract_model->before_check($id, $type, $this->_user);
        $this->_contract_model->set_check($id, $type, $this->_user_id, $warnings);
        $check_status = $this->_contract_model->get_checkstatus_by_id($id);
        if ($type === 'admin_checkflg') {
          // try to generate invoice payment
          $linked_contracts = $this->_contract_model->get_all_linked_contracts($check_status['code'], $check_status['type']);
          $new_invoicepayments = [];
          foreach ($linked_contracts as $contract_id) {
            try {
              $invoice_ids = $this->_contract_model->generate_invoicepayment_by_id($contract_id, NULL, true);
              if (count($invoice_ids) > 0) {
                $new_invoicepayments = array_merge($new_invoicepayments, $invoice_ids);
              }
            } catch (\Throwable $th) {
              Log::instance()->add(Log::DEBUG, "ERROR: Contract ID: " . $contract_id . " - " . $th->getMessage());
            }
          }
          // try to send mail
          $send_invoicepayments = [];
          if (count($new_invoicepayments) > 0) {
            foreach ($new_invoicepayments as $invoice_id) {
              try {
                $new_invoicepayment = $this->_contract_model->get_invoicepayment_by_id($invoice_id);
                if (strtotime($new_invoicepayment['invoice_date']) <= strtotime(date('Y-m-d'))) {
                  $send_invoicepayments[] = $invoice_id;
                  $this->_contract_model->sendpaymentmail($invoice_id);
                }
              } catch (\Throwable $th) {
                Log::instance()->add(Log::DEBUG, "Send Invoicepayment ERROR: Invoice ID: " . $invoice_id . " - " . $th->getMessage());
              }
            }
            $this->response->status(200)->body(json_encode(['result' => 'success', 'new_data' => $new_invoicepayments, 'send_data' => $send_invoicepayments], JSON_UNESCAPED_UNICODE));
          } else {
            $this->response->status(200)->body(json_encode(['result' => 'success', 'new_data' => $new_invoicepayments], JSON_UNESCAPED_UNICODE));
          }
        } else {
          $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
        }
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\ErrorException $error) {
      $this->response->status(200)->body(json_encode(['error' => $error->getMessage()], JSON_UNESCAPED_UNICODE));
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['result' => 'fail', 'error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_deletewarning() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $check_id = $post['id'];
        $seq = $post['seq'];
        $this->_contract_model->delete_warning($check_id, $seq);
        $this->response->status(200)->body(json_encode(['result' => 'success', 'data' => $seq], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['result' => 'fail', 'error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_exportsales() {
    $create_time = date('YmdHis');
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type: application/octet-stream');
    $this->response->headers('Content-Disposition: attachment; filename="売上データ-' . $create_time . '.csv"');
    try {
      if ($this->request->post()) { 
        $post = $this->request->post();
        $start_date = $post['start_date'];
        $end_date = $post['end_date'];
        if ($start_date == '' || $end_date == '') {
          throw new Exception('no date');
        }
        $type = $post['type'];
        $datas = $this->_contract_model->get_sales($start_date, $end_date, $type);
        $result = [
          'result' => 'success',
          'data' => $datas
        ];
        $this->response->status(200)->body(json_encode($result, JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['result' => 'fail', 'error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_invoicepaymentpdf() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      $invoice_id = $this->request->query('id');
      if ($invoice_id == null) {
        throw new Exception('no invoice_id');
      }
      $preview = $this->request->query('preview');
      if ($preview == '1') {
        $pdf_content = $this->_contract_model->generate_invoicepayment_pdf($invoice_id, false);
        if ($pdf_content == '' || $pdf_content == null) {
          throw new ErrorException('no_pdf');
        }
        $pdf_base64 = base64_encode($pdf_content);
        $response = [
          'result' => 'success',
          'data' => $pdf_base64
        ];
        $this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
      } else {
        $pdf = $this->_contract_model->generate_invoicepayment_pdf($invoice_id);
        if ($pdf == 'no_data') {
          throw new Exception('no data');
        }
      }
    } catch (\Throwable $th) {
      Log::instance()->add(Log::ERROR, $th->getMessage());
      throw new Kohana_HTTP_Exception_404();
    }
  }

  public function action_exportinvoicepaymentscsv() {
    $create_time = date('YmdHis');
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type: application/octet-stream');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        $start_date = $post['start_date'];
        $end_date = $post['end_date'];
        $period = '';
        if ($start_date !== '') {
          $period = "$start_date~";
        }
        if ($end_date !== '') {
          if ($period !== '') {
            $period .= $end_date;
          } else {
            $period .= "~$end_date";
          }
        }
        if ($period === '') {
          $period = '全期間';
        }
        // $this->response->headers('Content-Disposition: attachment; filename="請求書データ-' . $period . '_' . $create_time . '.csv"');
        $datas = $this->_contract_model->get_invoicepayments_csvdatas($start_date, $end_date);
        $result = [
          'result' => 'success',
          'data' => $datas
        ];
        $this->response->status(200)->body(json_encode($result, JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      $this->response->status(500)->body(json_encode(['result' => 'fail', 'error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_contractattachmentpost() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        // $client_code = $this->request->query('client_code', NULL);
        $post = $this->request->post();
        $client_code = isset($post['client_code']) ? $post['client_code'] : NULL;
        if ($client_code == '') {
          throw new Exception('no client code');
        }
        $results = $this->_contract_model->create_contractattachment($client_code, $post);
        $this->response->status(200)->body(json_encode(['result' => 'success', 'data' => $results], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      Log::instance()->add(Log::ERROR, $th->getMessage());
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_contractattachmentpdf() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      $client_code = $this->request->query('client_code', null);
      if ($client_code == null) {
        throw new Exception('no client_code');
      }
      $version = $this->request->query('version', null);
      if ($version == null) {
        throw new Exception('no version');
      }
      $preview = $this->request->query('preview');
      if ($preview == '1') {
        $pdf_content = $this->_contract_model->generate_contractattachment_pdf($client_code, $version, false);
        if ($pdf_content == '' || $pdf_content == null) {
          throw new ErrorException('no_pdf');
        }
        $pdf_base64 = base64_encode($pdf_content);
        $response = [
          'result' => 'success',
          'data' => $pdf_base64
        ];
        $this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
      } else {
        $pdf = $this->_contract_model->generate_contractattachment_pdf($client_code, $version);
        if ($pdf == 'no_data') {
          throw new Exception('no data');
        }
      }
    } catch (\Throwable $th) {
      Log::instance()->add(Log::ERROR, $th->getMessage());
      throw new Kohana_HTTP_Exception_404();
    }
  }

  public function action_editcontractattachmentpost() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      $client_code = $this->request->query('client_code', null);
      if ($client_code == null) {
        throw new Exception('no client code');
      }
      $version = $this->request->query('version', null);
      if ($version == null) {
        throw new Exception('no version');
      }
      if ($this->request->post()) {
        $post = $this->request->post();
        $this->_contract_model->update_contractattachment($client_code, $version, $post);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      Log::instance()->add(Log::ERROR, $th->getMessage());
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }

  public function action_deletecontractattachmentpost() {
    $this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
    try {
      if ($this->request->post()) {
        $post = $this->request->post();
        if ($post['client_code'] == null) {
          throw new Exception('no client code');
        }
        if ($post['version'] == null) {
          throw new Exception('no version');
        }
        $this->_contract_model->delete_contractattachment($post['client_code'], $post['version']);
        $this->response->status(200)->body(json_encode(['result' => 'success'], JSON_UNESCAPED_UNICODE));
      } else {
        $this->response->status(404)->body(json_encode(['error' => 'Not found'], JSON_UNESCAPED_UNICODE));
      }
    } catch (\Throwable $th) {
      Log::instance()->add(Log::ERROR, $th->getMessage());
      $this->response->status(500)->body(json_encode(['error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
    }
  }
  // API End
}

