<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Adminvery extends Controller_Template_Adminbase {

	public $_auth_required = TRUE;
	public $_transactional = true;
	public $_model;
	public $_very_model;
	public $_very_setting_model;
	public $_aws_model;
	public $_view_path = 'admin/very/';
	public $_action_path = '/adminvery/';
	const ITEM_DIV = 10;

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		if ($this->_bot_id) {
			$flg_very = $this->_model->get_bot_setting($this->_bot_id, 'flg_very');
			if (!$flg_very) {
				$this->redirect('/admin/top');
			}
		}

		$this->_model = new Model_Adminmodel();
		$this->_very_model = new Model_Adminverymodel();
		$this->_very_setting_model = new Model_Verysettingmodel();
		$this->_aws_model = new Model_Aws();
		$this->_model->init($this->_bot_id);
		ini_set('max_execution_time', 300); // 300 seconds = 5 minutes
	}

	public function action_verylist()
	{
		$bot_id = $this->_bot_id;
		$user_role =  $this->_user->role_cd;
		$user_bot_id = $this->_user->bot_id;
		$template_bot = $this->_model->get_bot_setting($this->_bot_id, 'template_bot');
		// 設定一覧を取得
		$very_list = $this->_very_model->get_very_list($bot_id);

		// ユーザー導線を取得
		$scenes = ORM::factory('botscene')->where('bot_id', '=', $bot_id)->order_by('sort_no')->find_all();
		$scene_list = [];
		foreach ($scenes as $scene) {
			$scene_list[] = [
				'scene_name' => $scene->scene_name,
				'label' => $scene->label
			];
		}
		// VERYのデータがない導線のみ抽出する
		$existing_scene_cds = array_column($very_list, 'scene_cd');
		$scene_list_without_very_setting = array_filter($scene_list, function ($scene) use ($existing_scene_cds) {
			return !in_array($scene['scene_name'], $existing_scene_cds);
		});
		$scene_list_without_very_setting = array_values($scene_list_without_very_setting);

		// テンプレート用のボットリストを作成する
		$very_bot_list = $this->_very_model->get_very_setting_bot_list($this->_lang_cd);
		$parent_bot = $this->_very_model->get_grp_bot_id($bot_id);
		if ($parent_bot == 0) $parent_bot = $bot_id;
		$template_bot_list = [];

		foreach ($very_bot_list as $bot) {
			$should_include = false;
			switch ($user_role) {
				case '99':
					$should_include = true;
					break;
				default:
					// 親ボットの担当者場合、全施設コピー可能
					if ($user_bot_id == intval($bot_id / 1000) * 1000) {
						if ($bot["bot_id"] == '880' || ($bot["bot_id"] >= $parent_bot && $bot["bot_id"] <= $parent_bot + 999)) {
							$should_include = true;
						}
					}
					// 親ボットの担当者以外の場合
					else {
						if ($bot["bot_id"] == '880' || $bot["bot_id"] == $template_bot || $bot["bot_id"] == $parent_bot) {
							$should_include = true;
						}
					}
					break;
			}
			if ($should_include) {
				$template_bot_list[] = [
					'bot_id' => $bot["bot_id"],
					'bot_name' => $bot["bot_name"]
				];
			}
		}

		$view = View::factory($this->_view_path . 'verylist');
		$view->_bot_id = $this->_bot_id;
		$view->scene_list_without_very_setting = json_encode($scene_list_without_very_setting, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
		$view->very_list = json_encode($very_list, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
		$view->template_bot_list = json_encode($template_bot_list, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
		$this->template->content = $view;
	}

	public function action_verytop()
	{
		if (isset($_GET['lang'])) {
			$lang_edit = $_GET['lang']; 
		}
		else {
			$lang_edit = explode(',',$this->_bot->support_lang)[0];
			$this->redirect($this->_action_path . 'verytop?lang=' . $lang_edit);
		}

        $scene_cd= $this->_model->get_scene_cd_with_default($this->_bot_id, $_GET['scene_cd']);
		if ($scene_cd !== $_GET['scene_cd']) {
			$params = $_GET;
			$params['scene_cd'] = $scene_cd;
			$query = http_build_query($params);
			$this->redirect($this->_action_path . 'verytop?' . $query);
		}
		$post = NULL;
		$apply_bot = $this->_bot_id;
		$apply_scene = $scene_cd;
		if ($this->request->post()) {
			$post = $this->request->post();
			if($this->_page_action == 'apply_template'){
				Session::instance()->set('apply_template', $post['apply_template']);
				Session::instance()->set('apply_template_scene', $post['apply_template_scene']);
				$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>$this->_page_action, 'lang_cd'=>$lang_edit, 'scene_cd'=>$scene_cd]);
				$this->redirect($this->_action_path . 'verytop?lang=' . $lang_edit . '&scene_cd=' . $scene_cd);
			} else {
				Session::instance()->delete('apply_template');
				Session::instance()->delete('apply_template_scene');
				$settings = json_decode($post['settings'], true);
				$c = DB::delete('t_very_settings')->where('bot_id', '=', $this->_bot_id)->where('scene_cd', '=', $scene_cd)->where('page', '=', 'top')->where('lang_cd', '=', $lang_edit)->execute();
				$action_div = '';
				if ($c == 0) {
					$action_div = 'add';
				}
				else {
					$action_div = 'modify';
				}
				DB::delete('t_very')->where('bot_id', '=', $this->_bot_id)->where('page', '=', 'top')->where('scene_cd', '=', $scene_cd)->execute();
				$page_url_base = $this->_model->get_env('sitepage_url') . '?f=';
				foreach($settings as $k=>$v) {
					// t_veryに保存するkey
					$skipKeys = ["style", "display_lang", "period", "scene_cd", "background-image", "call", 'television'];
					if (in_array($k, $skipKeys)) {
						continue;
					}
					
					// #56274 footerカスタマイズされない場合、データ保存しない
					if ($k == "footer" && empty($v)) {
						continue;
					}
					if ($k == "categorys") {
						foreach($v as $category_index=>$category_value) {
							if ($category_value['func']['kind'] == 'page') {
								if (filter_var($v[$category_index]['func']['params']['url'], FILTER_VALIDATE_URL) !== false) continue;
								$sitepage = ORM::factory('sitepage')->where('bot_id', '=', $this->_bot_id)->where('page_cd', '=', $v[$category_index]['func']['params']['url'])->find();
								$page_scene_cd = $scene_cd;
								if (!empty($sitepage->scene_cd)) {
									$page_scene_cd = $sitepage->scene_cd;
								}
								$page_url = $page_url_base . $page_scene_cd . '&id=' . $v[$category_index]['func']['params']['url'];
								$v[$category_index]['func']['params']['url'] = $page_url;
							}
						}
					}
					if ($k == "functions") {
						foreach($v as $function_index=>$function_value) {
							if ($function_value['func']['kind'] == 'page') {
								if (filter_var($v[$function_index]['func']['params']['url'], FILTER_VALIDATE_URL) !== false) continue;
								$sitepage = ORM::factory('sitepage')->where('bot_id', '=', $this->_bot_id)->where('page_cd', '=', $v[$function_index]['func']['params']['url'])->find();
								$page_scene_cd = $scene_cd;
								if (!empty($sitepage->scene_cd)) {
									$page_scene_cd = $sitepage->scene_cd;
								}
								$page_url = $page_url_base . $page_scene_cd . '&id=' . $v[$function_index]['func']['params']['url'];
								$v[$function_index]['func']['params']['url'] = $page_url;
							}
						}
					}
					$orm = ORM::factory('verysettings');
					$orm->bot_id = $this->_bot_id;
					$orm->scene_cd = $scene_cd;
					$orm->lang_cd = $lang_edit;
					$orm->page = 'top';
					$orm->setting = $k;
					$orm->value = json_encode($v, JSON_UNESCAPED_UNICODE);
					$orm->upd_user = $this->_user->user_id;
					$orm->upd_time = date('Y-m-d H:i:s');
					$orm->save();
				}
				$saveToOrmVery = ['style', 'display_lang', 'television'];
				foreach ($saveToOrmVery as $setting) {
					if (array_key_exists($setting, $settings)) {
						$orm = ORM::factory("very");
						$orm->bot_id = $this->_bot_id;
						$orm->page = 'top';
						$orm->scene_cd = $scene_cd;
						$orm->setting = $setting;
						$orm->value = json_encode($settings[$setting], JSON_UNESCAPED_UNICODE);
						$orm->upd_user = $this->_user->user_id;
						$orm->upd_time = date("Y-m-d H:i:s");
						$orm->save();
					}
				}
				if (array_key_exists("period",$settings)) {
					$periodData = json_decode($settings['period'], true);
					$start_date = "";
					$end_date = "";
					if (isset($periodData['startDate']) && trim($periodData['startDate']) != '') {
						$start_date = $periodData['startDate'] . ' ' . $periodData['startTime'];
					}
					if (isset($periodData['endDate']) && trim($periodData['endDate']) != '') {
						$end_date = $periodData['endDate'] . ' ' . $periodData['endTime'];
					}
					$orm = ORM::factory('very');
					$orm->bot_id = $this->_bot_id;
					$orm->page = 'top';
					$orm->scene_cd = $scene_cd;
					$orm->setting = 'period';
					$orm->value = json_encode(['start_date' => $start_date,'end_date'=> $end_date], JSON_UNESCAPED_UNICODE);
					$orm->upd_user = $this->_user->user_id;
					$orm->upd_time = date('Y-m-d H:i:s');
					$orm->save();
				}
				if (array_key_exists("background-image", $settings) && $settings["background-image"] != NULL) {
					$orm = ORM::factory('very');
					$orm->bot_id = $this->_bot_id;
					$orm->page = 'top';
					$orm->scene_cd = $scene_cd;
					$orm->setting = 'background-image';
					$orm->value = $settings['background-image'];
					$orm->upd_user = $this->_user->user_id;
					$orm->upd_time = date('Y-m-d H:i:s');
					$orm->save();
				}
				if (isset($post['translate']) && $post['translate'] == 'translate_auto') {
					foreach ($post['translate_lang'] as $translate_lang) {
						$target_lang = $translate_lang;
						$page_setting_orms = ORM::factory('verysettings')
												->where('bot_id', '=', $this->_bot_id)
												->where('scene_cd', '=', $scene_cd)
												->where('page', '=', 'top')
												->where('lang_cd', '=', $lang_edit)
												->find_all();
						if (count($page_setting_orms) > 0) {
							foreach ($page_setting_orms as $orm) {
								if (in_array($orm->setting, ['widget', 'categorys', 'functions', 'title', 'footer', 'main_pic', 'logo_pic', 'sns', 'categorys_type'])) {
									$originValue = json_decode($orm->value, JSON_UNESCAPED_UNICODE);
									$newValue = $originValue;
									if ($orm->setting == 'title') {
										$from_title = $originValue;
										$translateResult = $this->_model->translate($from_title, $target_lang);
										if ($translateResult != False) {
											$newValue = $translateResult;
										}
									} elseif (in_array($orm->setting, ['categorys', 'functions', 'footer'])) {
										foreach ($originValue as $index => $value) {
											if (array_key_exists('title', $value)) {
												$from_title = $value['title'];
												$translateResult = $this->_model->translate($from_title, $target_lang);
												if ($translateResult != False) {
													$newValue[$index]['title'] = $translateResult;
												}
											}
										}
									}
									$translationExistsInDB = ORM::factory('verysettings')
																->where('bot_id', '=', $this->_bot_id)
																->where('scene_cd', '=', $scene_cd)
																->where('page', '=', 'top')
																->where('lang_cd', '=', $target_lang)
																->where('setting', '=', $orm->setting)
																->find_all();
									if(count($translationExistsInDB) === 0) {
										// データがない場合insert
										$data['bot_id'] = $this->_bot_id;
										$data['scene_cd'] = $scene_cd;
										$data['page'] = 'top';
										$data['setting'] = $orm->setting;
										$data['lang_cd'] = $target_lang;
										$data['value'] = json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
										$data['upd_user'] = $this->_user_id;
										$data['upd_time'] = date('Y-m-d H:i:s',time());
										DB::insert('t_very_settings', array_keys($data))
											->values(array_values($data))
											->execute();
									} else {
										// データがある場合update
										DB::update('t_very_settings')->set(array(
											'value'=> json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
											'upd_user'=> $this->_user_id,
											'upd_time'=>date('Y-m-d H:i:s',time())
										))
										->where('bot_id', '=', $this->_bot_id)
										->where('scene_cd', '=', $scene_cd)
										->where('page', '=', 'top')
										->where('setting', '=', $orm->setting)
										->where('lang_cd', '=', $target_lang)
										->execute();
								}
							}
						}
					}
					$this->_write_access_log([
					    "page_action" => $this->_page_action,
					    "action"      => $this->_page_action,
					    'lang_cd'     => $lang_edit,
					    'scene_cd'    => $scene_cd
					]);

					$translated_settings = DB::select('*')
											->from('t_very_settings')
											->where('bot_id', '=', $this->_bot_id)
											->and_where('scene_cd', '=', $scene_cd)
											->and_where('page', '=', 'top')
											->and_where('lang_cd', '=', $target_lang)
											->execute()->as_array();

					$max_version = $this->_very_model->get_latest_version($this->_bot_id, $scene_cd, 'top', $target_lang);
					$new_version = $max_version ? $max_version + 1 : 1;

					$history_value = json_encode($translated_settings, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
					
					DB::insert('t_very_settings_history', array(
						'bot_id',
						'scene_cd',
						'page',
						'lang_cd',
						'version',
						'value',
						'upd_user',
						'upd_time'
					))
					->values(array(
						$this->_bot_id,
						$scene_cd,
						'top',
						$target_lang,
						$new_version,
						$history_value,
						$this->_user->user_id,
						date('Y-m-d H:i:s')
					))
					->execute();
				}
			}

			$original_settings = DB::select('*')
										->from('t_very_settings')
										->where('bot_id', '=', $this->_bot_id)
										->and_where('scene_cd', '=', $scene_cd)
										->and_where('page', '=', 'top')
										->and_where('lang_cd', '=', $lang_edit)
										->execute()->as_array();

				$max_version_lang_specific = $this->_very_model->get_latest_version($this->_bot_id, $scene_cd, 'top', $lang_edit);
				$new_version_lang_specific = $max_version_lang_specific ? $max_version_lang_specific + 1 : 1;

				$history_value = json_encode($original_settings, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

				DB::insert('t_very_settings_history', array(
					'bot_id',
					'scene_cd',
					'page',
					'lang_cd',
					'version',
					'value',
					'upd_user',
					'upd_time'
				))
				->values(array(
					$this->_bot_id,
					$scene_cd,
					'top',
					$lang_edit,
					$new_version_lang_specific,
					$history_value,
					$this->_user->user_id,
					date('Y-m-d H:i:s')
				))
				->execute();
					
				// after save t_very_setting, check if this setting exists in table t_very_token, if not exists, create a new one.
				// data (scene_cd = null) needs to be created for the 期間 feature 
				$this->_very_model->create_or_get_very_obfuscation_id($this->_bot_id, null, null);
				$this->_very_model->create_or_get_very_obfuscation_id($this->_bot_id, $scene_cd, null);

				$travel_check_flg = intval($post['travel_check_flg']);
				$this->_very_model->write_or_delete_verystaycontrol($this->_bot_id, $travel_check_flg, $post['travel_check_input']);

				$this->_very_model->create_or_update_very_call_setting($this->_bot_id, $post, $scene_cd, $this->_user_id);

				Session::instance()->set('very_top_content_select', $post['top_content_select']);
				$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>$action_div, 'lang_cd'=>$lang_edit, 'scene_cd'=>$scene_cd]);
				$this->redirect($this->_action_path . 'verytop?lang=' . $lang_edit . '&scene_cd=' . $scene_cd);
			}
		}
		else {
			$apply_template = Session::instance()->get('apply_template', NULL);
			if ($apply_template) {
				$apply_bot = $apply_template;
			}
			Session::instance()->delete('apply_template');
			$apply_template_scene = Session::instance()->get('apply_template_scene', NULL);
			if ($apply_template_scene) {
				$apply_scene = $apply_template_scene;
			}
			Session::instance()->delete('apply_template_scene');
			$periodDataTemplate = [
				'startDate' => '',
				'endDate' => '',
				'startTime' => '',
				'endTime' => ''
			];
			$settings = [
				'scene_cd' => $scene_cd, 
				'period' => $periodDataTemplate, 
				'title'=>[], 
				'main_pic'=>[], 
				'logo_pic'=>[], 
				'style'=>[
					"theme_color"=>"#b12b7f",
					"title_color"=>"#ffffff"
				],
				'functions'=>[], 
				'categorys'=>[]
			];
			$very_top_orms = $this->_very_model->get_very_top($apply_bot, $apply_scene);

			$latest_version = $this->_very_model->get_latest_version($this->_bot_id, $scene_cd, 'top', $lang_edit);
			$version = isset($_GET['version']) ? $_GET['version'] : 'current';
					
			if ($version !== 'current') {
			    if (!is_numeric($version) || $version > $latest_version || $version == $latest_version) {
			        $query = http_build_query(['scene_cd' => $scene_cd, 'lang' => $lang_edit]);
			        $this->redirect($this->_action_path . 'verytop?' . $query);
			    }
			}
			
			if ($version !== 'current') {
			    $very_top_settings = $this->_very_model->get_very_settings_by_version($apply_bot, $apply_scene, 'top', $lang_edit, $version)["value"];
			    $requested_version_record = $this->_very_model->get_very_settings_by_version($this->_bot_id, $scene_cd, 'top', $lang_edit, $version);
			} else {
			    $very_top_settings = $this->_very_model->get_very_settings($apply_bot, $apply_scene, 'top', $lang_edit);
			    $requested_version_record = $this->_very_model->get_very_settings_by_version($this->_bot_id, $scene_cd, 'top', $lang_edit, $latest_version);
			}
			
			$history_records = $this->_very_model->get_all_very_settings_history_records($this->_bot_id, $scene_cd, "top", $lang_edit);
			$history_mode = (isset($_GET['mode']) && $_GET['mode'] === 'read') ? 'read' : 'write';

			if (count($very_top_orms) > 0) {
				foreach ($very_top_orms as $very_top_orm) {
					if ($very_top_orm['setting'] == 'period') {
						$periodData = $periodDataTemplate;
						$period = json_decode($very_top_orm['value'], true);
						if (isset($period['start_date'])) {
							$start = explode(' ', $period['start_date']);
							$periodData['startDate'] = $start[0];
							if (count($start)	> 1) {
								$periodData['startTime'] = date("H:i", strtotime($start[1]));
							}
						}
						if (isset($period['end_date'])) {
							$end = explode(' ', $period['end_date']);
							$periodData['endDate'] = $end[0];
							if (count($end)	> 1) {
								$periodData['endTime'] = date("H:i", strtotime($end[1]));
							}
						}
						$settings['period'] = $periodData;
					} else if ($very_top_orm['setting'] == 'background-image') {
						$settings[$very_top_orm['setting']] = $very_top_orm['value'];
					} else {
						$settings[$very_top_orm['setting']] = json_decode($very_top_orm['value'], true);
					}
				}
			}
			if (count($very_top_settings) > 0) {
				foreach ($very_top_settings as $very_top_setting) {
					if ($apply_bot == $this->_bot_id || $very_top_setting['setting'] != "footer") {
						$settings[$very_top_setting['setting']] = json_decode($very_top_setting['value'], true);
					}
				}
				$settings['title'] = str_replace("'", "&#39;", $settings['title']);
			}
			else {
				// init data
				//１、ホテル名は多言語メッセージ
				$title =  $this->_model->get_bot_txt_message_all_lang($this->_bot_id, 'bot_name')[$lang_edit];
				$settings['title'] = str_replace("'", "&#39;", $title);
				//２、色合いはユーザー導線から
				$scene_path = $this->_model->get_scene_path($scene_cd, 'webchat');
				$filename = $scene_path . "config.json";
				if(file_exists($filename)) {
					$handle = fopen($filename, "r");
					$content = fread($handle, filesize($filename));
					fclose($handle);
					$data = json_decode($content, true);
					if (array_key_exists('theme-bkcolor', $data)) {
						$settings['style']["theme_color"] = $data['theme-bkcolor'];
					}
					if (array_key_exists('theme-color', $data)) {
						$settings['style']["title_color"] = $data['theme-color'];
					}
				}
				//３、メイン機能はチャット、faq
				// if ($this->_model->get_bot_setting($this->_bot_id, 'flg_ai_bot') == 1) {
				// 	$settings['functions'] = json_decode('[{"func":{"type":"basic","kind":"chatbot"}}]',true);
				// }
				// if ($this->_model->get_bot_setting($this->_bot_id, 'flg_faq_site') == 1) {
				// 	$settings['functions'] = json_decode('[{"func":{"type":"basic","kind":"faq"}}]',true);
				// }
				// if ($this->_model->get_bot_setting($this->_bot_id, 'flg_ai_bot') == 1 && $this->_model->get_bot_setting($this->_bot_id, 'flg_faq_site') == 1) {
				// 	$settings['functions'] = json_decode('[{"func":{"type":"basic","kind":"chatbot"}},{"func":{"type":"basic","kind":"faq"}}]',true);
				// }
				//４、TOP内容は　施設案内、周辺案内、イベント、レストラン
				// $settings['categorys'] = json_decode('[{"func":{"kind":"content","params":{"item_div":"1","class_cd":"01","display":"category"}},"title":"施設案内","pic":""},{"func":{"kind":"content","params":{"item_div":"1","class_cd":"02","display":"category"}},"title":"周辺案内","pic":""},{"func":{"kind":"content","params":{"item_div":"2","class_cd":"05","display":"detail"}},"title":"イベント","pic":""},{"func":{"kind":"content","params":{"item_div":"1","class_cd":"0101","display":"detail"}},"title":"レストラン","pic":""}]', true);
			}
		}


		$functions = $this->_model->get_code('VERY_FUNCTION',$this->_lang_cd);
		$functions_mapping = $this->_model->get_code('VERY_FUNCTION_MAP');

		$applicable_functions = [];
		foreach($functions as $k=>$v) {
			if (!array_key_exists($k,$functions_mapping) || $this->_model->get_bot_setting($this->_bot_id, $functions_mapping[$k]) != '0'){
				$applicable_functions[$k] = $v;
			}
		}

		$footers = $this->_model->get_code('VERY_FOOTER',$this->_lang_cd);
		$footers_mapping = $this->_model->get_code('VERY_FOOTER_MAP');

		$applicable_footers = [];
		foreach($footers as $k=>$v) {
			if (!array_key_exists($k,$footers_mapping) || $this->_model->get_bot_setting($this->_bot_id, $footers_mapping[$k]) != '0'){
				$applicable_footers[$k] = $v;
			}
		}

		$func_def = [
			"content" => __('admin.verytop.content'),
			"url" => "URL",
			"inquiry" => __('admin.verytop.inquiry'),
			"congestion" => __('admin.verytop.congestion'),
			"customize_content" => __('admin.verytop.customize_content'),
			"reception" => __('admin.verytop.reception'),
			"userguide" => __('admin.verytop.userguide'),
			"market" => "MARKET",
			"page" => __('admin.verytop.page'),
			"survey" => __('admin.verytop.survey'),
			"laundry" => __('admin.verytop.laundry'),
		];

		$show_page = $this->_model->get_bot_setting($this->_bot_id, 'flg_page') == 1;
		
		if ($this->_model->get_bot_setting($this->_bot_id, 'flg_next_inquiry') == 0) {
			unset($func_def['inquiry']);
		}
		if (!$show_page) {
			unset($func_def['page']);
		}

		$show_survey = $this->_model->get_bot_setting($this->_bot_id, 'flg_next_survey') == 1;
		if (!$show_survey) {
			unset($func_def['survey']);
		}

		$show_laundry = $this->_model->get_bot_setting($this->_bot_id, 'flg_laundry') == 1;
		if (!$show_laundry) {
			unset($func_def['laundry']);
		}

		$bot_supported_langs = explode(',', $this->_bot->support_lang);
		$very_supported_langs = [];
		foreach($bot_supported_langs as $v) {
			if (!in_array($v, $this->_model->get_setting('very_support_lang'))) continue;
			$very_supported_langs[$v] = $this->_codes['02'][$v];
		}

		$display_langs = [];
		if (isset($settings["display_lang"]) && is_array($settings["display_lang"])) {
			$lang_arr = $settings["display_lang"];
			foreach ($lang_arr as $v) {
				$lang_arr[$v] = $this->_codes['02'][$v];
			}
		} else {
			$lang_arr = $very_supported_langs;
		}
		foreach ($lang_arr as $k=>$v) {
			if (!in_array($k, $this->_model->get_setting('very_support_lang'))) continue;
			$display_langs[$k] = $v;
		}

		$area_cd1 = $this->_model->get_bot_setting($this->_bot_id, 'div_item_area_1');
		$area_cd2 = $this->_model->get_bot_setting($this->_bot_id, 'div_item_area_2');
		$area_selection_values = array(
			'area_1' => $area_cd1,
			'area_2' => $area_cd2,
		);
		$area_dict1 = $this->_model->get_code_div($area_cd1, $this->_lang_cd);
		$area_dict2 = $this->_model->get_code_div($area_cd2, $this->_lang_cd);

		$area_selection_dict = array(
			'area_1' => $area_dict1,
			'area_2' => $area_dict2
		);

		foreach ($area_selection_dict as $key=>$area_dict_array){
			if ( $area_dict_array ) {
				$area_dictionary = ['parents'=>[],'children'=>[]];
				foreach($area_dict_array as $area) {
					if( $area['parent_cd']) {
						if (isset($area_dictionary['children'][$area['parent_cd']])){
							$area_dictionary['children'][$area['parent_cd']][$area['class_cd']] = $area['name'];
						} else {
							$area_dictionary['children'][$area['parent_cd']] = [$area['class_cd'] => $area['name']];
						}
					} else {
						$area_dictionary['parents'][$area['class_cd']] = $area['name'];
					}
				}
				$area_selection_dict[$key] = $area_dictionary;
			}
		}

		// お知らせの初期値
		if(!isset($settings['notice'])) $settings['notice'] = [];
		if(!isset($settings['half_modal'])) $settings['half_modal'] = [];
		
		$item_divs = [];
		// 施設コンテンツ
		$class_div_1 = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_1');
		$item_divs[$class_div_1] = __('admin.items.item_div1.title');
		// 広域コンテンツ
		$class_div_2 = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_2');
		if ($class_div_2 !== '') {
			$item_divs[$class_div_2] = __('admin.items.item_div2.title');
		}

		// ユーザー導線
		$scenes = ORM::factory('botscene')->where('bot_id', '=', $this->_bot_id)->order_by('sort_no')->find_all();
		$scene_list = [];
		foreach($scenes as $scene) {
			$scene_list[$scene->scene_name] = $scene->label;
		}

		$bot_id = $this->_bot_id;
		$base_url = $this->_model->get_env('very_url');

		$obfuscation_id = $this->_very_model->get_very_obfuscation_id($bot_id, null, null);
		$obfuscation_id_with_scene_cd = $this->_very_model->get_very_obfuscation_id($bot_id, $scene_cd, null);

		$obfuscation_url = $obfuscation_id ? "{$base_url}?id={$obfuscation_id}" : "";
		$obfuscation_url_with_scene_cd = $obfuscation_id_with_scene_cd ? "{$base_url}?id={$obfuscation_id_with_scene_cd}" : "";

		// プレビューURL
		$verify_url = $this->_generate_very_preview_url("", $lang_edit, $obfuscation_id_with_scene_cd,$scene_cd);
		if (isset($settings["display_lang"]) && is_array($settings["display_lang"])) {
			$display_lang_arr = $settings["display_lang"];
			if (!in_array($lang_edit, $display_lang_arr)) {
				$post = [
					'data' => json_encode([
						"lang_cd" => $lang_edit,
						"bot_id" => $bot_id,
						"scene_cd" => $scene_cd
					])
				];

				// Append the evaluation token to the verify URL
				$verify_url .= '&eval=' . $this->_model->get_new_token($post);
			}
		}

		$current = Session::instance()->get('very_top_content_select', NULL);
		if ($current == NULL) $current = -1;
		$view = View::factory ($this->_view_path . 'verytop');
		$view->scene_list = $scene_list;
		$view->area_selection_values = $area_selection_values;
		$view->area_selection_dict = $area_selection_dict;
		$view->travel_check_flg = $this->_very_model->read_verystaycontrol($this->_bot_id);
		$view->travel_check_input = $this->_very_model->read_verystaycontrol_params($this->_bot_id);
		if ($this->_very_model->read_very_call_setting($this->_bot_id, $scene_cd) !== false) {
			$view->web_call_setting = $this->_very_model->read_very_call_setting($this->_bot_id, $scene_cd);
		} else {
			$view->web_call_setting = ['call'=> '0', "rooms" => ""];
		}
		
		$view->display_langs = $display_langs;
		$view->very_supported_langs = $very_supported_langs;
		$view->lang_edit = $lang_edit;
		$view->settings = $settings;
		$view->current = $current;
		$view->very_bot_list = $this->_very_model->get_very_setting_bot_list($lang_edit);
		$view->template_bot = $this->_model->get_bot_setting($this->_bot_id, 'template_bot');
		$view->user_role = $this->_user->role_cd;
		$view->user_bot_id = $this->_user->bot_id;
		$view->applicable_functions = $applicable_functions;
		$view->applicable_footers = $applicable_footers;
		$view->func_def = $func_def;
		$view->show_page = $show_page;
		$view->show_survey = $show_survey;
		$view->bot_lang = $this->_model->get_bot_support_lang(ORM::factory('bot', $this->_bot_id));
		$view->lang_cd = $this->_model->get_admin_lang();
		$view->verify_url = $verify_url;
		$view->obfuscation_url = $obfuscation_url;
		$view->obfuscation_url_with_scene_cd = $obfuscation_url_with_scene_cd;
		$view->flg_very_access_by_bot_id = $this->_very_model->get_bot_setting($this->_bot_id, "flg_very_access_by_bot_id");
		//デフォルトのvery pass
		$view->open_url = $base_url . '?bot_id=' . $bot_id;
		$view->open_url_with_scene_cd = $base_url . '?bot_id=' . $bot_id . '&f=' . $scene_cd;
		$view->all_class_code =  array(
			1 => $this->_model->get_all_class_code($this->_model->get_bot_setting($this->_bot_id, 'div_item_class_1'), $this->_lang_cd, $this->_bot_id),
			2 => $this->_model->get_all_class_code($this->_model->get_bot_setting($this->_bot_id, 'div_item_class_2'), $this->_lang_cd, $this->_bot_id)
		);

		$view->history_records = $history_records;
		$view->requested_version_record = $requested_version_record;
		$view->history_mode = $history_mode;
		$view->version = $version;

		$this->template->content = $view;
	}

	public function action_very_setting() {
		$post = NULL;
		$bot_id = $this->_bot_id;
		$scene_cd = $_GET['f'] ?? '';
		$settings = [];
		if ($this->request->post()) {
			$post = $this->request->post();
			$settings = json_decode($post['setting_data'], true);
			$scene_cd = $settings['scene_cd'];

			$this->_very_setting_model->delete_all_very($bot_id, $scene_cd);
			$this->_very_setting_model->delete_very_stay_control($bot_id);

			foreach ($settings as $key => $value) {
				if ($key == 'scene_cd') {
					continue;
				}
				if (is_array($value)) {
					$value = json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
				}
				if ($key == 'stay_control') {
					$this->_very_setting_model->insert_very_stay_control(
						$bot_id, $value
					);
					continue;
				}
				$this->_very_setting_model->insert_very(
					$bot_id,
					$scene_cd,
					'top',
					$key,
					$value,
					$this->_user_id
				);
			}

			// after save t_very, check if this scene_cd exists in table t_very_token, if not exists, create a new one.
			$this->_very_model->create_or_get_very_obfuscation_id($this->_bot_id, '', null);
			$this->_very_model->create_or_get_very_obfuscation_id($this->_bot_id, $scene_cd, null);

			$this->redirect($this->_action_path . 'very_setting?f=' . $scene_cd);
		}
		else {
			$very_top_orms = $this->_very_model->get_very_top($bot_id, $scene_cd);
			if (!empty($very_top_orms)) {
				foreach ($very_top_orms as $row) {
					$value = json_decode($row['value'], true);
					$settings[$row['setting']] = json_last_error() === JSON_ERROR_NONE ? $value : $row['value'];
				}
				
				$base_url = $this->_model->get_env('very_url');
				$settings['public_url'] = [
					"public_url" => $base_url . '?bot_id=' . $bot_id,
					"public_url_with_scene_cd" => $base_url . '?bot_id=' . $bot_id . '&f=' . $scene_cd,
					"obfuscation_url" => $base_url . '?id=' . ($this->_very_model->get_very_obfuscation_id($bot_id, null, null) ?? ""),
					"obfuscation_url_with_scene_cd" => $base_url . '?id=' . ($this->_very_model->get_very_obfuscation_id($bot_id, $scene_cd, null) ?? ""),
				];
			} else {
				// default: 色合いはユーザー導線から
				$this->_load_default_color_settings($scene_cd, $settings);
			}

			// 旅ナカチェックの設定
			$stay_control_orms = $this->_very_model->read_verystaycontrol_params($this->_bot_id);
			if ($stay_control_orms) {
				$settings['stay_control'] = json_decode($stay_control_orms);
			}
		}
		$settings['scene_cd'] = $scene_cd;
		$settings['enable_access_by_bot_id'] =  $this->_very_model->get_bot_setting($this->_bot_id, "flg_very_access_by_bot_id");

		// ユーザー導線
		$scene_list = $scene_cd
			? $this->_very_setting_model->get_all_scene_lists($bot_id)
			: $this->_very_setting_model->get_scene_lists_without_data($bot_id);


		// 表示言語
		$default_display_langs = $this->_get_very_support_languages();

		$view = View::factory ($this->_view_path . 'verysetting');
		$view->scene_list = $scene_list;
		$view->settings = $settings;
		$view->user_role = $this->_user->role_cd;
		$view->default_display_langs = $default_display_langs;
		$this->template->content = $view;
	}

	private function _get_very_support_languages() {
		$bot_supported_langs = explode(',', $this->_bot->support_lang);
		$very_supported_langs = [];
		foreach($bot_supported_langs as $v) {
			if (!in_array($v, $this->_model->get_setting('very_support_lang'))) continue;
			$very_supported_langs[$v] = $this->_codes['02'][$v];
		}
		return $very_supported_langs;
	}

	private function _load_default_color_settings($scene_cd, &$settings) {
		$scene_path = $this->_model->get_scene_path($scene_cd, 'webchat');
		$filename = $scene_path . "config.json";

		if (!is_readable($filename)) return;

		$data = json_decode(file_get_contents($filename), true);
		if (empty($data)) return;

		if (!empty($data['theme-bkcolor'])) {
			$settings['style']['theme_color'] = $data['theme-bkcolor'];
		}
		if (!empty($data['theme-color'])) {
			$settings['style']['title_color'] = $data['theme-color'];
		}
	}

	private function _mapping_very_lang($lang_cd) {
		$map = [
			'kr' => 'ko',
			'cn' => 'zh-cn',
			'tw' => 'zh-tw',
			'cb' => 'ceb',
			'hw' => 'haw',
			'hn' => 'hmn',
			'bh' => 'bho',
			'do' => 'doi',
			'il' => 'ilo',
			'ok' => 'gom',
			'ki' => 'kri',
			'ck' => 'ckb',
			'ma' => 'mai',
			'ni' => 'mni',
			'lu' => 'lus',
			'ns' => 'nso',
			'wi' => 'ak',
		];
	
		return $map[$lang_cd] ?? $lang_cd;
	}

		public function action_very_top()
	{

		if (!isset($_GET['scene_cd'])) $this->redirect($this->_action_path . 'verylist');
		$scene_cd = $_GET['scene_cd'];

		if (isset($_GET['lang'])) {
			$lang_edit = $_GET['lang'];
		} else {
			$lang_edit = explode(',', $this->_bot->support_lang)[0];
			$this->redirect($this->_action_path . 'very_top?lang=' . $lang_edit . '&scene_cd=' . $scene_cd);
		}

		$post = NULL;
		$apply_bot = $this->_bot_id;
		$apply_scene = $scene_cd;
		if ($this->request->post()) {
			$post = $this->request->post();
			$settings = json_decode($post['settings'], true);
			$c = DB::delete('t_very_settings')->where('bot_id', '=', $this->_bot_id)->where('scene_cd', '=', $scene_cd)->where('page', '=', 'top')->where('lang_cd', '=', $lang_edit)->execute();
			$action_div = '';
			if ($c == 0) {
				$action_div = 'add';
			}
			else {
				$action_div = 'modify';
			}
			$page_url_base = $this->_model->get_env('sitepage_url') . '?f=';
			foreach($settings as $k=>$v) {
				// #56274 footerカスタマイズされない場合、データ保存しない
				if ($k == "footer" && empty($v)) {
					continue;
				}
				if ($k == "categorys") {
					foreach($v as $category_index=>$category_value) {
						if ($category_value['func']['kind'] == 'page') {
							if (filter_var($v[$category_index]['func']['params']['url'], FILTER_VALIDATE_URL) !== false) continue;
							$sitepage = ORM::factory('sitepage')->where('bot_id', '=', $this->_bot_id)->where('page_cd', '=', $v[$category_index]['func']['params']['url'])->find();
							$page_scene_cd = $scene_cd;
							if (!empty($sitepage->scene_cd)) {
								$page_scene_cd = $sitepage->scene_cd;
							}
							$page_url = $page_url_base . $page_scene_cd . '&id=' . $v[$category_index]['func']['params']['url'];
							$v[$category_index]['func']['params']['url'] = $page_url;
						}
					}
				}
				if ($k == "functions") {
					foreach($v as $function_index=>$function_value) {
						if ($function_value['func']['kind'] == 'page') {
							if (filter_var($v[$function_index]['func']['params']['url'], FILTER_VALIDATE_URL) !== false) continue;
							$sitepage = ORM::factory('sitepage')->where('bot_id', '=', $this->_bot_id)->where('page_cd', '=', $v[$function_index]['func']['params']['url'])->find();
							$page_scene_cd = $scene_cd;
							if (!empty($sitepage->scene_cd)) {
								$page_scene_cd = $sitepage->scene_cd;
							}
							$page_url = $page_url_base . $page_scene_cd . '&id=' . $v[$function_index]['func']['params']['url'];
							$v[$function_index]['func']['params']['url'] = $page_url;
						}
					}
				}
				$orm = ORM::factory('verysettings');
				$orm->bot_id = $this->_bot_id;
				$orm->scene_cd = $scene_cd;
				$orm->lang_cd = $lang_edit;
				$orm->page = 'top';
				$orm->setting = $k;
				$orm->value = json_encode($v, JSON_UNESCAPED_UNICODE);
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
			}
			if (isset($post['translate']) && $post['translate'] == 'translate_auto') {
				foreach ($post['translate_lang'] as $translate_lang) {
					$target_lang = $translate_lang;
					$page_setting_orms = ORM::factory('verysettings')
											->where('bot_id', '=', $this->_bot_id)
											->where('scene_cd', '=', $scene_cd)
											->where('page', '=', 'top')
											->where('lang_cd', '=', $lang_edit)
											->find_all();
					if (count($page_setting_orms) > 0) {
						foreach ($page_setting_orms as $orm) {
							if (in_array($orm->setting, ['widget', 'categorys', 'functions', 'title', 'footer', 'main_pic', 'logo_pic', 'sns', 'categorys_type'])) {
								$originValue = json_decode($orm->value, JSON_UNESCAPED_UNICODE);
								$newValue = $originValue;
								if ($orm->setting == 'title') {
									$from_title = $originValue;
									$translateResult = $this->_model->translate($from_title, $target_lang);
									if ($translateResult != False) {
										$newValue = $translateResult;
									}
								} elseif (in_array($orm->setting, ['categorys', 'functions', 'footer'])) {
									foreach ($originValue as $index => $value) {
										if (array_key_exists('title', $value)) {
											$from_title = $value['title'];
											$translateResult = $this->_model->translate($from_title, $target_lang);
											if ($translateResult != False) {
												$newValue[$index]['title'] = $translateResult;
											}
										}
									}
								}
								$translationExistsInDB = ORM::factory('verysettings')
															->where('bot_id', '=', $this->_bot_id)
															->where('scene_cd', '=', $scene_cd)
															->where('page', '=', 'top')
															->where('lang_cd', '=', $target_lang)
															->where('setting', '=', $orm->setting)
															->find_all();
								if(count($translationExistsInDB) === 0) {
									// データがない場合insert
									$data['bot_id'] = $this->_bot_id;
									$data['scene_cd'] = $scene_cd;
									$data['page'] = 'top';
									$data['setting'] = $orm->setting;
									$data['lang_cd'] = $target_lang;
									$data['value'] = json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
									$data['upd_user'] = $this->_user_id;
									$data['upd_time'] = date('Y-m-d H:i:s',time());
									DB::insert('t_very_settings', array_keys($data))
										->values(array_values($data))
										->execute();
								} else {
									// データがある場合update
									DB::update('t_very_settings')->set(array(
										'value'=> json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
										'upd_user'=> $this->_user_id,
										'upd_time'=>date('Y-m-d H:i:s',time())
									))
									->where('bot_id', '=', $this->_bot_id)
									->where('scene_cd', '=', $scene_cd)
									->where('page', '=', 'top')
									->where('setting', '=', $orm->setting)
									->where('lang_cd', '=', $target_lang)
									->execute();
							}
						}
					}
				}
				$this->_write_access_log([
					"page_action" => $this->_page_action,
					"action"      => $this->_page_action,
					'lang_cd'     => $lang_edit,
					'scene_cd'    => $scene_cd
				]);

				$translated_settings = DB::select('*')
										->from('t_very_settings')
										->where('bot_id', '=', $this->_bot_id)
										->and_where('scene_cd', '=', $scene_cd)
										->and_where('page', '=', 'top')
										->and_where('lang_cd', '=', $target_lang)
										->execute()->as_array();

				$max_version = $this->_very_model->get_latest_version($this->_bot_id, $scene_cd, 'top', $target_lang);
				$new_version = $max_version ? $max_version + 1 : 1;

				$history_value = json_encode($translated_settings, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
				
				DB::insert('t_very_settings_history', array(
					'bot_id',
					'scene_cd',
					'page',
					'lang_cd',
					'version',
					'value',
					'upd_user',
					'upd_time'
				))
				->values(array(
					$this->_bot_id,
					$scene_cd,
					'top',
					$target_lang,
					$new_version,
					$history_value,
					$this->_user->user_id,
					date('Y-m-d H:i:s')
				))
				->execute();
			}
		}

		$original_settings = DB::select('*')
									->from('t_very_settings')
									->where('bot_id', '=', $this->_bot_id)
									->and_where('scene_cd', '=', $scene_cd)
									->and_where('page', '=', 'top')
									->and_where('lang_cd', '=', $lang_edit)
									->execute()->as_array();

			$max_version_lang_specific = $this->_very_model->get_latest_version($this->_bot_id, $scene_cd, 'top', $lang_edit);
			$new_version_lang_specific = $max_version_lang_specific ? $max_version_lang_specific + 1 : 1;

			$history_value = json_encode($original_settings, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

			DB::insert('t_very_settings_history', array(
				'bot_id',
				'scene_cd',
				'page',
				'lang_cd',
				'version',
				'value',
				'upd_user',
				'upd_time'
			))
			->values(array(
				$this->_bot_id,
				$scene_cd,
				'top',
				$lang_edit,
				$new_version_lang_specific,
				$history_value,
				$this->_user->user_id,
				date('Y-m-d H:i:s')
			))
			->execute();
					
			$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>$action_div, 'lang_cd'=>$lang_edit, 'scene_cd'=>$scene_cd]);
			$this->redirect($this->_action_path . 'very_top?lang=' . $lang_edit . '&scene_cd=' . $scene_cd);
		}
		else {
			$settings = [
				'scene_cd' => $scene_cd, 
				'title'=>[], 
				'main_pic'=>[], 
				'logo_pic'=>[], 
				'functions'=>[], 
				'categorys'=>[]
			];
			$very_top_orms = $this->_very_model->get_very_top($apply_bot, $apply_scene);

			$latest_version = $this->_very_model->get_latest_version($this->_bot_id, $scene_cd, 'top', $lang_edit);
			$version = isset($_GET['version']) ? $_GET['version'] : 'current';
					
			if ($version !== 'current') {
			    if (!is_numeric($version) || $version > $latest_version || $version == $latest_version) {
			        $query = http_build_query(['scene_cd' => $scene_cd, 'lang' => $lang_edit]);
			        $this->redirect($this->_action_path . 'very_top?' . $query);
			    }
			}
			
			if ($version !== 'current') {
			    $very_top_settings = $this->_very_model->get_very_settings_by_version($apply_bot, $apply_scene, 'top', $lang_edit, $version)["value"];
			    $requested_version_record = $this->_very_model->get_very_settings_by_version($this->_bot_id, $scene_cd, 'top', $lang_edit, $version);
			} else {
			    $very_top_settings = $this->_very_model->get_very_settings($apply_bot, $apply_scene, 'top', $lang_edit);
			    $requested_version_record = $this->_very_model->get_very_settings_by_version($this->_bot_id, $scene_cd, 'top', $lang_edit, $latest_version);
			}
			
			$history_records = $this->_very_model->get_all_very_settings_history_records($this->_bot_id, $scene_cd, "top", $lang_edit);
			$history_mode = (isset($_GET['mode']) && $_GET['mode'] === 'read') ? 'read' : 'write';

			foreach ($very_top_orms as $very_top_orm) {
				if ($very_top_orm['setting'] === 'display_lang') {
					$settings['display_lang'] = json_decode($very_top_orm['value'], true);
					break; // display_lang は1つしかない前提なら break で早期終了
				}
			}
			if (count($very_top_settings) > 0) {
				foreach ($very_top_settings as $very_top_setting) {
					if ($apply_bot == $this->_bot_id || $very_top_setting['setting'] != "footer") {
						$settings[$very_top_setting['setting']] = json_decode($very_top_setting['value'], true);
					}
				}
				$settings['title'] = str_replace("'", "&#39;", $settings['title']);
			}
			else {
				// init data
				//１、ホテル名は多言語メッセージ
				$title =  $this->_model->get_bot_txt_message_all_lang($this->_bot_id, 'bot_name')[$lang_edit];
				$settings['title'] = str_replace("'", "&#39;", $title);
			}
		}


		$functions = $this->_model->get_code('VERY_FUNCTION',$this->_lang_cd);
		$functions_mapping = $this->_model->get_code('VERY_FUNCTION_MAP');

		$applicable_functions = [];
		foreach($functions as $k=>$v) {
			if (!array_key_exists($k,$functions_mapping) || $this->_model->get_bot_setting($this->_bot_id, $functions_mapping[$k]) != '0'){
				$applicable_functions[$k] = $v;
			}
		}

		$footers = $this->_model->get_code('VERY_FOOTER',$this->_lang_cd);
		$footers_mapping = $this->_model->get_code('VERY_FOOTER_MAP');

		$applicable_footers = [];
		foreach($footers as $k=>$v) {
			if (!array_key_exists($k,$footers_mapping) || $this->_model->get_bot_setting($this->_bot_id, $footers_mapping[$k]) != '0'){
				$applicable_footers[$k] = $v;
			}
		}

		$func_def = [
			"content" => __('admin.verytop.content'),
			"url" => "URL",
			"inquiry" => __('admin.verytop.inquiry'),
			"congestion" => __('admin.verytop.congestion'),
			"customize_content" => __('admin.verytop.customize_content'),
			"reception" => __('admin.verytop.reception'),
			"userguide" => __('admin.verytop.userguide'),
			"market" => "MARKET",
			"page" => __('admin.verytop.page'),
			"survey" => __('admin.verytop.survey'),
			"laundry" => __('admin.verytop.laundry'),
		];

		$show_page = $this->_model->get_bot_setting($this->_bot_id, 'flg_page') == 1;
		
		if ($this->_model->get_bot_setting($this->_bot_id, 'flg_next_inquiry') == 0) {
			unset($func_def['inquiry']);
		}
		if (!$show_page) {
			unset($func_def['page']);
		}

		$show_survey = $this->_model->get_bot_setting($this->_bot_id, 'flg_next_survey') == 1;
		if (!$show_survey) {
			unset($func_def['survey']);
		}

		$show_laundry = $this->_model->get_bot_setting($this->_bot_id, 'flg_laundry') == 1;
		if (!$show_laundry) {
			unset($func_def['laundry']);
		}

		// Very対応言語の取得（botがサポートし、veryもサポートしている言語）
		$very_supported_langs = $this->_get_very_support_languages();

		// 表示言語の決定		
		$display_langs = [];
		if (isset($settings["display_lang"]) && is_array($settings["display_lang"])) {
			$lang_arr = $settings["display_lang"];
			foreach ($lang_arr as $v) {
				$lang_arr[$v] = $this->_codes['02'][$v];
			}
		} else {
			$lang_arr = $very_supported_langs;
		}
		foreach ($lang_arr as $k=>$v) {
			if (!in_array($k, $this->_model->get_setting('very_support_lang'))) continue;
			$display_langs[$k] = $v;
		}

		$area_cd1 = $this->_model->get_bot_setting($this->_bot_id, 'div_item_area_1');
		$area_cd2 = $this->_model->get_bot_setting($this->_bot_id, 'div_item_area_2');
		$area_selection_values = array(
			'area_1' => $area_cd1,
			'area_2' => $area_cd2,
		);
		$area_dict1 = $this->_model->get_code_div($area_cd1, $this->_lang_cd);
		$area_dict2 = $this->_model->get_code_div($area_cd2, $this->_lang_cd);

		$area_selection_dict = array(
			'area_1' => $area_dict1,
			'area_2' => $area_dict2
		);

		foreach ($area_selection_dict as $key=>$area_dict_array){
			if ( $area_dict_array ) {
				$area_dictionary = ['parents'=>[],'children'=>[]];
				foreach($area_dict_array as $area) {
					if( $area['parent_cd']) {
						if (isset($area_dictionary['children'][$area['parent_cd']])){
							$area_dictionary['children'][$area['parent_cd']][$area['class_cd']] = $area['name'];
						} else {
							$area_dictionary['children'][$area['parent_cd']] = [$area['class_cd'] => $area['name']];
						}
					} else {
						$area_dictionary['parents'][$area['class_cd']] = $area['name'];
					}
				}
				$area_selection_dict[$key] = $area_dictionary;
			}
		}

		// お知らせの初期値
		if(!isset($settings['notice'])) $settings['notice'] = [];
		if(!isset($settings['half_modal'])) $settings['half_modal'] = [];

		$bot_id = $this->_bot_id;
		$obfuscation_id_with_scene_cd = $this->_very_model->get_very_obfuscation_id($bot_id, $scene_cd, null);
		
		// プレビューURL
		$verify_url = $this->_generate_very_preview_url("", $lang_edit, $obfuscation_id_with_scene_cd,$scene_cd);
		if (isset($settings["display_lang"]) && is_array($settings["display_lang"])) {
			$display_lang_arr = $settings["display_lang"];
			if (!in_array($lang_edit, $display_lang_arr)) {
				$post = [
					'data' => json_encode([
						"lang_cd" => $lang_edit,
						"bot_id" => $bot_id,
						"scene_cd" => $scene_cd
					])
				];

				// Append the evaluation token to the verify URL
				$verify_url .= '&eval=' . $this->_model->get_new_token($post);
			}
		}

		$current = Session::instance()->get('very_top_content_select', NULL);
		if ($current == NULL) $current = -1;

		$view = View::factory ($this->_view_path . 'very_top');
		$view->area_selection_values = $area_selection_values;
		$view->area_selection_dict = $area_selection_dict;
	
		$view->display_langs = $display_langs;
		$view->very_supported_langs = $very_supported_langs;
		$view->lang_edit = $lang_edit;
		$view->settings = $settings;
		$view->current = $current;
		$view->user_role = $this->_user->role_cd;
		$view->applicable_functions = $applicable_functions;
		$view->applicable_footers = $applicable_footers;
		$view->func_def = $func_def;
		$view->show_page = $show_page;
		$view->show_survey = $show_survey;
		$view->bot_lang = $this->_model->get_bot_support_lang(ORM::factory('bot', $this->_bot_id));
		$view->lang_cd = $this->_model->get_admin_lang();
		$view->verify_url = $verify_url;
		$view->all_class_code =  array(
			1 => $this->_model->get_all_class_code($this->_model->get_bot_setting($this->_bot_id, 'div_item_class_1'), $this->_lang_cd, $this->_bot_id),
			2 => $this->_model->get_all_class_code($this->_model->get_bot_setting($this->_bot_id, 'div_item_class_2'), $this->_lang_cd, $this->_bot_id)
		);

		$view->history_records = $history_records;
		$view->requested_version_record = $requested_version_record;
		$view->history_mode = $history_mode;
		$view->version = $version;

		$this->template->content = $view;
	}

	public function action_veryreport(){
		$user = $this->_very_model->get_veryuser($this->_bot_id);
		$member = $this->_very_model->get_verymember($this->_bot_id);
		$guest = $this->_very_model->get_veryguest($this->_bot_id);
		$top_clicks = $this->_very_model->get_top_clicks($this->_bot_id);
		$view = View::factory ($this->_view_path . 'veryreport');
		$view->user = $user;
		$view->member = $member;
		$view->guest = $guest;
		$view->top_clicks = $top_clicks;
		$view->_bot_id = $this->_bot_id;
		$this->template->content = $view;
	}

	public function action_reportuser(){
		$post =  $start_date = $end_date = $start_time = $end_time = NULL;
		$scene_cd = '';
		if ($this->request->post()) {
			$post = $this->request->post();
			$start_date = isset($post['start_date']) && !empty($post['start_date']) ? $post['start_date'] : date('Y-m', strtotime('-11 months'));
			$end_date = isset($post['end_date']) && !empty($post['end_date']) ? $post['end_date'] : date('Y-m');
			$start_time = DateTime::createFromFormat('Y-m', $start_date)->format('Y-m-01');
			$end_time = DateTime::createFromFormat('Y-m', $end_date)->format('Y-m-t 23:59:59');
			Log::instance()->add(Log::DEBUG, 'Filtering very user count report from '.$start_time.' to '.$end_time);
			$scene_cd = $post['scene_cd'];
		}
		// ユーザー数
		$language_user_counts = $this->_very_model->get_very_report_user($this->_bot_id, $scene_cd, $this->_lang_cd, "lang", $start_time, $end_time);
		$user_counts = $this->_very_model->get_very_report_user($this->_bot_id, $scene_cd, $this->_lang_cd, "month", $start_time, $end_time);

		// ユーザー数（テーブル）
		$user_counts_for_table_data_and_columns = $this->_very_model->get_very_report_users_for_table($this->_bot_id, $scene_cd, $this->_lang_cd, $start_time, $end_time);
		$user_counts_for_table = $user_counts_for_table_data_and_columns['data'];
		$columns_for_table = $user_counts_for_table_data_and_columns['columns'];

		$scenes_with_data = $this->_very_model->get_scene_cds_has_data($this->_bot_id);

		$view = View::factory($this->_view_path . 'reportuser');
		$view->post = $post;
		$view->scene_cd = $scene_cd;
		$view->_bot_id = $this->_bot_id;
		$view->scenes_with_data = $scenes_with_data;
		$view->language_user_counts_json_encoded = json_encode($language_user_counts);
		$view->user_counts_json_encoded = json_encode($user_counts);
		$view->user_counts_for_table_json_encoded = json_encode($user_counts_for_table);
		$view->columns_for_table_json_encoded = json_encode($columns_for_table);
		$this->template->content = $view;
	}

	public function action_reportclick(){
		$post =  $start_date = $end_date = $start_time = $end_time = NULL;
		$scene_cd = '';
		if ($this->request->post()) {
			$post = $this->request->post();
			$start_date = isset($post['start_date']) && !empty($post['start_date']) ? $post['start_date'] : date('Y-m', strtotime('-11 months'));
			$end_date = isset($post['end_date']) && !empty($post['end_date']) ? $post['end_date'] : date('Y-m');
			$start_time = DateTime::createFromFormat('Y-m', $start_date)->format('Y-m-01');
			$end_time = DateTime::createFromFormat('Y-m', $end_date)->format('Y-m-t 23:59:59');
			Log::instance()->add(Log::DEBUG, 'Filtering very user count report from '.$start_time.' to '.$end_time);
			$scene_cd = $post['scene_cd'];
		}
		// クリック数（折れ線グラフ）
		$click_counts = $this->_very_model->get_very_report_clicks($this->_bot_id, $scene_cd, $start_time, $end_time);
		// クリック数（テーブル）
		$click_counts_for_table_data_and_columns = $this->_very_model->get_very_report_clicks_for_table($this->_bot_id, $scene_cd, $start_time, $end_time);
		$click_counts_for_table = $click_counts_for_table_data_and_columns['data'];
		$columns_for_table = $click_counts_for_table_data_and_columns['columns'];

		$scenes_with_data = $this->_very_model->get_scene_cds_has_data($this->_bot_id);

		$view = View::factory($this->_view_path . 'reportclick');
		$view->post = $post;
		$view->scenes_with_data = $scenes_with_data;
		$view->scene_cd = $scene_cd;
		$view->_bot_id = $this->_bot_id;
		$view->click_counts_json_encoded = json_encode($click_counts);
		$view->click_counts_for_table_json_encoded = json_encode($click_counts_for_table);
		$view->columns_for_table_json_encoded = json_encode($columns_for_table);
		$this->template->content = $view;
	}

	public function action_veryqr(){
		$post = NULL;
		$scene_cd = NULL;
		// https://activalues.atlassian.net/browse/SUPPORT-326
		// 導線指定なしもQRコード作成できるように
		if (isset($_GET['scene_cd']) && !empty($_GET['scene_cd'])) {
			$scene_cd = $_GET['scene_cd'];
		} else {
			$scene_cd = null;
		}
		if ($this->request->post()) {
			$post = $this->request->post();
			if ($post["page_action"] == "save"){
				//該当ボットにt_print_mediaのデータあれば更新、なければ新規作成
				$orm = ORM::factory('printmedia')->where('bot_id', '=', $this->_bot_id)->where('media_type', '=', 'very_qr_poster')->where('scene_cd', '=', $scene_cd)->find();
				if (isset($orm->seq)) {
					DB::update('t_print_media')->set(array(
						'settings'=> $post["settings"],
						))
						-> where('bot_id', '=', $this->_bot_id)
						->where('seq', '=', $orm->seq)
						->where('media_type', '=', 'very_qr_poster')
						->where('scene_cd', '=', $scene_cd)
						->execute();
				}
				else {
					//新規作成
					$printmedia = ORM::factory('printmedia');
					$printmedia->bot_id = $this->_bot_id;
					$printmedia->media_type = 'very_qr_poster';
					$printmedia->scene_cd = $scene_cd;
					$printmedia->settings = $post["settings"];
					$printmedia->save();
				}
				$this->_very_model->create_or_get_very_obfuscation_id($this->_bot_id, $scene_cd, null);
				$settings = json_decode($post['settings'], true);
				if (trim($settings['room_number']) !== "") {
					$room_numbers = explode(',', $settings['room_number']);
					foreach ($room_numbers as $room_number) {
						$this->_very_model->create_or_get_very_obfuscation_id($this->_bot_id, $scene_cd, trim($room_number));
					}
				}
				
			}
		}

		$orm = ORM::factory('printmedia')->where('bot_id', '=', $this->_bot_id)->where('media_type', '=', 'very_qr_poster')->where('scene_cd', '=', $scene_cd)->find();
		$view = View::factory ($this->_view_path . 'veryqr');

		if (isset($orm->seq)) {
			$settings = json_decode($orm->settings, true);
			if (isset($settings['icon']) && is_array($settings['icon'])) {
				$icons = $settings['icon'];
				foreach ($icons as &$icon) {
					$fileExtension = pathinfo($icon, PATHINFO_EXTENSION);
					switch (strtolower($fileExtension)) {
						case 'svg':
							if (strpos($icon, 'http') === 0) {
								$data = file_get_contents($icon);
							} else {
								$data = file_get_contents(APPPATH . '..' . $icon);
							}
							$base64 = 'data:image/svg+xml;base64,' . base64_encode($data);
							break;
						case 'png':
							if (strpos($icon, 'http') === 0) {
								$data = file_get_contents($icon);
							} else {
								$data = file_get_contents(APPPATH . '..' . $icon);
							}
							$base64 = 'data:image/png;base64,' . base64_encode($data);
							break;
						case 'jpg':
						case 'jpeg':
							if (strpos($icon, 'http') === 0) {
								$data = file_get_contents($icon);
							} else {
								$data = file_get_contents(APPPATH . '..' . $icon);
							}
							$base64 = 'data:image/jpeg;base64,' . base64_encode($data);
							break;
						default:
							$base64 = null;
							break;
					}
					if ($base64 !== null) {
						$icon = $base64;
					}
				}
				$settings['icon'] = $icons;
			}
			$view->settings = $settings;
		} else {
			//初回設定ない時のデフォルト表示
			$view->settings = [
				"obfuscation_url_flg" => '0',
				"pop_flg" => '0',
				"template" => "template_1",
				"size" => "A6",
				"background_color" => '#FFFFFF', 
				"color" => '#000000', 
				"qr_color" => '#000000',
				// "icon_color" => "#000000", svgカラーは未対応
				"logo_url" => $this->_model->get_bot_img_message_list($this->_bot_id, 'very_qr_poster_logo_default', 'ja')[0]["msg_image"], 
				"text_1" => 'Hotel & Travel information',
				"text_2" => '館内情報・周辺案内',
				"room_flg" => '0', 
				"room_number" => '',
				"icon" => ["/assets/common/talkappi-modal/images/02/ferry.svg","/assets/common/talkappi-modal/images/01/party.svg","/assets/common/talkappi-modal/images/05/wedding.svg"]
			];
		}
		
		// ユーザー導線
		$scenes_with_data = $this->_very_model->get_scene_cds_has_data_for_qr($this->_bot_id);

		$very_qr_poster_settings = json_decode($this->_very_model->get_print_media_settings("very_qr_poster")[0]["settings"],true);
		
		// generate obfuscation url starts
		$obfuscation_id = $this->_very_model->get_very_obfuscation_id($this->_bot_id, $scene_cd, null);
		$obfuscation_url = $this->_model->get_env('very_url') . '?id=' . $obfuscation_id;
		
		$obfuscation_urls_by_room = [];
		
		$rooms = $settings['room_number'] ?? '';
		if (trim($rooms) !== "") {
			$room_numbers = explode(',', $rooms);
			foreach ($room_numbers as $room_number) {
				$room_obfuscation_id = $this->_very_model->get_very_obfuscation_id($this->_bot_id, $scene_cd, trim($room_number));
				if ($room_obfuscation_id !== "") {
					$obfuscation_urls_by_room[$room_number] = $this->_model->get_env('very_url') . '?id=' . $room_obfuscation_id;
				}
			}
		}
		$view->obfuscation_urls_by_room = $obfuscation_urls_by_room;
		$view->obfuscation_url = $obfuscation_url;
		// generate obfuscation url ends
		$view->flg_very_access_by_bot_id = $this->_very_model->get_bot_setting($this->_bot_id, "flg_very_access_by_bot_id");

		$view->user_role = $this->_user->role_cd;
		// pop設定についてのメモ
		// 1. $settingsがない = 初回設定ない時のデフォルト表示 = qrコード表示
		// 2. $settingsがある && $settings['pop_flg']がない = 古いIF = POP表示
		// 3. $settingsがある && $settings['pop_flg']がある = 新しいIF = $settings['pop_flg']に従う
		$canvas_size_setting = 'qr';
		if (isset($settings) && (!isset($settings['pop_flg']) || $settings['pop_flg'] !== '0')) {
			$canvas_size_setting = $view->settings["size"];
		}
		$view->canvas_size = $very_qr_poster_settings["size"][$canvas_size_setting];
		$view->qr_url = $this->_model->get_env('very_url') . '?bot_id=' . $this->_bot_id;
		$view->scene_list = $scenes_with_data;
		$view->scene_cd = $scene_cd;
		//s3でのurlの場合はクライアント側の写真合成の際にcanvas汚染になるので、サーバー側からbase64形式にしてクライアント側に渡す
		if (strlen($view->settings["logo_url"]) > 0){
			$view->base64_logo_url = "data:image/png;base64," . base64_encode(file_get_contents($view->settings["logo_url"]));
		} else {
			$view->base64_logo_url = "";
		}
		$this->template->content = $view;
	}

	public function action_laundry()
	{
		$flg_laundry = $this->_very_model->get_bot_setting($this->_bot_id, "flg_laundry");
		if ($flg_laundry != 1) {
			// 洗濯機連携が無効の場合はVERY TOPページにリダイレクト
			$this->redirect($this->_action_path . 'verytop');
			return;
		}
		$bot_support_lang = explode(',', $this->_bot->support_lang);
		// botのサポート言語のうち、VERYでサポートしている言語のみを取得
		$support_lang = [];
		foreach ($bot_support_lang as $v) {
			if (!in_array($v, $this->_model->get_setting('very_support_lang'))) continue;
			$support_lang[$v] = $this->_codes['02'][$v];
		}
        // 編集中言語の取得
        $lang_edit = $_GET['lang'] ?? null;

        if ($lang_edit === null || !in_array($lang_edit, array_keys($support_lang))) {
            $lang_edit = reset($bot_support_lang);
            $this->redirect($this->_action_path . 'laundry?lang=' . $lang_edit);
        }

		// botに紐づく洗濯機設定情報をt_bot_settingより取得（idとdevice_id）
		$laundry_bot_data = $this->_very_model->get_bot_setting($this->_bot_id, "json_laundry_setting");
		$laundry_settings_data = $this->_very_model->get_very_settings($this->_bot_id, '', 'laundry', $lang_edit);
		$laundry_data = json_decode($laundry_settings_data[0]["value"], true); // trueを指定して連想配列として取得

		$laundry_usage_info = $laundry_data["usage_info"];
		$laundry_list_data = $laundry_data["laundry_list"];

		$laundry_device_list = array();

        foreach (json_decode($laundry_bot_data, true) as $laundry_bot_data) {
            $device_id = $laundry_bot_data['device_id'];
            $device_name = $this->_very_model->get_laundry_device_name_from_api($device_id);
            $matched = false;

            foreach ($laundry_list_data as $laundry_item) {
                if ($laundry_item['id'] == $laundry_bot_data['id']) {
                    $laundry_device_list[] = [
                        'id' => $laundry_bot_data['id'],
                        'device_id' => $device_id,
                        'device_name' => $device_name,
                        'name' => $laundry_item['name'],
                        'description' => $laundry_item['description']
                    ];
                    $matched = true;
                    break;
                }
            }

            if (!$matched) {
                $laundry_device_list[] = [
                    'id' => $laundry_bot_data['id'],
                    'device_id' => $device_id,
                    'device_name' => $device_name,
                    'name' => '',
                    'description' => ''
                ];
            }
        }

		// 配列をidの昇順でソート
		usort($laundry_device_list, function ($a, $b) {
			return $a['id'] <=> $b['id'];
		});

		$obfuscation_id = $this->_very_model->get_very_obfuscation_id($this->_bot_id, null, null);

		$very_laundry_url = $this->_generate_very_preview_url('laundry', $lang_edit, $obfuscation_id);

		$view = View::factory($this->_view_path . 'laundry');
		$view->lang_edit = $lang_edit;
		$view->support_lang = $support_lang;
		$view->laundry_usage_info = $laundry_usage_info;
		$view->laundry_device_list = $laundry_device_list;
		$view->very_laundry_url = $very_laundry_url;

		$this->template->content = $view;
	}

	public function action_reception(){
		$item_div = 14;
		$isReceptionOnlyRole = $this->_user->role_cd == 11;
		if ($isReceptionOnlyRole) {
			$this->redirect($this->_action_path . 'receptions');
		}
		$post = NULL;
		$preview_url = '';

		if ($this->request->post()) {
			$post = $this->request->post();
			$display_lang_cd = implode(',', json_decode($post['display_lang_cd'], true));
			$reception_id = $post['id'];
			if ($post['page_action'] === 'delete') {
				DB::update('t_reception')->set(array(
					'delete_flg'=> 1, 
					'upd_time'=> date('Y-m-d H:i:s',time()), 
					'upd_user'=> $this->_user_id, 
					))->where('reception_id', '=', $reception_id)->execute();
				$this->redirect($this->_action_path . 'receptions');
			} else {
				if ($reception_id == NULL) { //保存
					$reception_id = $this->_model->get_max_reception_id($this->_bot_id);
					DB::insert('t_reception', array('reception_id', 'bot_id'))->values(array($reception_id, $this->_bot_id))->execute();
					DB::insert('t_item_display', array('bot_id', 'item_id', 'item_div', 'lang_display'))
					->values(array($this->_bot_id, $reception_id, $item_div, $display_lang_cd))->execute();
				} else {
					DB::update('t_item_display')->set(array(
						'lang_display'=> $display_lang_cd))
						-> where('bot_id', '=', $this->_bot_id)->where('item_div', '=', $item_div)->where('item_id', '=', $reception_id)->execute();
				}

				// 更新
				DB::update('t_reception')->set(array(
				'support_lang_cd'=>implode(',', json_decode($post['support_lang_cd'], true)),
				'pause_flg'=>$post['pause_flg'],
				'display_flg'=>$post['display_flg'],
				'reception_data'=> $this->_set_reception_data($reception_id, $post),
				'printer_settings'=> $post['printer_settings'] ? $post['printer_settings'] : NULL,
				'item_div' => $post['item_div'] !== "" ? $post['item_div'] : NULL,
				'class_cd' => $post['class_cd'] !== '[]' ? implode(',', json_decode($post['class_cd'], true)) : '',
				'image' => $post['image'] !== '' ? $post['image'] : '',
				'item_id' => $post['item_id'] !== '' ? $post['item_id'] : NULL,
				'upd_time'=> date('Y-m-d H:i:s',time()), 
				'upd_user'=> $this->_user_id, 
				))->where('reception_id', '=', $reception_id)->execute();

				// UPSERT
				$titles = json_decode($post['reception-title'], JSON_UNESCAPED_UNICODE);
				if ($titles['ja'] !== '') {
					foreach ($titles as $lang => $title) {
						$description = json_decode($post["description"], JSON_UNESCAPED_UNICODE);
						$description_text = $description[$lang] ?: "";
						$sql = "
							INSERT INTO t_reception_description (reception_id, lang_cd, title, description)
							VALUES (:reception_id, :lang_cd, :title, :description)
							ON DUPLICATE KEY UPDATE
							title = VALUES(title),
							description = VALUES(description)
						";
						DB::query(Database::INSERT, $sql)
							->param(':reception_id', $reception_id)
							->param(':lang_cd', $lang)
							->param(':title', $title)
							->param(':description', $description_text)
							->execute();
					}
				}
	
				$this->redirect($this->_action_path . 'reception?id=' . $reception_id);
			}
		} else {
			$reception_id = $this->request->query('id', NULL);

			foreach ($this->_bot_lang as $lang => $v) {
				$lang_defalut[$lang] = '';
				$title[$lang] = '';
				$description[$lang] = '';
			}
			
			if ($reception_id == NULL) {
				$post = [
					'reception_id' => '',
					'image' => '',
					'item_id' => '',
					'class_cd'=> [], 
					'support_lang_cd'=> [$this->_model->get_admin_lang()], 
					'display_lang_cd'=> [$this->_model->get_admin_lang()], 
					'reception_data'=> [
						"period_new"=>[
							[
								"title"=>"",
								"start_date"=>date('Y-m-d'),
								"end_date"=>date('Y-m-d', strtotime('+1 month')),
								"weekdays"=>[
									"1",
									"2",
									"3",
									"4",
									"5",
									"6",
									"7"
								],
								"holiday"=>"include",
								"excluded_dates"=>"",
								"dates"=>[
									[
										"start"=>"9:00",
										"end"=>"17:00"
									]
								]
							]
						],
						'printer_settings' => null,
						'pause_flg' => '0',
						'display_flg' => '1',
					]
				];
			} else {
				$reception = ORM::factory('reception', $reception_id);
				$display = ORM::factory('itemdisplay')->where('bot_id', '=', $this->_bot_id)->where('item_id', '=', $reception_id)->where('item_div', '=', $item_div)->find();
				$post = [
					'reception_id' => $reception_id,
					'item_id' => $reception->item_id,
					'image' => $reception->image,
					'class_cd'=> $reception->class_cd ? explode(',', $reception->class_cd) : [], 
					'support_lang_cd'=> explode(',', $reception->support_lang_cd), 
					'display_lang_cd'=> explode(',', $display->lang_display), 
					'reception_data'=>json_decode($reception->reception_data, true),
					'printer_settings' => $reception->printer_settings ? json_decode($reception->printer_settings, true) : null,
					'pause_flg' => $reception->pause_flg,
					'display_flg' => $reception->display_flg,
				];
				$receptiondesc = ORM::factory('receptiondescription')->where('reception_id', '=', $reception_id)->find_all();
				$lang_defalut = [];
				$title = [];
				$description = [];
				foreach ($receptiondesc as $desc) {
					$lang_defalut[$desc->lang_cd] = '';
					$title[$desc->lang_cd] = $desc->title;
					$description[$desc->lang_cd] = $desc->description;
				}
				
				$obfuscation_id = $this->_very_model->get_very_obfuscation_id($this->_bot_id, null, null);

				$preview_url = $this->_generate_very_preview_url('receptionlist', $this->_lang_cd, $obfuscation_id);
				$preview_url .= '&reception_id=' . $reception_id;
				if ($reception->display_flg == 0) {
					$tokenPost = [];
					$tokenPost['data'] = json_encode(["reception_id"=>$reception_id,"bot_id"=>$this->_bot_id]);
					$preview_url .= '&token=' . $this->_model->get_new_token($tokenPost);
				}
			}

		}
		$view = View::factory ($this->_view_path . 'reception/reception');
		$view->bot_lang = $this->_model->get_bot_support_lang(ORM::factory('bot', $this->_bot_id));
		$view->post = $post;
		$view->class_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_1');
		$view->title = $title;
		$view->preview_url = $preview_url;
		$view->public_display_url =  $this->_model->get_env('admin_url') . 'adminvery/reception_display?id=' . $reception_id;
		$view->description = $description;
		$view->capacity_data = isset($post['reception_data']['capacity']) ? $post['reception_data']['capacity'] : [];
		$view->lang = $lang_defalut;
		$view->reception_id = $reception_id;
		$this->template->content = $view;
	}

	public function action_receptionentry(){
		$post = NULL;
		$reception_id = $this->request->query('id');
		$lang_cd = $this->request->query('lang_cd', NULL);
		$_lang_cd = I18n::$lang = $this->_lang_cd;
		$preview_url = '';

		if ($this->request->post()) {
			$post = $this->request->post();
			$entries = json_decode($post['entries'], true);
			$translate = $post['translate'];
			$translate_lang = isset($post['translate_lang']) ? json_decode($post['translate_lang'], true) : [];

			DB::delete('t_reception_entry')->where('reception_id', '=', $reception_id)->where('lang_cd', '=', $lang_cd)->execute();

			foreach ($entries as $entry) {
				$orm = ORM::factory('receptionentry');
				$orm->reception_id = $reception_id;
				$orm->no = $entry['no'];
				$orm->lang_cd = $lang_cd;
				$orm->title = $entry['title'];
				$orm->title_description = '';
				$orm->entry_type_cd = $entry['entry_type_cd'];
				$orm->entry_data = $entry['entry_data'];
				$orm->required = $entry['required'];
				$orm->input_rules = !empty($entry['input_rules']) ? json_encode($entry['input_rules']) : '';
				$orm->save();
			}

			// 自動翻訳が選ばれている場合
			if ($translate === 'translate_auto') {
				foreach ($translate_lang as $target_lang) {
					DB::delete('t_reception_entry')->where('reception_id', '=', $reception_id)->where('lang_cd', '=', $target_lang)->execute();

					foreach ($entries as $entry) {
						$translated_entry = ORM::factory('receptionentry');
						$translated_entry->reception_id = $reception_id;
						$translated_entry->no = $entry['no'];
						$translated_entry->lang_cd = $target_lang;
						$translated_entry->title = $this->_model->translate($entry['title'], $target_lang, $lang_cd);
						$translated_entry->title_description = '';
						$translated_entry->entry_type_cd = $entry['entry_type_cd'];
						
						if ($entry['entry_type_cd'] === 'opt' && !empty($entry['entry_data'])) {
							// opt の場合 entry_data の title のみを翻訳する
							$entry_data = json_decode($entry['entry_data'], true);
							if (is_array($entry_data)) {
								foreach ($entry_data as $key => &$option) {
									if (is_string($option)) {
										// 文字列配列の場合は、文字列を直接翻訳
										$entry_data[$key] = $this->_model->translate($option, $target_lang, $lang_cd);
									} elseif (is_array($option) && isset($option['title'])) {
										// オブジェクト配列の場合は、title のみを翻訳
										$option['title'] = $this->_model->translate($option['title'], $target_lang, $lang_cd);
									}
								}
							}
							$translated_entry->entry_data = json_encode($entry_data,JSON_UNESCAPED_UNICODE);
						} else {
							$translated_entry->entry_data = '';
						}

						$translated_entry->required = $entry['required'];
						$translated_entry->input_rules = !empty($entry['input_rules']) ? json_encode($entry['input_rules']) : '';
						$translated_entry->save();
					}
				}
			}
		}
		$reception = ORM::factory('reception')->where('reception_id', '=', $reception_id)->find();
		$obfuscation_id = $this->_very_model->get_very_obfuscation_id($this->_bot_id, null, null);

		$preview_url = $this->_generate_very_preview_url('receptionlist', $lang_cd, $obfuscation_id);

		$query = '&reception_id=' . $reception_id;
		if ($reception->display_flg == 0) {
			$tokenPost = ['data' => json_encode(["reception_id" => $reception_id, "bot_id" => $this->_bot_id])];
			$query .= '&token=' . $this->_model->get_new_token($tokenPost);
		}
		$preview_url .= $query;
		$edit_lang_cd = array();
		foreach ($this->_model->get_bot_support_lang(ORM::factory('bot', $this->_bot_id)) as $lang => $v) {
			if(in_array($lang, explode(',', $reception->support_lang_cd))){
				$edit_lang_cd[$lang] = $v;
			}
		}
		if ($lang_cd == NULL) {
			if (!empty($reception->support_lang_cd)) {
				$langs = explode(',',$reception->support_lang_cd);
				if (count($langs) > 0) $lang_cd = $langs[0];
				$this->redirect("/adminvery/receptionentry?id=$reception_id&lang_cd=$lang_cd");
			}
		}
		if ($lang_cd == NULL) $this->redirect('/adminvery/reception?id=' . $reception_id);
		$view = View::factory ($this->_view_path . 'reception/receptionentry');
		$view->verify_url = $preview_url;
		$receptionentry = ORM::factory('receptionentry')->where('reception_id', '=', $reception_id)->where('lang_cd', '=', $lang_cd)->find_all();
		$post = [];
		foreach ($receptionentry as $entry) {
			array_push($post, 
				[
					'reception_id' => $reception_id,
					'no' => $entry->no,
					'lang_cd' => $entry->lang_cd,
					'title' => $entry->title,
					'entry_type_cd' => $entry->entry_type_cd,
					'required' => $entry->required,
					'input_rules' => $entry->input_rules,
					'entry_data' => $entry->entry_data,
				]
			);
		}
		$view ->post = $post;
		$view ->admin_lang_cd = $_lang_cd;
		$view ->edit_lang_cd = $edit_lang_cd;
		$view ->reception_id = $reception_id;
		$view-> display = json_decode($reception->reception_data, true)['display'];
		$this->template->content = $view;
	}

	public function action_receptiondisplay()
	{

		$reception_id = $this->request->query('id');
		$reception = ORM::factory('reception')->where('reception_id', '=', $reception_id)->find();
		
		// VERYのサポート言語を取得
		$reception_support_lang = explode(',', $reception->support_lang_cd);
		$support_lang = [];
		foreach ($reception_support_lang as $v) {
			if (!in_array($v, $this->_model->get_setting('very_support_lang'))) continue;
			$support_lang[$v] = $this->_codes['02'][$v];
		}
		$lang_edit = $_GET['lang_cd'] ?? null;
	
		if ($lang_edit === null || !in_array($lang_edit, array_keys($support_lang))) {
			$lang_edit = reset($reception_support_lang);
			$this->redirect($this->_action_path . "receptiondisplay?id=$reception_id&lang_cd=$lang_edit");
		}
		
		$receion_display_settings = $this->_very_model->get_reception_display_settings($reception_id);
		$json_display_settings = json_decode($receion_display_settings[0]['reception_data'], true);
		$display_settings = $json_display_settings['display'] ?? [];
		
		$display_data = $this->_very_model->get_reception_display_data($reception_id, $lang_edit);

		$view = View::factory($this->_view_path . 'reception/receptiondisplay');
		$view->lang_edit = $lang_edit;
		$view->support_lang = $support_lang;
		$view->reception_id = $reception_id;
		$view->display_settings = $display_settings;
		$view->display_data = $display_data;
		$this->template->content = $view;
	}

	private function _set_reception_data($reception_id, $reception)
	{
		error_log(json_encode($reception));
		$data_index = [
			'items',
			'waiting_time',
			'waiting_party',
			'notification',
			'cancel',
			'reception_no_prefix',
			'registration_necessary',
			'capacity',
			'auto_pause',
			'max_reception_party',
			'display_style'
		];
	
		$existing_record = ORM::factory('reception', $reception_id);
		$existing_reception_data = $existing_record && $existing_record->loaded() 
			? json_decode($existing_record->reception_data, true) ?? [] 
			: [];

		$reception_data = [];
	
		// ディスプレイ表示処理
		$display = [];
		if (isset($reception['display_on_screen']) && $reception['display_on_screen'] !== "0") {
			if (!empty($reception['waiting_display']) && $reception['waiting_display'] === "1") {
				$display[] = 1;
			}
			if (!empty($reception['calling_display']) && $reception['calling_display'] === "1") {
				$display[] = 3;
			}
			if (!empty($reception['auto_cancel_display']) && $reception['auto_cancel_display'] === "1") {
				$display[] = 6;
			}
			if (!empty($reception['text_display']) && $reception['text_display'] === "1") {
				$display[] = 9;
			}
		}
		$reception_data['display'] = $display;
	
		foreach ($data_index as $index) {
			if (array_key_exists($index, $reception)) {
				if ($index === "items") {
					$reception_data["period_new"] = json_decode($reception[$index], true);
				}
				else if ($index === "waiting_time") {
					$reception_data['waiting']['time'] = $reception[$index];
				}
				else if ($index === 'waiting_party') {
					$reception_data['waiting']['party'] = $reception[$index];
				}
				else if ($index === 'display_style') {
					$reception_data['display_style'] = isset($reception[$index]) ? json_decode($reception[$index], true) : ($existing_reception_data['display_style'] ?? []);
				}
				else {
					$reception_data[$index] = $reception[$index];
				}
			}
		}
	
		$merged_reception_data = array_merge($existing_reception_data, $reception_data);
		error_log(json_encode($merged_reception_data));
		return json_encode($merged_reception_data);
	}
	

	public function action_receptions(){
		if ($this->request->post()) {
			$post = $this->request->post();
			if ($post['page_action'] === 'display') {
				DB::update('t_reception')->set(array(
					'display_flg'=> $post['display_flg'], 
					'upd_time'=> date('Y-m-d H:i:s',time()), 
					'upd_user'=> $this->_user_id, 
					))->where('reception_id', '=', $post['reception_id'])->execute();
			} else {
				DB::update('t_reception')->set(array(
					'delete_flg'=> 1, 
					'upd_time'=> date('Y-m-d H:i:s',time()), 
					'upd_user'=> $this->_user_id, 
					))->where('reception_id', '=', $post['reception_id'])->execute();
			}
		}
		$pause = $this->request->query('pause');
		$not_public = $this->request->query('not_public');
		$items = $this->_very_model->get_reception_items($this->_bot_id, $this->_lang_cd, $pause, $not_public);
		$view = View::factory ($this->_view_path . 'reception/receptions');
		$view->items = $items;
		$this->template->content = $view;
	}

	public function action_userguide() {
		$lang = $_GET['lang'] ?? explode(',', $this->_bot->support_lang)[0];
		if (!isset($_GET['lang'])) {
			$this->redirect($this->_action_path . 'userguide?lang=' . $lang);
		}
        $scene_cd= $this->_model->get_scene_cd_with_default($this->_bot_id, $_GET['scene_cd']);
		if ($scene_cd !== $_GET['scene_cd']) {
			$params = $_GET;
			$params['scene_cd'] = $scene_cd;
			$query = http_build_query($params);
			$this->redirect($this->_action_path . 'userguide?' . $query);
		}
		$page_settings = $this->_very_model->get_very_settings($this->_bot_id, $scene_cd, 'info', $lang);
		$support_lang = $this->_model->get_bot_support_lang(ORM::factory('bot', $this->_bot_id));

		if ($this->request->post()) {
			$post = $this->request->post();
			if ($this->_page_action == 'apply-template') {
				$templateValue = $this->_model->get_config('userguidetemplate', $lang);
				$newValue = $templateValue;
				foreach ($templateValue as $index => $value) {
					if (is_array($value['content'])) {
						$content = new stdClass();
						$content->type = 1;
						$content_text = '';
				
						if (is_array($value['content'][0])) {
							// Handle array of arrays case
							foreach ($value['content'] as $subValue) {
								$content_text .= $this->processContent($subValue, $lang, $this->_bot_id);
							}
						} else {
							// Handle single array case
							$content_text .= $this->processContent($value['content'], $lang, $this->_bot_id);
						}
				
						$content->text = $content_text;
						$newValue[$index]['content'] = $content;
					} else {
						// Handle unexpected type of $value['content']
						continue;
					}
				}
				$data = array(
					'value'=> json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
					'upd_user'=> $this->_user_id,
					'upd_time'=>date('Y-m-d H:i:s',time())
				);

				if(count($page_settings) === 0) {
					$data['bot_id'] = $this->_bot_id;
					$data['scene_cd'] = $scene_cd;
					$data['page'] = 'info';
					$data['setting'] = 'section';
					$data['lang_cd'] = $lang;
					DB::insert('t_very_settings', array_keys($data))
						->values(array_values($data))
						->execute();
				} else {
					DB::update('t_very_settings')->set($data)
						->where('bot_id', '=', $this->_bot_id)
						->where('scene_cd', '=', $scene_cd)
						->where('page', '=', 'info')
						->where('setting', '=', 'section')
						->where('lang_cd', '=', $lang)
						->execute();
				}
				$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>$this->_page_action, "lang_cd"=>$lang]);
				$this->redirect('/adminvery/userguide?lang=' . $lang . '&scene_cd=' . $scene_cd);
				return;
			} else {
				$bot_id = $this->_bot_id;
				$items = json_decode($post['items'], true);
				$encoded_items = json_encode($items, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
				if(count($page_settings) === 0) {
					DB::insert('t_very_settings', array(
						'bot_id',
						'page',
						'scene_cd',
						'setting',
						'lang_cd',
						'value',
						'upd_user',
						'upd_time',
						))->values(array(
						$bot_id,
						"info",
						$scene_cd,
						"section",
						$lang,
						$encoded_items,
						$this->_user_id,
						date('Y-m-d H:i:s',time()),
						))->execute();
					$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>'add', "lang_cd"=>$lang]);
				}else{
					DB::update('t_very_settings')->set(array(
						'value'=> $encoded_items,
						'upd_user'=> $this->_user_id,
						'upd_time'=>date('Y-m-d H:i:s',time())
						))
						->where('bot_id', '=', $bot_id)
						->where('scene_cd', '=', $scene_cd)
						->where('page', '=', 'info')
						->where('setting', '=', 'section')
						->where('lang_cd', '=', $lang)
						->execute();
					$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>'modify', "lang_cd"=>$lang]);
				}

				if (isset($post['translate']) && $post['translate'] == 'translate_auto') {
					foreach ($post['translate_lang'] as $translate_lang) {
						$target_lang = $translate_lang;
						$orm = ORM::factory('verysettings')
									->where('bot_id', '=', $this->_bot_id)
									->where('scene_cd', '=', $scene_cd)
									->where('page', '=', 'info')
									->where('lang_cd', '=', $lang)
									->find();
						$valueFromDB = str_replace('\n', '<br>', $orm->value);
						$originValue = json_decode($valueFromDB, true);
						$newValue = $originValue;
						foreach ($originValue as $index => $value) {
							if (array_key_exists('title', $value)) {
								$from_title = $value['title'];
								$translateResult = $this->_model->translate($from_title, $target_lang);
								if ($translateResult != False) {
									$newValue[$index]['title'] = $translateResult;
								}
							}
							if (array_key_exists('content', $value)) {
								$from_content_text = $value['content']['text'];
								$translateResult = $this->_model->translate($from_content_text, $target_lang);
								if ($translateResult != False) {
									$newValue[$index]['content']['text'] = $translateResult;
								}
							}
						}
						$data = array(
							'value'=> json_encode($newValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
							'upd_user'=> $this->_user_id,
							'upd_time'=>date('Y-m-d H:i:s',time())
						);
						$translationExistsInDB = ORM::factory('verysettings')
													  ->where('bot_id', '=', $this->_bot_id)
													  ->where('scene_cd', '=', $scene_cd)
													  ->where('page', '=', 'info')
													  ->where('setting', '=', 'section')
													  ->where('lang_cd', '=', $target_lang)
													  ->find_all();
						if(count($translationExistsInDB) === 0) {
							// データがない場合insert
							$data['bot_id'] = $this->_bot_id;
							$data['scene_cd'] = $scene_cd;
							$data['page'] = 'info';
							$data['setting'] = 'section';
							$data['lang_cd'] = $target_lang;
							DB::insert('t_very_settings', array_keys($data))
								->values(array_values($data))
								->execute();
						} else {
							// データがある場合update
							DB::update('t_very_settings')->set($data)
								->where('bot_id', '=', $this->_bot_id)
								->where('scene_cd', '=', $scene_cd)
								->where('page', '=', 'info')
								->where('setting', '=', 'section')
								->where('lang_cd', '=', $target_lang)
								->execute();
						}
						$this->_write_access_log(["page_action"=>$this->_page_action, "action"=>$this->_page_action, "lang_cd"=>$lang]);
					}
				}
				$this->redirect('/adminvery/userguide?lang='. $lang . '&scene_cd=' . $scene_cd);
			}
		}

		if(count($page_settings) === 0){
			$items_array = [];
			foreach($support_lang as $k=>$v) {
				$items_array[$k][] = [
					'title'=>'',
					'icon'=>'/01/access.svg',
					'content'=> [
						'type'=>'',
						'text'=>''
						]
					];
			}
			$items = json_encode($items_array,JSON_UNESCAPED_UNICODE);
		}
		else{
			$items = $page_settings[0]['value'];
		}

		$lang_arr = explode(',', $this->_bot->support_lang);
		$translate_to_lang = [];
		foreach ($lang_arr as $lang) {
			$translate_to_lang[$lang] = $this->_codes['02'][$lang];
		}
		// ユーザー導線
		$scene_list = $this->_very_model->get_scene_cds_has_data($this->_bot_id, false);
		
		$lang_edit = $_GET['lang'] ?? 'ja';
		$bot_id = $this->_bot_id;

		$obfuscation_id_with_scene_cd = $this->_very_model->get_very_obfuscation_id($bot_id, $scene_cd, null);

		$very_userguide_url = $this->_generate_very_preview_url('userguide', $lang_edit, $obfuscation_id_with_scene_cd, $scene_cd);

		$view = View::factory($this->_view_path . 'userguide');
		$view->scene_list = $scene_list;
		$view-> scene_cd = $scene_cd;
		$view-> lang_edit = $lang_edit;
		$view->very_userguide_url = $very_userguide_url;
		$view->support_lang = $support_lang;
		$view->items = json_decode($items);
		$view->translate_to_lang = $translate_to_lang;
		$this->template->content = $view;
	}
	
	/**
	 * Processes content based on its type.
	 *
	 * @param array $content
	 * @param string $lang
	 * @param int $bot_id
	 * @return string
	 */
	private function processContent($content, $lang, $bot_id) {
		if ($content['type'] == 1) {
			return $content['text'];
		} else {
			$intent_cd = $content['intent_cd'];
			$sub_cd = $content['sub_cd'];
			return $this->getFaqContent($intent_cd, $sub_cd, $lang, $bot_id);
		}
	}

	/**
	 * Fetches FAQ content based on intent and sub-intent codes.
	 *
	 * @param string $intent_cd
	 * @param string $sub_cd
	 * @param string $lang
	 * @param int $bot_id
	 * @return string
	 */
	private function getFaqContent($intent_cd, $sub_cd, $lang, $bot_id) {
		$parent_bot = $this->_model->get_grp_bot_id($bot_id);
		if ($parent_bot <= 0) $parent_bot = $bot_id;
		$faq = ORM::factory('botintent')
			->where_open() 
			->where('bot_id', '=', $bot_id)
			->or_where('bot_id', '=', $parent_bot)
			->where_close()
			->where('intent_cd', '=', $intent_cd)
			->where('sub_intent_cd', '=', $sub_cd)
			->where('lang_cd', '=', $lang)
			->order_by(DB::expr("CASE WHEN bot_id = {$bot_id} THEN 1 ELSE 2 END"))
			->find();

		$content_text = '';
		for ($i = 1; $i <= 9; $i++) {
			if ($faq->{'answer' . $i . '_type_cd'} == 'txt') {
				$content_text .= '<p>' . $faq->{'answer' . $i} . '</p>';
			}
		}
		
		return $content_text;
	}
	
	public function action_receptionlist() {
		$reception_id = $this->request->query('id');
		$reception_list_information = $this->_very_model->get_reception_list_information($this->_bot_id, $this->_lang_cd, $reception_id);
		$reception_list_items = $this->_very_model->get_reception_list_items($this->_bot_id, $reception_id);
		$reception_entry = $this->_very_model->get_reception_entry($reception_id, $this->_lang_cd);

		$view = View::factory ($this->_view_path . 'receptionlist');
		$view->reception_id = $reception_id;
		$view->lang_cd = $this->_lang_cd;
		
		if(!$reception_list_information[0]["item_id"]){
			$view->title = $reception_list_information[0]["title"];
		} else {
			$contens =  $this->_model->get_contents_by_id($reception_list_information[0]["item_id"], $this->_lang_cd);
			$view->title = $contens[0]['item_name'];
		}
		
		$capacity = [];
		$reception_data = json_decode($reception_list_information[0]["reception_data"], true);
		if (array_key_exists('capacity',$reception_data)) {
			$capacity_data = json_decode($reception_data['capacity'], true);
			if ($capacity_data != NULL) {
				foreach ($capacity_data as $value) {
					$title = json_decode(($value['title']), true);
					$capacity[$value['id']] = isset($title[$this->_lang_cd]) ? $title[$this->_lang_cd] : $title['ja'];
				}
			}
		}

		$view->bot_id = $this->_bot_id;
		$view->pause_flg = $reception_list_information[0]["pause_flg"];
		$view->reception_data = $reception_data;
		$view->capacity = $capacity;
		$view->reception_list_items = $reception_list_items;
		$view->groups_wait = count(
			array_filter($reception_list_items, function ($k) {
				return $k["status_cd"] <= '2';
			}
		));
		$view->reception_entry = $reception_entry;
		$this->template->content = $view;
	}
	
	public function action_reception_operation() {
		$reception_id = $this->request->query('id');
		$reception_list_information = $this->_very_model->get_reception_list_information($this->_bot_id, $this->_lang_cd, $reception_id);
		$reception_list_items = $this->_very_model->get_reception_list_items($this->_bot_id, $reception_id);
		
		$reception_data = json_decode($reception_list_information[0]["reception_data"], true);
		$reception_entries = $this->_very_model->get_reception_entry($reception_id, ["ja", "en"]);

		// 言語ごとにデータを分類
		$reception_ja_entry = [];
		$reception_en_entry = [];
		foreach ($reception_entries as $entry) {
			if ($entry["lang_cd"] === "ja") {
				$reception_ja_entry[] = $entry;
			} elseif ($entry["lang_cd"] === "en") {
				$reception_en_entry[] = $entry;
			}
		}
		
		$printer_settings = NULL;
		if ($reception_list_information[0]["printer_settings"] != NULL) {
			$printer_settings = json_decode($reception_list_information[0]["printer_settings"], JSON_UNESCAPED_UNICODE);
		}

		$view = View::factory ($this->_view_path . 'reception/reception_operation');
		$view->pause_flg = $reception_list_information[0]["pause_flg"];
		$view->reception_data = $reception_data;
		$view->capacity_data = array_key_exists('capacity',$reception_data) ? json_decode($reception_data['capacity'], true) : [];
		$view->reception_ja_entry = $reception_ja_entry;
		$view->reception_en_entry = $reception_en_entry;
		$view->reception_id = $reception_id;
		$view->reception_list_items = $reception_list_items;
		$view->groups_wait = count(
			array_filter($reception_list_items, function ($k) {
				return $k["status_cd"] <= '2';
			}
		));
		$view->bot_id = $this->_bot_id;
		$view->printer_settings = $printer_settings;
		$this->template->content = $view;
	}

	public function action_receptionlog()
	{
		$post = [];
		if ($this->request->post()) {
			$post = $this->request->post();
		} else {
			$post['start_date'] = date('Y/m', strtotime('-1 month', strtotime(date('Y-m-01'))));
			$post['end_date'] = date('Y/m');
		}

		$sub_bots = [];
		$grp_bot_cond = "";
		if ($this->_user->role_cd == '99') {
			// スーパー管理者は子ボットを除く全施設を取得する
			$cond_bot_id = isset($post["cond_bot_id"]) ? $post["cond_bot_id"] : '';
			$bot_array = Session::instance()->get('bot_array');
			$sub_bots = array_map(function ($item) {
				return ['bot_id' => $item[0], 'bot_name' => $item[1]];
			}, $bot_array);
			
			// 絞り込んだ bot_id が親ボットのとき、子ボットの情報を取得する
			$grp_bot_id = $this->_model->get_grp_bot_id($cond_bot_id);
			if ($grp_bot_id === 0) {
				$grp_bot_cond = $this->_model->_create_bot_cond_grp_only($cond_bot_id, "x.bot_id");
			}
		} else {
			$cond_bot_id = $this->_bot_id;
			$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
			// 親ボットの場合すべての施設を取得する
			if ($grp_bot_id === 0) {
				$sub_bots = $this->_model->get_sub_bots($this->_bot_id);
				$cond_bot_id = isset($post["cond_bot_id"]) ? $post["cond_bot_id"] : '';
			}
			array_unshift($sub_bots, ["bot_id" => $this->_bot_id, "bot_name" => $this->_bot->bot_name]);
		}
		$bot_ids = array_column($sub_bots, 'bot_id');
		array_push($bot_ids, $this->_bot_id);
		$bot_ids_str = implode(',', $bot_ids);

		$result = $this->_very_model->get_very_receptionlog($cond_bot_id, $bot_ids_str, $grp_bot_cond, $post["start_date"], $post["end_date"]);

		$view = View::factory($this->_view_path . '/receptionlog');
		$view->post = $post;
		$view->sub_bots = $sub_bots;
		$view->cond_bot_id = $cond_bot_id;
		$view->result = $result;
		$this->template->content = $view;
	}

	public function action_reception_display() {
		$reception_id = $this->request->query('id');
		$secondary_lang_cd = ($this->request->query('lang') == null) ? 'en' : null;
		$lang_cd = $this->request->query('lang') ?? 'ja';

		$setting = $this->_very_model->reception_display_setting($reception_id, $this->_bot_id, $lang_cd);
		$display_item = $display_text = $display_style = null;
		$secondary_display_text = null;

		if ($setting !== false) {
			$display_item = json_decode($setting["display_item"], true);
			$display_text = json_decode($setting["display_text"], true);
			$display_style = json_decode($setting["display_style"], true);
		}

		if ($secondary_lang_cd) {
			$secondary_settings = $this->_very_model->reception_display_setting($reception_id, $this->_bot_id, $secondary_lang_cd);
			if ($secondary_settings !== false) {
				$secondary_display_text = json_decode($secondary_settings["display_text"], true);
			}
		}
		
		$reception_items = $this->_very_model->get_reception_list_items($this->_bot_id, $reception_id);
		$view = View::factory($this->_view_path . 'reception/reception_display');
		$view->display_item = $display_item;
		$view->display_text = $display_text;
		$view->secondary_display_text = $secondary_display_text;
		$view->display_style = $display_style;
		$view->reception_items = $reception_items;
		$view->lang_cd = $lang_cd;
		$this->template->content = $view;
	}

	public function action_copy() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$this->_verytop_copy($this->_bot_id, $post['scene_cd']);
		$this->response->body(json_encode(['result'=>'success', 'message'=>'common.message.success.content_copy_success']));
	}

	public function action_paste() {
		$post = $this->request->post();
		$this->auto_render = FALSE;
		$obj = $this->_verytop_paste();
		if ($obj == null) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		} else {
			Session::instance()->delete('verytop_copy');
			$this->_copy_verytop($obj['bot_id'], $obj['scene_cd'], $post['scene_cd']);
			$this->response->body(json_encode(['result'=>'success', 'message'=>'content_copy_success']));
		}
	}
	private function _copy_verytop($bot_id, $scene_cd, $paste_scene_cd) {
		$copy_from_categories = ORM::factory('verysettings')
		->where('bot_id', '=', $bot_id)
		->where('scene_cd', '=', $scene_cd)
		->where('page', '=', 'top')
		->where('setting', '=', 'categorys')
		->find_all();

		$copy_from_functions = ORM::factory('verysettings')
		->where('bot_id', '=', $bot_id)
		->where('scene_cd', '=', $scene_cd)
		->where('page', '=', 'top')
		->where('setting', '=', 'functions')
		->find_all();

		$copy_from_very_settings = ORM::factory('very')
		->where('bot_id', '=', $bot_id)
		->where('scene_cd', '=', $scene_cd)
		->where('page', '=', 'top')
		->find_all();

		// コピー先の t_very_settings のデータを削除
		$sql = "DELETE
		FROM t_very_settings
		WHERE bot_id = :bot_id
		AND scene_cd = :paste_scene_cd
		AND page = 'top'
		AND setting IN ('categorys','functions')";
		$query = DB::query(Database::DELETE, $sql);
		$query->parameters(array(
			':bot_id' => $this->_bot_id,
			':paste_scene_cd' => $paste_scene_cd
		));
		$query->execute();

		// t_very_settings のデータをコピー
		foreach ($copy_from_categories as $category) {
			// todo: コピー先のサポート言語についての処理
			$new_category = ORM::factory('verysettings');
			$new_category->bot_id = $this->_bot_id;
			$new_category->scene_cd = $paste_scene_cd;
			$new_category->page = $category->page;
			$new_category->setting = $category->setting;
			$new_category->lang_cd = $category->lang_cd;
			$new_category->value = $category->value;
			$new_category->upd_time = date('Y-m-d H:i:s');
			$new_category->upd_user = $this->_user_id;
			$new_category->save();
		}

		foreach ($copy_from_functions as $function) {
			// todo: コピー先のサポート言語についての処理
			$new_function = ORM::factory('verysettings');
			$new_function->bot_id = $this->_bot_id;
			$new_function->scene_cd = $paste_scene_cd;
			$new_function->page = $function->page;
			$new_function->setting = $function->setting;
			$new_function->lang_cd = $function->lang_cd;
			$new_function->value = $function->value;
			$new_function->upd_time = date('Y-m-d H:i:s');
			$new_function->upd_user = $this->_user_id;
			$new_function->save();
		}

		// コピー先の t_very のデータを削除
		$sql = "DELETE
		FROM t_very
		WHERE bot_id = :bot_id
		AND scene_cd = :paste_scene_cd
		AND page = 'top'";

		$query = DB::query(Database::DELETE, $sql);
		$query->parameters(array(
			':bot_id' => $this->_bot_id,
			':paste_scene_cd' => $paste_scene_cd
		));
		$query->execute();

		// t_very のデータをコピー
		foreach ($copy_from_very_settings as $setting) {
			// todo: コピー先のサポート言語についての処理
			$new_very_setting = ORM::factory('very');
			$new_very_setting->bot_id = $this->_bot_id;
			$new_very_setting->scene_cd = $paste_scene_cd;
			$new_very_setting->page = $setting->page;
			$new_very_setting->setting = $setting->setting;
			$new_very_setting->value = $setting->value;
			$new_very_setting->upd_time = date('Y-m-d H:i:s');
			$new_very_setting->upd_user = $this->_user_id;
			$new_very_setting->save();
		}
	}

	private function _generate_very_preview_url($path, $lang_cd, $obfuscation_id = null, $scene_cd = null) {
		$very_url = $this->_model->get_env('very_url');
		$lang_part = $this->_mapping_very_lang($lang_cd);
		$path_part = $path !== '' ? "/{$path}" : '';

		if ($obfuscation_id) {
			$verify_url = "{$very_url}{$lang_part}{$path_part}?id={$obfuscation_id}";
		} else {
			$verify_url = "{$very_url}{$lang_part}{$path_part}?bot_id={$this->_bot_id}&f={$scene_cd}";
		}

		return $verify_url;
	}
}
