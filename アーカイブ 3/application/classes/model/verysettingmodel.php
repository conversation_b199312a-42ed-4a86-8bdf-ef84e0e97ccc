<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Verysettingmodel extends Model_Basemodel
{	
    function insert_very($bot_id, $scene_cd, $page, $setting, $value, $userId){
        $orm = ORM::factory('very');
        $orm->bot_id = $bot_id;
        $orm->scene_cd = $scene_cd;
        $orm->page = $page;
        $orm->setting = $setting;
        $orm->upd_time = date('Y-m-d H:i:s');
        $orm->upd_user = $userId;
        $orm->value = $value;
        return $orm->save();
    }

    function insert_very_stay_control($bot_id, $params) {
        $orm = ORM::factory('verystaycontrol');
        $orm->bot_id = $bot_id;
        $orm->params = $params;
        return $orm->save();
    }

    function delete_all_very ($bot_id, $scene_cd) {
        return DB::delete('t_very')
            ->where('bot_id', '=', $bot_id)
            ->and_where('scene_cd', '=', $scene_cd)
            ->and_where('page', '=', 'top')
            ->execute();
    }

    function delete_very_stay_control($bot_id) {
        return DB::delete('t_very_stay_control')
            ->where('bot_id', '=', $bot_id)
            ->execute();
    }

    function get_all_scene_lists($bot_id){
        $scenes = ORM::factory('botscene')
        ->where('bot_id', '=', $bot_id)
        ->order_by('sort_no')
        ->find_all();

        $scene_list = [];
		foreach($scenes as $scene) {
			$scene_list[$scene->scene_name] = $scene->label;
		}
        return $scene_list;
    }

    function get_scene_lists_without_data($bot_id){
        $all_scenes = $this->get_all_scene_lists($bot_id); // [scene_cd => label]
        $scenes_with_data = $this->fetch_scenes_with_setting($bot_id); // flat array

        $scene_list = [];
        foreach ($all_scenes as $scene_cd => $label) {
            if (!in_array($scene_cd, $scenes_with_data, true)) {
                $scene_list[$scene_cd] = $label;
            }
        }
        return $scene_list;
    }

    private function fetch_scenes_with_setting($bot_id){
        $sql = "SELECT DISTINCT scene_cd
            FROM t_very
            WHERE bot_id = :bot_id";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':bot_id' => $bot_id
        ]);

        $results = $query->execute()->as_array(null, 'scene_cd'); 
        return $results; 
    }
}