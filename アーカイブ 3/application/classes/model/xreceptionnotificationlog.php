<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Xreceptionnotificationlog extends ORM
{
	protected $_table_name = 'x_reception_notification_log';
	protected $_primary_key = 'log_id';

  public function write_log($params) {
    try {
      $result = $this->values($params)->create();
      return [is_null($result->log_id), 'Update error'];
    } catch (\Throwable $e) {
      return [false, $e->getMessage()];
    }
  }
}

?>