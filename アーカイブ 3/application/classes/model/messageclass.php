class FacebookMessage
{
    public $messaging_type;
    public $recipient;
    public $message;
}

class FacebookRecipent
{
    public $id;
}

class FacebookText
{
    public $text;
}

class FacebookAttatchment
{
    public $attachment;
}

class FacebookAttatchmentContent
{
    public $type;
    public $payload;
}

class FacebookPayloadImage
{
    public $url;
    public $is_reusable;
}

class FacebookPayloadTemplate
{
    public $template_type;
    public $elements;
}

class FacebookPayloadTemplateElement
{
    public $title;
    public $image_url;
    public $buttons;
}

class FacebookButton
{
    public $type;
    public $title;
    public $url;
}

class LineText
{
    public $type;
    public $text;
}

class LineImage
{
    public $type;
    public $originalContentUrl;
    public $previewImageUrl;
}

class LineTemplate
{
    public $type;
    public $altText;
    public $template;
}

class LineTemplateContent
{
    public $type;
    public $columns;
}

class LineColumn
{
    public $thumbnailImageUrl;
    public $imageBackgroundColor;
    public $title;
    public $text;
    public $defaultAction;
    public $actions;
}

class LineAction
{
    public $type;
    public $label;
    public $data;
    public $uri;
}

class WechatText
{
    public $touser;
    public $msgtype;
    public $text;
}

class WechatTextContent
{
    public $content;
}

class WechatImage
{
    public $touser;
    public $msgtype;
    public $image;
}

class WechatImageContent
{
    public $media_id;
}

class WechatNews
{
    public $touser;
    public $msgtype;
    public $news;
} 

class WechatNewsArticles
{
    public $articles;
}

class WechatArticle
{
    public $title;
    public $description;
    public $url;
    public $picurl;
}   