<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Supportmodel extends Model_Basemodel
{
    function get_translate_requests($bot_id=null) {
        $sql = "
        SELECT
        tr.bot_id,
        tb.bot_name,
        tr.source_lang,
        tr.item_id,
        (CASE
        WHEN tr.item_div = 0 THEN mbi.question
        WHEN tr.item_div IN (1, 2) THEN tid.item_name
        WHEN tr.item_div = 6 THEN tbm.msg_name
        END) AS name,
        tr.item_div,
        tr.source_status,
        tr.priority,
        tr.request_date,
        tr.deadline_date,
        tr.request_id,
        tro.lang_cd,
        tro.status,
        tbi.intent_cd,
        tbi.sub_intent_cd
        FROM
            t_translate_request tr
        LEFT JOIN
            t_bot tb ON tr.bot_id = tb.bot_id
        LEFT JOIN
            t_translate_operation tro ON tro.request_id = tr.request_id
        LEFT JOIN
            t_bot_intent tbi ON tr.item_id = tbi.intent_id AND tbi.bot_id = tr.bot_id AND tbi.lang_cd = tr.source_lang
        LEFT JOIN 
            m_bot_intent mbi ON mbi.intent_cd = tbi.intent_cd AND mbi.lang_cd = tbi.lang_cd AND mbi.sub_intent_cd=tbi.sub_intent_cd 
        LEFT JOIN 
            t_item_description tid ON (tr.item_div = 1 OR tr.item_div = 2) AND tid.item_id = tr.item_id AND tid.lang_cd = tr.source_lang
        LEFT JOIN 
            t_bot_msg tbm ON tr.item_div = 6 AND tbm.msg_id = tr.item_id AND tbm.bot_id = tr.bot_id
        ";
    if ($bot_id) {
        $sql .= " WHERE " . $this->_create_bot_cond_grp_only($bot_id, "tr.bot_id") . ";";
    }
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
        ':bot_id' => $bot_id,
    ));
    $results = $query->execute()->as_array();
    return $results;
    }

    function get_translate_information($request_id)
    {
        $sql = "
        SELECT
            tr.bot_id,
            tb.bot_name,
            tr.item_id,
                (CASE
                WHEN tr.item_div = 0 THEN mbi.question
                WHEN tr.item_div IN (1, 2) THEN tid.item_name
                WHEN tr.item_div = 6 THEN tbm.msg_name
                END) AS name,
            tr.item_div,
            tr.source_status,
            tr.source_lang,
            tr.priority,
            tr.deadline_date,
            tr.target_langs,
            tr.request_detail,
            tr.request_date,
            tu.user_id AS request_user_id,
            tu.name AS request_user_name
        FROM
            t_translate_operation tro
        LEFT JOIN
            t_translate_request tr ON tro.request_id = tr.request_id
        LEFT JOIN
            t_bot tb ON tr.bot_id = tb.bot_id
        LEFT JOIN
            t_bot_intent tbi ON tr.item_id = tbi.intent_id AND tbi.bot_id = tr.bot_id AND tbi.lang_cd = tr.source_lang
        LEFT JOIN 
            m_bot_intent mbi ON mbi.intent_cd = tbi.intent_cd AND mbi.lang_cd = tbi.lang_cd AND mbi.sub_intent_cd=tbi.sub_intent_cd 
        LEFT JOIN 
            t_item_description tid ON (tr.item_div = 1 OR tr.item_div = 2) AND tid.item_id = tr.item_id AND tid.lang_cd = tr.source_lang
        LEFT JOIN 
            t_bot_msg tbm ON tr.item_div = 6 AND tbm.msg_id = tr.item_id AND tbm.bot_id = tr.bot_id
        LEFT JOIN 
            t_user tu ON tr.request_user = tu.user_id
        WHERE
            tro.request_id = :request_id
        LIMIT 1
        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':request_id' => $request_id,
        ));
        $results = $query->execute()->as_array();
        if (!empty($results)) {
            return $results[0]; 
        }
        return [];
    }

    // 新翻訳管理画面：item_idごとに翻訳リクエストをすべて取得
    function get_translate_requests_by_item_id($item_id)
    {
        $sql = "
        SELECT
            tr.request_id,
            tr.bot_id,
            tb.bot_name,
            tr.item_id,
            (CASE
                WHEN tr.item_div = 0 THEN mbi.question
                WHEN tr.item_div IN (1, 2) THEN tid.item_name
                WHEN tr.item_div = 6 THEN tbm.msg_name
            END) AS name,
            tr.item_div,
            tr.source_status,
            tr.source_lang,
            tr.priority,
            tr.deadline_date,
            tr.target_langs,
            tr.request_detail,
            tr.request_date,
            tu.user_id AS request_user_id,
            tu.name AS request_user_name,
            tro.lang_cd,
            tro.status,
            tro.translate_user,
            tro.release_date,
            tro.notes
        FROM
            t_translate_request tr
        LEFT JOIN
            t_translate_operation tro ON tro.request_id = tr.request_id
        LEFT JOIN
            t_bot tb ON tr.bot_id = tb.bot_id
        LEFT JOIN
            t_bot_intent tbi ON tr.item_id = tbi.intent_id AND tbi.bot_id = tr.bot_id AND tbi.lang_cd = tr.source_lang
        LEFT JOIN 
            m_bot_intent mbi ON mbi.intent_cd = tbi.intent_cd AND mbi.lang_cd = tbi.lang_cd AND mbi.sub_intent_cd = tbi.sub_intent_cd 
        LEFT JOIN 
            t_item_description tid ON (tr.item_div = 1 OR tr.item_div = 2) AND tid.item_id = tr.item_id AND tid.lang_cd = tr.source_lang
        LEFT JOIN 
            t_bot_msg tbm ON tr.item_div = 6 AND tbm.msg_id = tr.item_id AND tbm.bot_id = tr.bot_id
        LEFT JOIN 
            t_user tu ON tr.request_user = tu.user_id
        WHERE
            tr.item_id = :item_id
        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':item_id' => $item_id,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    // 新翻訳管理画面：item_idごとに翻訳対応状況を全て取得
    function get_translate_operation_by_item_id($item_id)
    {
        $sql = "
        SELECT
            tro.lang_cd,
            tro.status,
            tro.translate_user,
            tro.release_date,
            tro.notes,
            tro.upd_date
        FROM
            t_translate_operation tro
        LEFT JOIN
            t_translate_request tr ON tro.request_id = tr.request_id
        WHERE 
            tr.item_id = :item_id
        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':item_id' => $item_id,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    function get_translate_operation($request_id)
    {
        $sql = "
        SELECT
            tro.lang_cd,
            tro.status,
            tro.translate_user,
            tro.release_date,
            tro.notes
        FROM
            t_translate_operation tro
        WHERE 
            tro.request_id = :request_id
        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':request_id' => $request_id,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    // Redmine API
    
    /**
     * Retrieves a Redmine issue by its ID.
     *
     * @param int $bot_id The ID of the bot.
     * @param int $issue_id The ID of the issue to retrieve.
     * @return array The decoded response containing the issue details.
     * @throws Exception If the issue is not found or does not belong to the specified project.
     */
    function get_redmine_issue($bot_id, $issue_id) {
        try {
            $config = $this->get_redmine_setting($bot_id);
            $project_id = $this->get_project_id($bot_id, $config['project_id']);
            $base_query = [
                'key' => $config['api_key']
            ];
            $_filter = [
                'include' => 'attachments,relations,journals'
            ];
            $query = http_build_query(array_merge($base_query, $_filter));
            $url = $config['url'] . 'issues/' . $issue_id . '.json?' . $query;
            $response = $this->curl_get($url);
            $decoded_response = json_decode($response, true);
            if (!isset($decoded_response['issue'])) {
                throw new Exception("Issue not found");
            }
            $service_categories = $this->get_redmine_custom_fields($bot_id, '14')['possible_values'];
            foreach ($decoded_response['issue']['custom_fields'] as &$field) {
                if ($field['id'] === 14 && $field['name'] === "サービス分類") {
                    foreach ($service_categories as $service_item) {
                        if ($service_item['value'] === $field['value']) {
                            $field['label'] = $service_item['label'];
                            break;
                        }
                    }
                    break;
                }
            }
            unset($field);
            
            // Check if the issue belongs to the specified project
            if ($decoded_response['issue']['project']['id'] != $project_id) {
                $parent_project_id = $this->get_parent_project_id($bot_id, $decoded_response['issue']['project']['id']);
                if (is_null($parent_project_id) || $parent_project_id != $project_id) {
                    throw new Exception("Issue does not belong to the specified project");
                }
            }
            return $decoded_response['issue'];
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "get_redmine_issue Exception:" . $th->getMessage());
            throw $th;
        }
    }

    function get_redmine_issue_status($issue_id) {
        $redmine_setting = $this->get_env('redmine');
		$api_key = $redmine_setting['api_key'];
		$base_url = $redmine_setting['url'];
		$url = $base_url . "issues/{$issue_id}.json";
		
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'X-Redmine-API-Key: ' . $api_key,
			'Content-Type: application/json'
		]);
		
		$response = curl_exec($ch);
		
		if (curl_errno($ch)) {
			error_log('Redmine API request failed: ' . curl_error($ch));
			curl_close($ch);
			return false;
		}
		
		curl_close($ch);
		
		return json_decode($response, true);
    }

    /**
     * Retrieves the project ID for a given bot ID and project identifier.
     *
     * @param int $bot_id The ID of the bot.
     * @param string $project_identifier The identifier of the project.
     * @return int The project ID.
     */
    function get_project_id($bot_id, $project_identifier) {
        $config = $this->get_redmine_setting($bot_id);
        $url = $config['url'] . 'projects/' . $project_identifier . '.json?key=' . $config['api_key'];
        $response = $this->curl_get($url);
        $decoded_response = json_decode($response, true);
        return $decoded_response['project']['id'];
    }

    /**
     * Retrieves the project's parent's ID for a given bot ID and project identifier.
     *
     * @param int $bot_id The ID of the bot.
     * @param string $project_identifier The identifier of the project.
     * @return int|null The project ID.
     */
    function get_parent_project_id($bot_id, $project_identifier) {
        $config = $this->get_redmine_setting($bot_id);
        $url = $config['url'] . 'projects/' . $project_identifier . '.json?key=' . $config['api_key'];
        $response = $this->curl_get($url);
        $decoded_response = json_decode($response, true);
        if (!isset($decoded_response['project']['parent'])) {
            return null;
        } else {
            return $decoded_response['project']['parent']['id'];
        }
    }

    function get_redmine_users_by_bot($bot_id) {
        $config = $this->get_redmine_setting($bot_id);
        $project_identifier = $config['project_id'];
        $project_id = $this->get_project_id($bot_id, $project_identifier);
        $query = http_build_query(['key' => $config['api_key']]);
        $url = $config['url'] . 'projects/' . $project_id . '/memberships.json?' . $query;
        $memberships = [];
        $offset = 0;
        $limit = 100; // redmine APIの1回のリクエストで取得できる上限
        do {
            $paginated_url = $url . '&offset=' . $offset . '&limit=' . $limit;
            $response = $this->curl_get($paginated_url);
            $decoded_response = json_decode($response, true);
            if (isset($decoded_response['memberships'])) {
                $memberships = array_merge($memberships, $decoded_response['memberships']);
                $offset += $limit;
            } else {
                break;
            }
        } while (count($decoded_response['memberships']) == $limit);
        return $memberships;
    }

    function get_redmine_custom_fields($bot_id, $custom_id) {
        $config = $this->get_redmine_setting($bot_id);
        $query = http_build_query(['key' => $config['api_key']]);
        $url = $config['url'] . 'custom_fields.json?' . $query;

        $custom_fields = [];
        $offset = 0;
        $limit = 100; // redmine APIの1回のリクエストで取得できる上限
        do {
            $paginated_url = $url . '&offset=' . $offset . '&limit=' . $limit;
            $response = $this->curl_get($paginated_url);
            $decoded_response = json_decode($response, true);
            if (isset($decoded_response['custom_fields'])) {
                $custom_fields = array_merge($custom_fields, $decoded_response['custom_fields']);
                $offset += $limit; // 次のページを指定するため、オフセットを増加させる
            } else {
                break;
            }
        } while (count($decoded_response['custom_fields']) == $limit); // 次のページがあるかチェック

        $filtered_fields = array_filter($custom_fields, function($field) use ($custom_id) {
            return $field['id'] == $custom_id;
        });
        $filtered_fields = array_values($filtered_fields);
        
        return $filtered_fields[0];
    }

    /**
     * Retrieves Redmine issues based on the provided parameters.
     *
     * @param int $bot_id The ID of the bot.
     * @param array $filter An optional array of filters to apply to the issues. e.g. ['status_id' => 1, 'assignee_email' => '<EMAIL>', 'tracker_id' => 1]
     * @param int $offset The offset for pagination.
     * @param int $limit The maximum number of issues to retrieve.
     * @return array An array containing the retrieved issues and additional information.
     * @throws \Throwable If an error occurs while retrieving the issues.
     */
    function get_redmine_issues($bot_id, $filter = [], $offset = 0, $limit = 100) {
        try {
            $config = $this->get_redmine_setting($bot_id);
            $base_query = ['project_id' => $config['project_id'], 'key' => $config['api_key'], 'offset' => $offset, 'limit' => $limit];
            $_filter = $filter;
            if (array_key_exists('assignee_email', $filter)) {
                $redmine_user = $this->get_redmine_user_by_email($bot_id, $filter['assignee_email']);
                if ($redmine_user === null) {
                    return ['issues' => [], 'total_count' => 0, 'offset' => $offset, 'limit' => $limit];
                }
                $_filter['assigned_to_id'] = $redmine_user['id'];
                unset($_filter['assignee_email']);
            }
            if (!array_key_exists('status_id', $filter)) {
                $_filter['status_id'] = '*';
            }
            $query = http_build_query(array_merge($base_query, $_filter, ['sort' => 'updated_on:desc']));
            $url = $config['url'] . 'issues.json?' . $query;
            $response = $this->curl_get($url);
            $decoded_response = json_decode($response, true); // ['issues' => [], 'total_count' => 0, 'offset' => 0, 'limit' => 0]
            $decoded_response['issues'] = $this->format_issues($decoded_response['issues']);
            return $decoded_response;
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "get_redmine_issues Exception:" . $th->getMessage());
            throw $th;
        }
    }

    /**
     * Formats the given array of issues into a specific format.
     *
     * @param array $issues The array of issues to be formatted.
     * @return array The formatted array of issues.
     */
    function format_issues($issues) {
        $formatted_issues = [];
        foreach ($issues as $issue) {
            $formatted_issue = [
                'id' => $issue['id'],
                'subject' => $issue['subject'],
                'status' => $issue['status']['name'],
                'tracker' => $issue['tracker']['name'],
                'priority' => $issue['priority']['name'],
                'assignee' => isset($issue['assigned_to']) ? $issue['assigned_to']['name'] : null,
                'assignee_id' => isset($issue['assigned_to']) ? $issue['assigned_to']['id'] : null,
                'start_date' => $issue['start_date'],
                'due_date' => $issue['due_date'],
                'created_on' => $issue['created_on'],
                'updated_on' => $issue['updated_on'],
                'author' => $issue['author']['name'],
            ];
            $formatted_issues[] = $formatted_issue;
        }
        return $formatted_issues;
    }

    /**
     * Retrieves the assignees for a given bot.
     *
     * @param int $bot_id The ID of the bot.
     * @return array An array of assignees, where each assignee is represented by an associative array with keys 'user_id', 'name', and 'email'.
     */
    function get_assignees($bot_id) {
        $sql = "SELECT user_id, name, email FROM t_user WHERE bot_id=:bot_id AND delete_flg = 0";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    /**
     * Retrieves the categories (trackers) from the Redmine API for a given bot.
     *
     * @param int $bot_id The ID of the bot.
     * @return array The array of categories (trackers).
     * @throws \Throwable If an error occurs while retrieving the categories.
     */
    function get_categories($bot_id) {
        try {
            $config = $this->get_redmine_setting($bot_id);
            $query = http_build_query(['key' => $config['api_key']]);
            $url = $config['url'] . 'trackers.json?' . $query;
            $response = $this->curl_get($url);
            $decoded_response = json_decode($response, true);
            $trackers = $decoded_response['trackers'];
            $formated_trackers = [];
            foreach ($trackers as $tracker) {
                $formated_trackers[] = [
                    'id' => $tracker['id'],
                    'name' => $tracker['name'],
                ];
            }
            return $formated_trackers;
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "get_trackers Exception:" . $th->getMessage());
            throw $th;
        }
    }

    /**
     * Checks if a user exists in Redmine based on their email address.
     *
     * @param int $bot_id The ID of the bot.
     * @param string $email The email address of the user to check.
     * @return bool Returns true if the user exists in Redmine, false otherwise.
     */
    function check_if_redmineuser($bot_id, $email) {
        try {
            $config = $this->get_redmine_setting($bot_id);
            $query = http_build_query([
                'name' => $email,
                'key' => $config['api_key']
            ]);
            $url = $config['url'] . 'users.json?' . $query;
            $response = $this->curl_get($url);
            $decoded_res = json_decode($response, true);
            if (!$decoded_res) {
                return false;
            }
            if (array_key_exists('total_count', $decoded_res) && $decoded_res['total_count'] > 0) {
                return true;
            }
            return false;
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "check_if_redmineuser Exception:" . $th->getMessage());
            return false;
        }
    }

    /**
     * Retrieves a Redmine user by email.
     *
     * @param int $bot_id The ID of the bot.
     * @param string $email The email address of the user.
     * @return array|null The user data as an associative array, or null if no user is found.
     */
    function get_redmine_user_by_email($bot_id, $email) {
        try {
            $config = $this->get_redmine_setting($bot_id);
            $query = http_build_query([
                'name' => $email,
                'key' => $config['api_key']
            ]);
            $url = $config['url'] . 'users.json?' . $query;
            $response = $this->curl_get($url);
            $decoded_res = json_decode($response, true);
            if (array_key_exists('users', $decoded_res) && count($decoded_res['users']) > 0) {
                return $decoded_res['users'][0];
            }
            return null;
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "get_redmine_user_by_email Exception:" . $th->getMessage());
            $this->notify_slack_error(
                "[detail] Failed to get Redmine user",
                $th->getMessage(),
                null,
                $bot_id,
                $email
            );
            return null;
        }
    }

    function get_redmine_user_apikey_by_email($bot_id, $email) {
        try {
            $config = $this->get_redmine_setting($bot_id);
            $query = http_build_query([
                'name' => $email,
                'key' => $config['api_key']
            ]);
            $url1 = $config['url'] . 'users.json?' . $query;
            $response1 = $this->curl_get($url1);
            $decoded_res1 = json_decode($response1, true);
            if (array_key_exists('users', $decoded_res1) && count($decoded_res1['users']) > 0) {
                $current_user = $decoded_res1['users'][0];
                $url2 = $config['url'] . 'users/' . $current_user['id'] . '.json?' . $query;
                $response2 = $this->curl_get($url2);
                $decoded_res2 = json_decode($response2, true);
                if (array_key_exists('user', $decoded_res2)) {
                    return $decoded_res2['user']['api_key'];
                }
            } 
            return $config['api_key'];
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "get_redmine_user_apikey Exception:" . $th->getMessage());
            return null;
        }
    }

    /**
     * Retrieves the Redmine settings for a specific bot.
     *
     * @param int $bot_id The ID of the bot.
     * @return array The Redmine settings for the bot.
     * @throws Exception If the API key or project ID is missing.
     */
    function get_redmine_setting($bot_id) {
        $config = [];
        try {
            $config = $this->get_env('redmine');
            $bot_config = $this->get_bot_setting($bot_id, 'json_redmine', true);
            if (!$bot_config) {
                throw new Exception("no json_redmine");
            }
            if (array_key_exists('project_id', $bot_config)) {
                $config['project_id'] = $bot_config['project_id'];
            }
            if (!$config['api_key']) {
                throw new Exception("no api_key");
            }
            if (!$config['project_id']) {
                throw new Exception('no project_id');
            }
            return $config;
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "CURL_GET Exception:" . $th->getMessage());
            throw $th;
        }
    }

    // redmine連携が設定されているか確認
    function check_redmine_project_id($bot_id) {
        $bot_config = $this->get_bot_setting($bot_id, 'json_redmine', true);
        if ($bot_config && array_key_exists('project_id', $bot_config)) {
            return true;
        }
        return false;
    }

    private function notify_slack_error($message, $error, $request_data = null, $bot_id = null, $user_email = null) {
        // サポート機能のエラーをSlackに通知
        try {
            $redmine_settings = $this->get_bot_tpl_message(0, 'redmine_settings', 'ja');
            $webhook_url = json_decode($redmine_settings)->error_notify_slack_webhook_url;
            if (empty($webhook_url)) {  
                return;
            }
            // バックトレースからエラー発生場所の情報を取得
            $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
            $caller = isset($trace[1]) ? $trace[1] : null;
            $error_location = '';
            if ($caller) {
                $error_location = sprintf(
                    "発生場所: %s:%d (%s)",
                    basename($caller['file'] ?? 'unknown'),
                    $caller['line'] ?? 0,
                    $caller['function'] ?? 'unknown'
                );
            }
            $data = [
                'text' => "サポート機能でエラーが発生\n" .
                        "メッセージ: " . $message . "\n" .
                        "エラー: " . $error . "\n" .
                        ($bot_id ? "ボットID: " . $bot_id . "\n" : "") .
                        ($user_email ? "ユーザー: " . $user_email . "\n" : "") .
                        ($request_data ? "リクエスト内容: " . json_encode($request_data, JSON_UNESCAPED_UNICODE) . "\n" : "") .
                        $error_location
            ];
            $ch = curl_init($webhook_url);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_exec($ch);
            curl_close($ch);
        } catch (\Throwable $th) {
            Log::instance()->add(Log::ERROR, "Slack notification failed: " . $th->getMessage());
        }
    }

    function create_redmine_issue($bot_id, $user_email, $issue_attributes, $apikey) {
        try {
            $redmine_config = $this->get_redmine_setting($bot_id);
            $redmine_user = $this->get_redmine_user_by_email($bot_id, $user_email);
            if ($redmine_user === null) {
                throw new Exception("User not found in Redmine");
            }
            $query = http_build_query(['key' => $apikey]);
            $url = $redmine_config['url'] . 'issues.json?' . $query;
            $request_data = [
                'issue' => $issue_attributes
            ];
            $request_data['issue']['project_id'] = $redmine_config['project_id'];
            $header = [
                'Content-Type: application/json'
            ];
            $response = $this->curl_post($url, $header, $request_data);
            $decoded_res = json_decode($response, true);
            if (array_key_exists('issue', $decoded_res)) {
                return $decoded_res['issue'];
            }
            throw new Exception("Failed to create issue: " . $response);
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "create_redmine_issue Exception:" . $th->getMessage());
            $this->notify_slack_error(
                "Failed to create Redmine issue",
                $th->getMessage(),
                $issue_attributes,
                $bot_id,
                $user_email
            );
            throw $th;
        }
    }

    function update_redmine_issue($bot_id, $user_email, $issue_id, $issue_attributes, $apikey) {
        try {
            $redmine_config = $this->get_redmine_setting($bot_id);
            $redmine_user = $this->get_redmine_user_by_email($bot_id, $user_email);
            if ($redmine_user === null) {
                throw new Exception("User not found in Redmine");
            }
            $query = http_build_query(['key' => $apikey]);
            $url = $redmine_config['url'] . 'issues/' . $issue_id . '.json?' . $query;
            $request_data = [
                'issue' => $issue_attributes
            ];
            if (isset($issue_attributes['notes'])) {
                $request_data['issue']['notes'] = $issue_attributes['notes'];
            }
            $header = [
                'Content-Type: application/json'
            ];
            $response = $this->curl_put($url, $header, $request_data);
            $decoded_res = json_decode($response, true);
            if (isset($decoded_res['issue'])) {
                return $decoded_res['issue'];
            } elseif (empty($decoded_res) && $response === '') {
                return ['success' => true];
            }
            throw new Exception("Failed to update issue: " . $response);
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "update_redmine_issue Exception:" . $th->getMessage());
            $this->notify_slack_error(
                "Failed to update Redmine issue #{$issue_id}",
                $th->getMessage(),
                $issue_attributes,
                $bot_id,
                $user_email
            );
            throw $th;
        }
    }

    function upload_attachment($file, $bot_id, $apikey) {
        try {
            $fileTmpPath = $file['tmp_name'];
            $fileName = $file['name'];
            $fileSize = $file['size'];
            // check size?
            $fileType = $file['type'];
            $redmine_config = $this->get_redmine_setting($bot_id);
            $query = http_build_query(['key' => $apikey, 'filename' => $fileName, 'content_type' => $fileType]);
            $url = $redmine_config['url'] . 'uploads.json?' . $query;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/octet-stream',
            ]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, file_get_contents($fileTmpPath));
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception(curl_error($ch));
            }
            $responseData = json_decode($response, true);
            if (array_key_exists('upload', $responseData)) {
                $upload = $responseData['upload'];
                $upload['filename'] = $fileName;
                $upload['content_type'] = $fileType;
                return $upload;
            } else {
                throw new Exception("Failed to upload attachment");
            }
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "upload_attachment Exception:" . $th->getMessage());
            throw $th;
        }
    }

    function delete_attachment($token, $bot_id, $apikey) {
        try {
            $redmine_config = $this->get_redmine_setting($bot_id);
            $query = http_build_query(['key' => $apikey]);
            $url = $redmine_config['url'] . 'attachments/' . $token . '.json?' . $query;
            $header = [
                'Content-Type: application/json'
            ];
            $response = $this->curl_delete($url, $header);
            return $response;
        } catch (\Throwable $th) {
            Log::instance()->add(Log::INFO, "delete_attachment Exception:" . $th->getMessage());
            throw $th;
        }
    }

    function redmine_status_list() {
        return [
            ['code' => 1, 'name' => '未対応'],
            ['code' => 2, 'name' => '処理中'],
            ['code' => 3, 'name' => '処理済み'],
            ['code' => 4, 'name' => 'ペンディング'],
            ['code' => 5, 'name' => '完了'],
            ['code' => 6, 'name' => '却下'],
        ];
    }

    public function get_default_facility_cd_by_redmine_issue_id($issue_id, $bot_id) {
        try {
            // Redmineのissueデータからプロジェクトidを取得
            $config = $this->get_redmine_setting($bot_id);
            $url = $config['url'] . 'issues/' . $issue_id . '.json?key=' . $config['api_key'];
            $response = $this->curl_get($url);
            $issue_data = json_decode($response, true);
            if (!isset($issue_data['issue']['project']['id'])) {
                throw new Exception('Project ID not found in issue data');
            }
            $project_id = $issue_data['issue']['project']['id'];
            // プロジェクトidをもとに、プロジェクトの識別子を取得する
            $url = $config['url'] . 'projects/' . $project_id . '.json?key=' . $config['api_key'];
            $response = $this->curl_get($url);
            Log::instance()->add(Log::DEBUG, "API Response: " . $response);
            $project_data = json_decode($response, true);
            if ($project_data === null && json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON in API response');
            }
            if (!isset($project_data['project']['identifier'])) {
                throw new Exception('Project identifier not found in project data');
            }
            $project_identifier = $project_data['project']['identifier'];
            // プロジェクトの識別子をもとに、t_bot_settingのsetting_cdがjson_redmineのレコードを取得
            $query = "
                SELECT bot_id, setting_value
                FROM t_bot_setting
                WHERE setting_cd = 'json_redmine'
                AND setting_value IS NOT NULL
                AND setting_value != ''
                AND JSON_VALID(setting_value)
            ";
            $result = DB::query(Database::SELECT, $query)
                ->execute()
                ->as_array();
            if (empty($result)) {
                throw new Exception('No valid bot settings found');
            }
            $matching_bot_id = null;
            foreach ($result as $row) {
                $setting_value = json_decode($row['setting_value'], true);
                if (isset($setting_value['project_id']) && $setting_value['project_id'] === $project_identifier) {
                    $matching_bot_id = $row['bot_id'];
                    break;
                }
            }
            if ($matching_bot_id === null) {
                Log::instance()->add(Log::ERROR, "No matching bot_id found for project_identifier: " . $project_identifier);
                throw new Exception('Bot ID not found for project identifier');
            }
            // matching_bot_idをもとに、facility_cdを取得する
            $bot = ORM::factory('Bot', $matching_bot_id);
            if (!$bot->loaded()) {
                throw new Exception('Bot not found');
            }
            Log::instance()->add(Log::DEBUG, "Found matching bot_id: " . $matching_bot_id);
            return $bot->facility_cd;
        } catch (Exception $e) {
            Log::instance()->add(Log::ERROR, "get_default_facility_cd Exception: " . $e->getMessage() . " at issue_id: " . $issue_id . ", bot_id: " . $bot_id);
            return null;
        }
    }

    // redmine自社担当者の設定
    public function get_company_staff_custom_field($bot_id) {
		$bot_redmine_setting = json_decode($this->get_bot_setting($bot_id, 'json_redmine'), true);
		if ($bot_redmine_setting && isset($bot_redmine_setting['cs_sale_contents_assigned_to_id'])) {
			$company_staff_id = $bot_redmine_setting['cs_sale_contents_assigned_to_id'];
			$redmine_users = $this->get_redmine_users_by_bot($bot_id);
			foreach ($redmine_users as $user) {
				if (isset($user['user']['id']) && $user['user']['id'] == $company_staff_id) {
					$company_staff_name = str_replace(' ', '', $user['user']['name']);
					return [
						'id' => 15,
						'value' => $company_staff_name
					];
				}
			}
		}
		return null;
	}
    
    public function get_notice_view_data($start_ticket_id, $end_ticket_id) {
        // 管理画面の全ユーザー取得（社内ユーザー、解約施設を除く）
        $users_query = "
            SELECT tu.bot_id, b.bot_name, tu.user_id, tu.name AS user_name
            FROM t_user tu
            JOIN t_bot b ON b.bot_id = tu.bot_id
            WHERE tu.role_cd NOT IN ('99', '80')
            AND tu.email NOT LIKE '%@activalues.com'
            AND b.bot_status_cd = '04'
            AND b.end_date IS NULL
            AND tu.bot_id != 0
            ORDER BY tu.bot_id ASC, tu.user_id ASC
        ";
        $all_tickets = array_map('strval', range($start_ticket_id, $end_ticket_id));
        $users = DB::query(Database::SELECT, $users_query)->execute()->as_array();
        // すべてのお知らせを取得
        $tickets_query = "SELECT DISTINCT ticket_id FROM t_ticket ORDER BY ticket_id ASC";
        $tickets = DB::query(Database::SELECT, $tickets_query)
            ->param(':start_ticket_id', $start_ticket_id)
            ->param(':end_ticket_id', $end_ticket_id)
            ->execute()->as_array();
        // すべてのお知らせの既読情報を取得
        $replies_query = "SELECT ticket_id, content FROM t_ticket_reply";
        $replies = DB::query(Database::SELECT, $replies_query)
            ->param(':start_ticket_id', $start_ticket_id)
            ->param(':end_ticket_id', $end_ticket_id)
            ->execute()->as_array();
        // コンマ区切りの既読ユーザーのIDを取得
        $ticket_views = [];
        foreach ($replies as $reply) {
            $user_ids = explode(',', $reply['content']);
            foreach ($user_ids as $user_id) {
                $ticket_views[$reply['ticket_id']][$user_id] = 1;
            }
        }
        // データを整形
        $user_data = [];
        foreach ($users as $user) {
            $user_key = $user['bot_id'] . '_' . $user['user_id'];
            $user_data[$user_key] = [
                'bot_id' => $user['bot_id'],
                'bot_name' => $user['bot_name'],
                'user_name' => $user['user_name'],
                'tickets' => array_fill_keys($all_tickets, [])
            ];
            foreach ($tickets as $ticket) {
                $ticket_id = $ticket['ticket_id'];
                $viewed = isset($ticket_views[$ticket_id][$user['user_id']]) ? [1] : [];
                $user_data[$user_key]['tickets'][$ticket_id] = $viewed;
            }
        }
        // 施設（bot）ごとの合計値を算出
        $bot_totals = [];
        foreach ($user_data as $user_key => $data) {
            $bot_id = $data['bot_id'];
            if (!isset($bot_totals[$bot_id])) {
                $bot_totals[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $data['bot_name'],
                    'user_name' => 'total',
                    'tickets' => array_fill_keys($all_tickets, [])
                ];
                foreach ($tickets as $ticket) {
                    $ticket_id = $ticket['ticket_id'];
                    $viewed = isset($ticket_views[$ticket_id][$user['user_id']]) ? [1] : [];
                    $user_data[$user_key]['tickets'][$ticket_id] = $viewed;
                }
            }
            foreach ($data['tickets'] as $ticket => $actions) {
                foreach ($actions as $action => $count) {
                    $bot_totals[$bot_id]['tickets'][$ticket][$action] += $count;
                }
            }
        }
        return ['user_data' => $user_data, 'bot_totals' => $bot_totals];
    }
    
    public function get_notice_ids($start_ticket_id = null, $end_ticket_id = null) {
        $ticket_query = "
            SELECT DISTINCT ticket_id
            FROM t_ticket
        ";
        // 範囲指定
        if ($start_ticket_id !== null && $end_ticket_id !== null) {
            $ticket_query .= " WHERE ticket_id BETWEEN :start_ticket_id AND :end_ticket_id";
        }
        $ticket_query .= " ORDER BY ticket_id ASC";
        $query = DB::query(Database::SELECT, $ticket_query);
        if ($start_ticket_id !== null && $end_ticket_id !== null) {
            $query->param(':start_ticket_id', $start_ticket_id)
                  ->param(':end_ticket_id', $end_ticket_id);
        }
        $tickets = $query->execute()->as_array();
        $ticket_ids = array_map(function($ticket) {
            return $ticket['ticket_id'];
        }, $tickets);
        return $ticket_ids;
    }

    public function get_latest_notice_ids($limit = 5) {
        $ticket_query = "
            SELECT ticket_id
            FROM t_ticket
            ORDER BY ticket_id DESC
            LIMIT :limit
        ";
        $query = DB::query(Database::SELECT, $ticket_query)
            ->param(':limit', $limit);
        $tickets = $query->execute()->as_array();
        $ticket_ids = array_map(function($ticket) {
            return $ticket['ticket_id'];
        }, $tickets);
        return $ticket_ids;
    }
    public function get_mgt_access_data($start_date, $end_date, $type, $bot_id = null) {
        // 管理画面の全ユーザー取得（社内ユーザー、解約施設を除く）
        $active_external_users = $this->get_active_external_users($bot_id);
        // 全ての月のリストを作成
        $all_months = $this->get_all_months($start_date, $end_date);
    
        // タイプに基づいてWHERE条件とアクションタイプを設定
        switch ($type) {
            case 'statistics_view_count':
                $action_types = [
                    'talkreport1', 'accessreport', 'faqlogword', 'faqlogwordselect', 
                    'faqlogcate', 'faqlogfaq', 'faqlogexpand', 'faqlogexpandrelation', 
                    'faqlogsurvey', 'faqlogfollow', 'faqreport1', 'faqreport2', 
                    'faqlog', 'veryreport', 'report', 'report3'
                ];
                $where_condition = "AND t.action IN ('" . implode("','", $action_types) . "')";
                break;
            case 'faq_add_count':
                $action_types = ['talknew'];
                $where_condition = "AND t.action = 'talknew' AND JSON_VALID(t.content) AND JSON_UNQUOTE(JSON_EXTRACT(t.content, '$.action')) = 'add'";
                break;
            case 'faq_modify_count':
                $action_types = ['talknew'];
                $where_condition = "AND t.action = 'talknew' AND JSON_VALID(t.content) AND JSON_UNQUOTE(JSON_EXTRACT(t.content, '$.action')) = 'modify'";
                break;
            case 'verytop_save_count':
                $action_types = ['verytop'];
                $where_condition = "AND JSON_VALID(t.content) AND JSON_EXTRACT(t.content, '$.page_action') = 'save' AND t.action = 'verytop'";
                break;
            case 'all_access_count':
                $action_types = [];
                $where_condition = "";
                break;
            default:
                throw new InvalidArgumentException("Unknown type: $type");
        }
        $bot_condition = "";
        if ($bot_id !== null) {
            $bot_condition = "AND b.bot_id = :bot_id";
        }
        $main_query = "
            SELECT 
                b.bot_id, 
                DATE_FORMAT(t.access_time, '%Y-%m') AS month, 
                u.user_id, 
                COUNT(DISTINCT DATE_FORMAT(t.access_time, '%Y-%m-%d')) AS count 
            FROM 
                t_user_access t
                INNER JOIN t_user u ON u.user_id = t.user_id
                INNER JOIN t_bot b ON b.bot_id = t.bot_id
            WHERE 
                b.bot_status_cd = '04'
                AND b.end_date IS NULL
                AND u.role_cd NOT IN ('99', '80')
                AND u.email NOT LIKE '%@activalues.com'
                AND t.access_time BETWEEN :start_date AND :end_date
                AND b.bot_id <> 0
                {$where_condition}
                {$bot_condition}
            GROUP BY 
                DATE_FORMAT(t.access_time, '%Y-%m'), 
                b.bot_id, 
                u.user_id
            ORDER BY 
                b.bot_id, 
                month, 
                u.user_id
        ";
        $query = DB::query(Database::SELECT, $main_query)
            ->param(':start_date', $start_date)
            ->param(':end_date', $end_date);
        if ($bot_id !== null) {
            $query->param(':bot_id', $bot_id);
        }
        $raw_data = $query->execute()->as_array();
        // データ整形
        $user_data = [];
        foreach ($active_external_users as $user) {
            $user_key = $user['bot_id'] . '_' . $user['user_id'];
            $user_data[$user_key] = [
                'bot_id' => $user['bot_id'],
                'bot_name' => $user['bot_name'],
                'user_name' => $user['user_name'],
                'months' => array_fill_keys($all_months, [])
            ];
            foreach ($all_months as $month) {
                $user_data[$user_key]['months'][$month] = array_fill_keys($action_types, '0');
            }
        }
        // 実際のデータを反映
        foreach ($raw_data as $row) {
            $user_key = $row['bot_id'] . '_' . $row['user_id'];
            if (isset($user_data[$user_key])) {
                $user_data[$user_key]['months'][$row['month']][$row['action']] = $row['count'];
            }
        }
        // 施設（bot）ごとの合計値を算出
        $bot_totals = [];
        foreach ($user_data as $user_key => $data) {
            $bot_id = $data['bot_id'];
            if (!isset($bot_totals[$bot_id])) {
                $bot_totals[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $data['bot_name'],
                    'user_name' => 'total',
                    'months' => array_fill_keys($all_months, [])
                ];
                foreach ($all_months as $month) {
                    $bot_totals[$bot_id]['months'][$month] = array_fill_keys($action_types, '0');
                }
            }
            foreach ($data['months'] as $month => $actions) {
                foreach ($actions as $action => $count) {
                    $bot_totals[$bot_id]['months'][$month][$action] += $count;
                }
            }
        }
        return ['user_data' => $user_data, 'bot_totals' => $bot_totals];
    }

    // 全ての月のリストを作成
    public function get_all_months($start_date, $end_date) {
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $end->modify('first day of next month');
        $interval = new DateInterval('P1M');
        $period = new DatePeriod($start, $interval, $end);
        $months = [];
        foreach ($period as $dt) {
            $months[] = $dt->format('Y-m');
        }
        return $months;
    }

    public function get_verybotuse_data($start_date, $end_date) {
        $main_query = "
            SELECT
                tb.bot_id,
                tb.bot_name,
                DATE_FORMAT(tbl.log_time, '%Y-%m') AS month,
                tu.user_id,
                tu.name AS user_name,
                COUNT(*) AS count
            FROM
                t_bot_log_800 tbl
                INNER JOIN t_bot_member tbm ON tbl.member_id = tbm.member_id
                INNER JOIN t_user tu ON tbm.link_id = tu.user_id
                INNER JOIN t_bot tb ON tu.bot_id = tb.bot_id
            WHERE
                tb.bot_status_cd = '04'
                AND tb.end_date IS NULL
                AND tu.role_cd NOT IN ('99', '80')
                AND tbl.log_time BETWEEN :start_date AND :end_date
            GROUP BY
                DATE_FORMAT(tbl.log_time, '%Y-%m'),
                tb.bot_id,
                tu.user_id,
                tu.role_cd
            ORDER BY
                tb.bot_id,
                DATE_FORMAT(tbl.log_time, '%Y-%m'),
                tu.name
        ";
        $query = DB::query(Database::SELECT, $main_query)
            ->param(':start_date', $start_date)
            ->param(':end_date', $end_date);
        $raw_data = $query->execute()->as_array();
        // アクティブな外部ユーザーを取得
        $active_external_users = $this->get_active_external_users();
        // 全ての月のリストを作成
        $all_months = $this->get_all_months($start_date, $end_date);
        foreach ($active_external_users as $user) {
            $user_key = $user['bot_id'] . '_' . $user['user_id'];
            $grouped_data[$user_key] = [
                'bot_id' => $user['bot_id'],
                'bot_name' => $user['bot_name'],
                'user_name' => $user['user_name'],
                'months' => array_fill_keys($all_months, '0')
            ];
        }
        // 実際のデータを当てはめる
        foreach ($raw_data as $row) { 
            $user_key = $row['bot_id'] . '_' . $row['user_id']; 
            if (isset($grouped_data[$user_key])) {
                $grouped_data[$user_key]['months'][$row['month']] = $row['count'];
            }
        }
        return $grouped_data;
    }

    private function get_active_external_users($bot_id = null) {
        $users_query = "
            SELECT tu.bot_id, b.bot_name, tu.user_id, tu.name AS user_name
            FROM t_user tu
            JOIN t_bot b ON b.bot_id = tu.bot_id
            WHERE tu.role_cd NOT IN ('99', '80')
            AND tu.email NOT LIKE '%@activalues.com'
            AND b.bot_status_cd = '04'
            AND b.end_date IS NULL
            AND tu.bot_id != 0
        ";
        if ($bot_id !== null) {
            $users_query .= " AND b.bot_id = :bot_id";
        }
        $users_query .= " ORDER BY tu.bot_id ASC, tu.user_id ASC";
        $query = DB::query(Database::SELECT, $users_query);
        if ($bot_id !== null) {
            $query->param(':bot_id', $bot_id);
        }
        return $query->execute()->as_array();
    }

    public function get_active_bots() {
        $sql = "SELECT bot_id, bot_name FROM t_bot WHERE bot_status_cd = '04' AND end_date IS NULL AND bot_id != 0 ORDER BY bot_id ASC";
        $query = DB::query(Database::SELECT, $sql);
        $results = $query->execute()->as_array();
        $bots = [];
        foreach ($results as $result) {
            $bots[$result['bot_id']] = $result['bot_name'];
        }
        return $bots;
    }

    public function get_chatbot_report_data($start_date, $end_date, $active_bots, $type='monthly', $metric='new_member') {
        $result = [];
        // 取得するカラムを指定
        $column = ($metric === 'new_conversation') ? 'new_conversation' : 'new_member';
        // すべての月リストを作成
        $all_months = $this->get_all_months($start_date, $end_date);
        foreach ($active_bots as $bot_id => $bot_name) {
            $months = array_fill_keys($all_months, 0); // すべての月を0で初期化
            try {
                $report_table = 'r_bot_report_' . $bot_id;
                $exists = DB::query(Database::SELECT, "SHOW TABLES LIKE '{$report_table}'")->execute('slave')->count() > 0;
                if (!$exists) {
                    // ボット自身のテーブルが存在しない場合、グループボットIDのテーブルを試す
                    $grp_bot_id = $this->get_grp_bot_id($bot_id);
                    if ($grp_bot_id > 0) {
                        $report_table = 'r_bot_report_' . $grp_bot_id;
                        $exists = DB::query(Database::SELECT, "SHOW TABLES LIKE '{$report_table}'")->execute('slave')->count() > 0;
                        if (!$exists) {
                            // グループのテーブルも存在しない場合は0のまま
                            $result[$bot_id] = [
                                'bot_id' => $bot_id,
                                'bot_name' => $bot_name,
                                'months' => $months
                            ];
                            continue;
                        }
                    } else {
                        // グループに属さず、個別のテーブルも存在しない場合も0のまま
                        $result[$bot_id] = [
                            'bot_id' => $bot_id,
                            'bot_name' => $bot_name,
                            'months' => $months
                        ];
                        continue;
                    }
                }
                $sql = "SELECT report_date as date, {$column} 
                       FROM {$report_table} 
                       WHERE report_date BETWEEN :start_date AND :end_date 
                       AND bot_id = :bot_id
                       AND scene_cd = 'all' 
                       AND lang_cd = 'all' 
                       AND sns_type_cd = 'all' 
                       AND report_div = 3 
                       ORDER BY report_date ASC";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters([
                    ':start_date' => $start_date,
                    ':end_date' => $end_date,
                    ':bot_id' => $bot_id
                ]);
                $bot_data = $query->execute('slave')->as_array();
                if (!empty($bot_data)) {
                    foreach ($bot_data as $data) {
                        $month = date('Y-m', strtotime($data['date']));
                        $months[$month] = $data[$column];
                    }
                }
                // レコードがなくても0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
            } catch (Database_Exception $e) {
                // 例外時も0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
                continue;
            }
        }
        return $result;
    }

    /**
     * INQUIRYのレポートデータを取得する
     * @param string $start_date 開始日
     * @param string $end_date 終了日  
     * @param array $active_bots アクティブなボットID、名前の連想配列
     * @param string $type 今後、日次データ取得のために'daily'を追加する可能性あり
     * @param string $metric cvは回答数。今後、pv数などの指標も追加する可能性あり
     * @return array ボットごとの月別集計データ
     */
    public function get_inquiry_report_data($start_date, $end_date, $active_bots, $type='monthly', $metric='cv') {
        $result = [];
        $all_months = $this->get_all_months($start_date, $end_date);
        foreach ($active_bots as $bot_id => $bot_name) {
            $months = array_fill_keys($all_months, 0); // すべての月を0で初期化
            try {
                $sql = "SELECT DATE_FORMAT(report_date, '%Y-%m') as month, SUM(count) as inquiry_answer 
                       FROM r_inquiry_report 
                       WHERE report_date BETWEEN :start_date AND :end_date 
                       AND bot_id = :bot_id
                       AND report_type = 'cv'
                       GROUP BY DATE_FORMAT(report_date, '%Y-%m')
                       ORDER BY month ASC";

                $query = DB::query(Database::SELECT, $sql);
                $query->parameters([
                    ':start_date' => $start_date,
                    ':end_date' => $end_date,
                    ':bot_id' => $bot_id
                ]);

                $bot_data = $query->execute('slave')->as_array();
                if (!empty($bot_data)) {
                    foreach ($bot_data as $data) {
                        $months[$data['month']] = $data['inquiry_answer'];
                    }
                }
                // レコードがなくても0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
            } catch (Database_Exception $e) {
                // 例外時も0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
                continue;
            }
        }
        return $result;
    }

    /**
     * SURVEYのレポートデータを取得する
     * @param string $start_date 開始日
     * @param string $end_date 終了日  
     * @param array $active_bots アクティブなボットID、名前の連想配列
     * @param string $type 今後、日次データ取得のために'daily'を追加する可能性あり
     * @param string $metric cvは回答数。今後、pv数などの指標も追加する可能性あり
     * @return array ボットごとの月別集計データ
     */
    public function get_survey_report_data($start_date, $end_date, $active_bots, $type='monthly', $metric='cv') {
        $result = [];
        $all_months = $this->get_all_months($start_date, $end_date);
        foreach ($active_bots as $bot_id => $bot_name) {
            $months = array_fill_keys($all_months, 0); // すべての月を0で初期化
            try {
                $sql = "SELECT DATE_FORMAT(report_date, '%Y-%m') as month, SUM(count) as survey_answer 
                       FROM r_survey_report 
                       WHERE report_date BETWEEN :start_date AND :end_date 
                       AND bot_id = :bot_id
                       AND report_type = 'cv'
                       GROUP BY DATE_FORMAT(report_date, '%Y-%m')
                       ORDER BY month ASC";

                $query = DB::query(Database::SELECT, $sql);
                $query->parameters([
                    ':start_date' => $start_date,
                    ':end_date' => $end_date,
                    ':bot_id' => $bot_id
                ]);

                $bot_data = $query->execute('slave')->as_array();
                if (!empty($bot_data)) {
                    foreach ($bot_data as $data) {
                        $months[$data['month']] = $data['survey_answer'];
                    }
                }
                // レコードがなくても0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
            } catch (Database_Exception $e) {
                // 例外時も0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
                continue;
            }
        }
        return $result;
    }

    /**
     * VERYBOTのレポートデータを取得する
     * @param string $start_date 開始日
     * @param string $end_date 終了日  
     * @param array $active_bots アクティブなボットID、名前の連想配列
     * @param string $type 今後、日次データ取得のために'daily'などを追加する可能性あり
     * @param string $metric new_memberは新規会員数。今後、repeat_memberなどの指標も追加する可能性あり
     * @return array ボットごとの月別集計データ
     */
    public function get_very_report_data($start_date, $end_date, $active_bots, $type='monthly', $metric='new_member') {
        $result = [];
        // 取得するカラムを指定
        $column = ($metric === 'repeat_member') ? 'repeat_member' : 'new_member';
        $all_months = $this->get_all_months($start_date, $end_date);

        foreach ($active_bots as $bot_id => $bot_name) {
            $months = array_fill_keys($all_months, 0); // すべての月を0で初期化
            try {
                // レポートテーブルを確認
                $report_table = 'r_very_report_' . $bot_id;
                $exists = DB::query(Database::SELECT, "SHOW TABLES LIKE '{$report_table}'")->execute('slave')->count() > 0;
                if (!$exists) {
                    // ボット自身のテーブルが存在しない場合、グループボットIDのテーブルを試す
                    $grp_bot_id = $this->get_grp_bot_id($bot_id);
                    if ($grp_bot_id > 0) {
                        $report_table = 'r_very_report_' . $grp_bot_id;
                        $exists = DB::query(Database::SELECT, "SHOW TABLES LIKE '{$report_table}'")->execute('slave')->count() > 0;
                        if (!$exists) {
                            // グループのテーブルも存在しない場合は0のまま
                            $result[$bot_id] = [
                                'bot_id' => $bot_id,
                                'bot_name' => $bot_name,
                                'months' => $months
                            ];
                            continue;
                        }
                    } else {
                        // グループに属さず、個別のテーブルも存在しない場合も0のまま
                        $result[$bot_id] = [
                            'bot_id' => $bot_id,
                            'bot_name' => $bot_name,
                            'months' => $months
                        ];
                        continue;
                    }
                }

                $sql = "SELECT report_date as date, {$column} 
                       FROM {$report_table} 
                       WHERE report_date BETWEEN :start_date AND :end_date 
                       AND bot_id = :bot_id
                       AND scene_cd = 'all' 
                       AND lang_cd = 'all' 
                       AND report_div = 3 
                       ORDER BY report_date ASC";

                $query = DB::query(Database::SELECT, $sql);
                $query->parameters([
                    ':start_date' => $start_date,
                    ':end_date' => $end_date,
                    ':bot_id' => $bot_id
                ]);

                $bot_data = $query->execute('slave')->as_array();
                if (!empty($bot_data)) {
                    foreach ($bot_data as $data) {
                        $month = date('Y-m', strtotime($data['date']));
                        $months[$month] = $data[$column];
                    }
                }
                // レコードがなくても0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
            } catch (Database_Exception $e) {
                // 例外時も0のままmonthsを返す
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'months' => $months
                ];
                continue;
            }
        }
        return $result;
    }

    public function get_healthscore_report_data($start_date, $end_date) {
        $active_bots = $this->get_active_bots();
        $all_months = $this->get_all_months($start_date, $end_date);
        $result = [];
        foreach ($active_bots as $bot_id => $bot_name) {
            $monthly_scores = array_fill_keys($all_months, '-');
            $monthly_metrics = array_fill_keys($all_months, null);
            $monthly_criteria = array_fill_keys($all_months, null);
            try {
                $sql = "SELECT 
                        DATE_FORMAT(hs.report_date, '%Y-%m') as month, 
                        hs.health_rank, 
                        hs.total_score, 
                        hs.metrics_detail,
                        hsc.metrics_criteria
                        FROM r_health_score hs
                        LEFT JOIN r_health_score_criteria hsc 
                        ON hs.criteria_version = hsc.criteria_version
                        WHERE hs.report_date BETWEEN :start_date AND :end_date
                        AND hs.bot_id = :bot_id
                        AND hs.report_div = 3
                        ORDER BY hs.report_date ASC";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters([
                    ':start_date' => $start_date,
                    ':end_date' => $end_date,
                    ':bot_id' => $bot_id
                ]);
                $bot_data = $query->execute('slave')->as_array();
                if (!empty($bot_data)) {
                    foreach ($bot_data as $data) {
                        $monthly_scores[$data['month']] = $data['health_rank'] . '(' . $data['total_score'] . ')';
                        $monthly_metrics[$data['month']] = $data['metrics_detail'];
                        $monthly_criteria[$data['month']] = $data['metrics_criteria'];
                    }
                }
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'monthly_scores' => $monthly_scores,
                    'monthly_metrics' => $monthly_metrics,
                    'monthly_criteria' => $monthly_criteria
                ];
            } catch (Database_Exception $e) {
                $result[$bot_id] = [
                    'bot_id' => $bot_id,
                    'bot_name' => $bot_name,
                    'monthly_scores' => $monthly_scores,
                    'monthly_metrics' => $monthly_metrics,
                    'monthly_criteria' => $monthly_criteria
                ];
                continue;
            }
        }
        return $result;
    }

    public function get_mail_logs($start_date, $start_time, $end_date, $end_time, $bot_id = null, $type = null, $email = null, $limit = 500) {
        $start_date_yyyymmdd = $this->convert_to_yyyymmdd($start_date);
        $end_date_yyyymmdd = $this->convert_to_yyyymmdd($end_date);
        $query = "
            SELECT id, bot_id, sender, receiver, receiver_cc, receiver_bcc,
                   title, body, send_time, error_info
            FROM x_mail_log
            WHERE 1=1
        ";
        $params = [];
        if ($bot_id !== null) {
            $query .= " AND bot_id = :bot_id";
            $params[':bot_id'] = $bot_id;
        }
        $query .= " AND vir_send_time BETWEEN :start_date_yyyymmdd AND :end_date_yyyymmdd";
        $params[':start_date_yyyymmdd'] = $start_date_yyyymmdd;
        $params[':end_date_yyyymmdd'] = $end_date_yyyymmdd;
        if ($type !== null) {
            $query .= " AND type = :type";
            $params[':type'] = $type;
        }
        if ($email !== null) {
            $query .= " AND (sender = :email OR receiver = :email OR receiver_cc = :email OR receiver_bcc = :email)";
            $params[':email'] = $email;
        }
        $query .= " AND send_time BETWEEN :start_datetime AND :end_datetime";
        $params[':start_datetime'] = $start_date . ' ' . $start_time;
        $params[':end_datetime'] = $end_date . ' ' . $end_time;
        // インデックスを活用するORDER BY
        if ($bot_id !== null) {
            $query .= " ORDER BY bot_id, vir_send_time DESC";
        } else if ($type !== null) {
            $query .= " ORDER BY vir_send_time DESC, type";
        } else {
            $query .= " ORDER BY vir_send_time DESC";
        }
        $query .= " LIMIT :limit";
        $params[':limit'] = (int)$limit;
        $result = DB::query(Database::SELECT, $query)
            ->parameters($params)
            ->execute()
            ->as_array();
        return $result;
    }
    
    private function convert_to_yyyymmdd($date) {
        $datetime = new DateTime($date);
        return $datetime->format('Ymd');
    }

    public function get_assigned_bot_counts() {
        $sql = "
            WITH active_bots AS (
                SELECT DISTINCT b.bot_id
                FROM t_bot b
                INNER JOIN t_facility_options fo ON b.bot_id = fo.bot_id
                WHERE b.delete_flg != 1 
                AND b.bot_status_cd != 5
                AND fo.start_date <= CURDATE()
                AND (fo.end_date IS NULL OR fo.end_date > CURDATE())
            )
            SELECT 
                JSON_EXTRACT(bs.setting_value, '$.cs_sale_contents_assigned_to_id') as assignee_id,
                COUNT(DISTINCT CASE WHEN ab.bot_id IS NOT NULL THEN b.bot_id END) as active_bot_count,
                COUNT(DISTINCT CASE WHEN ab.bot_id IS NULL THEN b.bot_id END) as inactive_bot_count,
                COUNT(DISTINCT b.bot_id) as total_bot_count
            FROM
                t_bot b
                INNER JOIN t_bot_setting bs ON b.bot_id = bs.bot_id
                LEFT JOIN active_bots ab ON b.bot_id = ab.bot_id
            WHERE
                b.bot_id != 0
                AND b.delete_flg != 1
                AND b.bot_status_cd != 5
                AND bs.setting_cd = 'json_redmine'
                AND bs.setting_value LIKE '%cs_sale_contents_assigned_to_id%'
            GROUP BY 
                JSON_EXTRACT(bs.setting_value, '$.cs_sale_contents_assigned_to_id')
            ORDER BY 
                total_bot_count DESC
        ";
        return DB::query(Database::SELECT, $sql)->execute()->as_array();
    }

    public function get_bot_ngwords($bot_id) {
        $sql = "
            SELECT
                ng.No,
                ng.lang_cd,
                ng.type,
                ng.txt_match,
                ng.ng_word
            FROM
                t_dict_ng_word ng
            WHERE 
                ng.bot_id = :bot_id
        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    public function update_translate_request_status($request_id, $status) {
        $sql = "UPDATE t_translate_operation SET status = :status, upd_date = NOW() WHERE request_id = :request_id";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
            ':status' => $status,
            ':request_id' => $request_id,
        ));
        $query->execute();
    }
}
