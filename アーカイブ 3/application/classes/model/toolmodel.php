<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Basemodel extends Model
{
    function change_item_class()
	{
		$sql = "SELECT a.item_id, a.item_cd, a.item_class_cd, a.item_class_type_cd, b.item_class_name, c.item_class_type_name
				FROM t_item a
				LEFT JOIN t_item_class b ON a.item_class_cd = b.item_class_cd AND b.lang_cd='ja'
				LEFT JOIN t_item_class_type c ON a.item_class_cd = c.item_class_cd AND a.item_class_type_cd = c.item_class_type_cd AND c.lang_cd='ja'";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();
		$sql = "SELECT class_cd, name FROM m_item_class WHERE parent_cd='' AND lang_cd='ja'";
		$class = DB::query(Database::SELECT, $sql)->execute()->as_array('name', 'class_cd');
		$sql = "SELECT class_cd, name FROM m_item_class WHERE parent_cd<>'' AND lang_cd='ja'";
		$class_type = DB::query(Database::SELECT, $sql)->execute()->as_array('name', 'class_cd');
		foreach($results as $result) {
			$columns = array();
			if (array_key_exists($result['item_class_name'], $class)) {
				$columns['item_class_cd'] = $class[$result['item_class_name']];
			}
			else {
				$columns['item_class_cd'] = '00';
			}
			if (array_key_exists($result['item_class_type_name'], $class_type)) {
				$columns['item_class_type_cd'] = $class_type[$result['item_class_type_name']];
			}
			else {
				$columns['item_class_type_cd'] = '00';
			}
			DB::update('t_item')->set($columns)->where('item_id', '=', $result['item_id'])->execute();
		}
	}
}

?>
