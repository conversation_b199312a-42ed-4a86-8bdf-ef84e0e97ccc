<?php defined('SYSPATH') or die('No direct script access.');

class CLI_Asoview {
	public $_model;

    public function __construct()
	{
		$this->_model = new Model_Basemodel();
	}
    public function _call_method($params)
    {
        $method = array_shift($params);
        return call_user_func_array(array($this, $method), $params);
    }

	private function _logEcho($_to_echo)	//, $line=false
	{
		Log::instance()->add(Log::DEBUG, $_to_echo);
	}
	private function write_log($log) {
		$file =  APPPATH . "../../files/log/batch_" . date('Y-m-d') . ".log";
		$f = fopen($file, 'a');
		$log = date('Y-m-d H:i:s') . ' ' . $log . PHP_EOL;
		fwrite($f, $log);
		fclose($f);
		echo($log);
		//$this->_model->log_info('batch', $log);
	}

    	// *** MAIN FUNCTION interface function for batch execution in release
	public function download_process_asoview_daily_data_integration ($raw_flg=1) {
		// 1. download asoview data in compressed files from asoview server according to today's timestamp,
		// and extract them into regular csv/tsv files
		$this->_download_extract_asoview_compressed_file($raw_flg);

		// 2. import to database, which actually was done inside last function

		// 3. delete downloaded/extracted files, which actually was done inside download/ingest function
	}

	private $_asoview_ticket_very_market_columns_set = "(@col1_val, mgr_name, @description, @introduction, @ticket_image_url, @genre_code, @genre_name, @col_age_lower, @col_age_upper, @sales_date_start, @sales_date_end, @precaution, @ticket_url, @ticket_price_type, @ticket_price_type_age, @ticket_retail_price_tax_included, @ticket_list_price, @valid_period, @valid_thru, @unavailable_period, @available_date_types, @available_time_segments, @last_reception_time_points, @max_purchase, @location_id, @location_name, @region, @rgn_code, @prefecture, @prf_code, @area_big, @area_code, @area_small, @area_small_code, @location_phone_number, @location_zip, @location_address, @access_by_car, @access_public_trans, @col_lat_lon, @sc_vendor, @sc_vendor_representative, @sc_vendor_address, @sc_vendor_phone_number) SET product_id = REPLACE(@col1_val, 'ticket', ''), provider = 'asoview', type = 'ticket', commit_rate_in = 5, commit_rate_out = 3, public_flg = 1";
	private $_asoview_plan_very_market_columns_set = "(@col1_val, mgr_name, @link, @description, @area_code, @meeting_place_id, @meeting_place_name, @time_required, @period, @target_age, @reservation_close_day, @reservation_close_time) SET product_id = REPLACE(@col1_val, 'pln', ''), provider = 'asoview', type = 'plan', commit_rate_in = 3, commit_rate_out = 1, public_flg = 1";
	private $_asoview_ticket_columns_set = "(@col1_val, ticket_name, description, introduction, ticket_image_url, @col2_val, genre_name, @col_age_lower, @col_age_upper, sales_date_start, sales_date_end, precaution, ticket_url, ticket_price_type, ticket_price_type_age, ticket_retail_price_tax_included, ticket_list_price, valid_period, valid_thru, unavailable_period, available_date_types, available_time_segments, last_reception_time_points, max_purchase, location_id, location_name, region, @col3_val, prefecture, @col4_val, area_big, @col5_val, area_small, area_small_code, location_phone_number, @col6_val, location_address, access_by_car, access_public_trans, @col_lat_lon, sc_vendor, sc_vendor_representative, sc_vendor_address, sc_vendor_phone_number) SET ticket_id = REPLACE(@col1_val, 'ticket', ''), genre_id = REPLACE(@col2_val, 'act', ''), age_limit_lower = NULLIF(@col_age_lower, ''), age_limit_upper = NULLIF(@col_age_upper, ''), rgn_cd = REPLACE(@col3_val, 'rgn', ''), prf_cd = REPLACE(@col4_val, 'prf', ''), are_cd = REPLACE(@col5_val, 'are', ''), location_zip = REPLACE(@col6_val, '-', ''), location_geo_point = Point(SUBSTRING_INDEX(@col_lat_lon,' ',-1), SUBSTRING_INDEX(@col_lat_lon,' ',1))";

	// use raw z_asoview_ticket, or plan to use t_very_ticket in very/real case
	private $_asoview_ticket_table = ["t_very_ticket", "z_asoview_ticket"];
	private $_asoview_very_market_table = "t_very_market_item";	//
	private $_refined_very_market_table = "t_market_item";
	private $_very_market_description_table = "t_market_item_description";
	private $_market_price_matrix_table = "t_market_item_price_mtx";

	private $_asoview_tsv_file_keywords = array("1_1_category_group", "1_2_category", "1_3_category_genre", "1_4_genre",
		"2_1_region", "2_2_prefecture", "2_3_area", 
		"3_1_plan", "3_2_plan_genre", "3_3_meeting", "3_4_fee", "3_5_image", "3_6_movie");

	private $_asoview_very_tables = ["m_very_category_group", "m_very_category", "m_very_category_genre", "m_very_genre",
		"m_very_region", "m_very_prefecture", "m_very_area", 
		"t_very_plan", "t_very_plan_genre", "t_very_meeting", "t_very_fee", "t_very_image", "t_very_movie"];
		
	private $_asoview_activity_raw_tables = ["z_asoview_category_group", "z_asoview_category", "z_asoview_category_genre", "z_asoview_genre",
		"z_asoview_region", "z_asoview_prefecture", "z_asoview_area", 
		"z_asoview_plan", "z_asoview_plan_genre", "z_asoview_meeting", NULL, "z_asoview_image", "z_asoview_movie"];	//t_very_fee>NULL

	private function _asoview_activity_tables($raw_flg=0) {
		return $raw_flg ? $this->_asoview_activity_raw_tables : $this->_asoview_very_tables;
	}

	private $_asoview_very_columns_sets = [
		"", "",
		"(category_id, @col1_val, category_genre_code) SET genre_id = REPLACE(@col1_val, 'act', '')", 
		"LINES STARTING BY 'act'", 
		
		"LINES STARTING BY 'rgn'",
		"(@col1_val, @col2_val, prefecture) SET prf_cd = REPLACE(@col1_val, 'prf', ''), rgn_cd = REPLACE(@col2_val, 'rgn', '')",
		"(@col1_val, @col2_val, area_big) SET are_cd = REPLACE(@col1_val, 'are', ''), prf_cd = REPLACE(@col2_val, 'prf', '')",

		"(@col1_val, title, link, description, @col2_val, meeting_place_id, meeting_place_name, time_required, period, target_age, reservation_close_day, reservation_close_time) SET pln_cd = REPLACE(@col1_val, 'pln', ''), are_cd = REPLACE(@col2_val, 'are', '')",
		"(@col1_val, @col2_val) SET pln_cd = REPLACE(@col1_val, 'pln', ''), genre_id = REPLACE(@col2_val, 'act', '')",
		"(@col1_val, mtg_place_flg, mtg_place, @col2_val, @col3_val, location, @col_lat, @col_lon) SET pln_cd = REPLACE(@col1_val, 'pln', ''), are_cd = REPLACE(@col2_val, 'are', ''), zip = REPLACE(@col3_val, '-', ''), geo_point = Point(@col_lon, @col_lat)", 
		NULL, "LINES STARTING BY 'pln'", "LINES STARTING BY 'pln'"];

	private function _download_extract_asoview_compressed_file ($raw_flg=0) {
		$date_string = date("Ymd");
		$info = '=======Starting ['.__FUNCTION__."]: asoview daily affiliate ingesting into database...";
		$this->write_log($info);
		$target_ticket_gz_file = APPPATH . "../../files/temp/asoview/asoview_tickets_$date_string.gz";//	asoview/tar/

		$ticket_gz_file_url = "http://fileif.asoview.com/tickets/asoview_tickets_$date_string.gz";
		// download ticket csv file in format of .gz
		$ticket_download_bytes_written = file_put_contents($target_ticket_gz_file, file_get_contents($ticket_gz_file_url));
		if ($ticket_download_bytes_written) {	// extract ticket csv file from .gz
			$info = "asoview_tickets_$date_string.gz bytes written:$ticket_download_bytes_written";
			$this->write_log($info);
			if ($this->_extract_gz_compressed_file($target_ticket_gz_file, '.csv')) {
				$info = "asoview_tickets_$date_string.gz bytes extracted to csv, ingesting into database...";
				$this->write_log($info);

				//TODO improve:delete only when all downloads/extracts succeeded
				$this->_asoview_clear_data_from_tables($raw_flg);
				//TODO improve:check clear results

				// ingest ticket csv data into database
				$ticket_csv_file_path = APPPATH . "../../files/temp/asoview/asoview_tickets_$date_string.csv";
				//ingest into ticket table
				$this->_asoview_load_data_local_infile_tsv($ticket_csv_file_path, $this->_asoview_ticket_table[$raw_flg], $this->_asoview_ticket_columns_set, ",", "\"", 1);
				//ingest into market table
				if ( ! $raw_flg ) {
					$this->_asoview_load_data_local_infile_tsv($ticket_csv_file_path, $this->_asoview_very_market_table, $this->_asoview_ticket_very_market_columns_set, ",", "\"", 1);
				}
				// delete tickets csv file on success/failure
				unlink($ticket_csv_file_path);
			} else {
				$warning = "asoview_tickets_$date_string.gz failed to extract, ticket data won't be ingested";
				$this->write_log($warning);
				//TODO improve:abort without continue?
			}
		} else {
			$warning = "asoview_tickets_$date_string.gz failed to written, ticket data won't be ingested";
			$this->write_log($warning);
		}

		// $month_string = '202309';
		$month_string = date("Ym");
		$ticket_tar_gz_file_url = "http://fileif.asoview.com/activalues/$month_string/activalues_$date_string.tar.gz";
		$target_ticket_tar_gz_file = APPPATH . "../../files/temp/asoview/activalues_$date_string.tar.gz";//	asoview/tar/

		$target_activity_folder = APPPATH . "../../files/temp/asoview/$month_string/activity/";//	asoview/tar/

		//TODO improve:check month folder existance
		// download monthly/daily data compressed in format of .tar.gz from folder containing tsv files
		$tar_bytes_written = 	//	false;
			file_put_contents($target_ticket_tar_gz_file, file_get_contents($ticket_tar_gz_file_url));
		if ($tar_bytes_written) {
			$info = "activalues_$date_string.tar.gz bytes written:$tar_bytes_written";
			$this->write_log($info);
			if ($this->_extract_tar_gz_compressed_folder($target_ticket_tar_gz_file, $target_activity_folder) ) {
				$info = "$target_ticket_tar_gz_file extracted to $target_activity_folder, ingesting into database...";
				$this->write_log($info);

				// ingesting into database
				$tsv_files = glob($target_activity_folder."activalues_{$date_string}_*.{tsv}", GLOB_BRACE);
				$info = '.tsv files in extract destination folder:'.count($tsv_files);
				$this->write_log($info);
				$fee_tsv_file = NULL;
				foreach($tsv_files as $tsv_file) {
					$info = 'ingesting:'.$tsv_file;
					$this->write_log($info);
				  	// ingest each tsv here
					// ingest activalues_$date_string_3_4_fee.tsv
					$prefix = $target_activity_folder."activalues_{$date_string}_";
					$suffix = ".tsv";
					$key = str_replace([$prefix, $suffix], "", $tsv_file);
					echo('tsv file KEY:'.$key.PHP_EOL);
					if (in_array($key, $this->_asoview_tsv_file_keywords)) {
						$index = array_search($key, $this->_asoview_tsv_file_keywords);
						$activity_table = $this->_asoview_activity_tables($raw_flg)[$index];	// _asoview_very_tables
						if ($key == "3_4_fee") {
							if ( ! $raw_flg ) {
								$info = 'Ingesting(in PHP) asoview fee data processing into database...';
								$this->write_log($info);
								$this->_read_tsv($tsv_file, null, $activity_table);
							} else {
								$fee_tsv_file = $tsv_file;
								$info = 'Skipped ingesting asoview fee data, will process into database after market item table ingested for item_id referencing...';
								$this->write_log($info);
								continue;
							}
						} else {
							$this->_asoview_load_data_local_infile_tsv($tsv_file, $activity_table, $this->_asoview_very_columns_sets[$index], "\\t");
							if ( $key == "3_1_plan" && ! $raw_flg ) {
								// ingest into market table
								$this->_asoview_load_data_local_infile_tsv($tsv_file, $this->_asoview_very_market_table, $this->_asoview_plan_very_market_columns_set, "\\t");
							}
						}
						unlink($tsv_file);
					}
				}
				if ( $raw_flg ) {				
					$this->_extract_market_data_from_asoview_tables($fee_tsv_file);
					if ($fee_tsv_file) {
						unlink($fee_tsv_file);
					}
				}
				//TODO improve/check:There should not be any files other than previously deleted .tsv files
				rmdir($target_activity_folder);
				$info = 'cleaned temp directory '.$target_activity_folder;
				$this->write_log($info);
				$info = '=======END _download_extract_asoview_compressed_file completed';
				$this->write_log($info);
			} else {
				$error = "=======ABORTED $target_ticket_tar_gz_file failed to decompress or extract, asoview daily importing aborted";
				$this->write_log($error);
				return false;
			}
		} else {
			$warning = "=======ABORTED activalues_$date_string.tar.gz failed to download, asoview monthly/daily data won't be ingested";
			$this->write_log($warning);
			return false;
		}
	}

	private function _extract_market_data_from_asoview_tables ($fee_tsv_file) {
		//mark all old data delete flag
		$market_description_clean_sql = "UPDATE $this->_refined_very_market_table 
			SET `delete_flg` = 1 
			WHERE item_cd LIKE 'AS%' AND link_type_cd= 'as' AND item_div IN (15,16) AND `delete_flg` = 0";
		$market_description_clean_query = DB::query(Database::UPDATE, $market_description_clean_sql);
		$cleaned = $market_description_clean_query->execute();
		$info = 'Cleaned up(mark delete_flg before inserting/updating) market item '.$cleaned;
		$this->write_log($info);

		// process mapping from genre/area_big to m_class_code.class_cd
		$genre_mapping = null;
		$area_mapping = null;
		{
			$genre_table = $this->_asoview_activity_tables(1)[3];
			$area_big_table = $this->_asoview_activity_tables(1)[6];
			$class_code_table = 'm_class_code';

			$genre_mapping_sql = "SELECT *
			FROM $genre_table AS genre
			JOIN $class_code_table AS code ON (CAST(genre.genre_id AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_general_ci) =code.word 
			WHERE code_div=999915";
			$genre_mapping_query = DB::query(Database::SELECT, $genre_mapping_sql);
			$genre_mapping = $genre_mapping_query->execute()->as_array('word', 'class_cd');

			
			$area_mapping_sql = "SELECT *
			FROM $area_big_table AS area
			JOIN $class_code_table AS code ON (CAST(area.are_cd AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_general_ci) =code.word 
			WHERE code_div=999925";
			$area_mapping_query = DB::query(Database::SELECT, $area_mapping_sql);
			$area_mapping = $area_mapping_query->execute()->as_array('word', 'class_cd');

			$info = 'Mapping genre_id and are_cd into m_class_code: '.count($genre_mapping).'/'.count($area_mapping);
			$this->write_log($info);
		}

		// extract from ticket table, easier one (seems to be)
		$this->_extract_market_data_from_asoview_tickets($genre_mapping, $area_mapping, 1);
		// extract from plan table, complicated one, also need data from meeting table, fee table and genre table
		$this->_extract_market_data_from_asoview_plans($genre_mapping, $area_mapping, 0, 1);

		// extract fee data
		$this->_process_market_asoview_plan_prices($fee_tsv_file);

		// insert market item description as ja records
		$this->_cleanup_market_item_description();
	}

	private function _extract_market_data_from_asoview_tickets ($genre_mapping=NULL, $area_mapping=NULL, $raw_flg=0) {
		$item_div=16;
		$link_type_cd='as';
		$ticket_table = $this->_asoview_ticket_table[$raw_flg];//raw table

		// item_id : mgr_id?
		$duplicates_sub_select = "SELECT link_id FROM $this->_refined_very_market_table WHERE item_cd LIKE 'AS%' AND link_type_cd= '$link_type_cd' AND item_div= $item_div";
		
		// market_desc_duplicates_sub_select
		$market_desc_duplicates_sub_select = "SELECT item_id FROM $this->_very_market_description_table";

		$dup_sql = "SELECT *, ST_X(location_geo_point) as location_lng, ST_Y(location_geo_point) as location_lat FROM $ticket_table WHERE ticket_id IN ($duplicates_sub_select)";
		$dup_query = DB::query(Database::SELECT, $dup_sql);
		$dup_results = $dup_query->execute();
		$dup_tickets = $dup_results->as_array();

		

		$info = 'Processing market data, to update old tickets count:'.count($dup_tickets);
		$this->write_log($info);

		$rows_updated = 0;
		$index = 0;
		// no transaction update
		$time_start = time();
		foreach($dup_tickets as $ticket) {
			// process old ticket and update in new market table
			$asoview_market_row_update_query = DB::update($this->_refined_very_market_table)
				->where('item_cd', 'LIKE', 'AS%')
				->where('link_type_cd', '=', $link_type_cd)
				->where('item_div', '=', $item_div)
				->where('link_id', '=', $ticket['ticket_id'])
				;

			// process updating link data in function
			$link_data = $this->_process_market_asoview_ticket_upsert_values($ticket, false, $index);

			if (!$link_data) {
				$error = 'unable to process link_data to update old ticket.'.($link_data);
				echo($error.PHP_EOL);
				Log::instance()->add(Log::ERROR, $error);
				break;
			}
			
			$class_cd = '';
			$area_cd = '';
			if ($genre_mapping && isset($genre_mapping[$ticket['genre_id']])) {
				$class_cd = $genre_mapping[$ticket['genre_id']];
			}
			if ($area_mapping && isset($area_mapping[$ticket['are_cd']])) {
				$area_cd = $area_mapping[$ticket['are_cd']];
			}


			$asoview_market_row_update_query
				->value('item_cd', $this->_db_expr_market_item_cd($item_div, $ticket['ticket_id']))
				->value('class_cd', $class_cd)
				->value('area_cd', $area_cd)
				->value('geo_point', $ticket['location_geo_point'])
				->value('location_lng', $ticket['location_lng'])
				->value('location_lat', $ticket['location_lat'])
				->value('item_name', $ticket['ticket_name'])
				->value('delete_flg', 0)
				->value('upd_time', date('Y-m-d H:i:s', time()))
				->value('link_data', $link_data)
				;
			$updated = $asoview_market_row_update_query->execute();
			$rows_updated += (int)$updated;
			$index ++;
		}
		$info = 'updated tickets count:'.$rows_updated.", seconds lapsed ".(time()-$time_start);
		$this->write_log($info);

		if ($rows_updated) {
			// update old tickets into market desc 
			$info = "Updating $rows_updated market item(ticket) descriptions... ";
			$this->write_log($info);
			$market_description_batch_insert_sql = "UPDATE $this->_very_market_description_table AS d INNER JOIN $this->_refined_very_market_table AS market ON d.item_id = market.item_id
			SET d.title = market.item_name, d.description = JSON_UNQUOTE(JSON_EXTRACT(market.link_data, '$.description')), d.upd_time = NOW()
					WHERE `item_cd` LIKE 'AS%' AND `link_type_cd` = 'as' AND `item_div` = $item_div AND `delete_flg` = 0";
			$market_description_batch_insert_query = DB::query(Database::INSERT, $market_description_batch_insert_sql);
			$updated = $market_description_batch_insert_query->execute();
			$info = 'Updated market item(ticket) descriptions '.json_encode($updated);
			$this->write_log($info);
		}

		//new inserts
		$new_tickets_sql = "SELECT *, ST_X(location_geo_point) as location_lng, ST_Y(location_geo_point) as location_lat FROM $ticket_table WHERE ticket_id NOT IN ($duplicates_sub_select)";
		$new_query = DB::query(Database::SELECT, $new_tickets_sql);
		$new_results = $new_query->execute();
		$new_tickets = $new_results->as_array();

		$market_insert_columns = array(
			'item_cd', 'link_type_cd', 'link_id', 'item_div', 
			'class_cd', 'area_cd',
			'geo_point', 'location_lng', 'location_lat',
			'item_name',
			'item_data',
			'link_data',
			'delete_flg', 'upd_user', 
		);

		$class_cd_insert_index = 4;
		$area_cd_insert_index = 5;

		$info = 'Processing market data, to ingest new tickets count:'.count($new_tickets);
		$this->write_log($info);
		$asoview_market_row_insert_query = DB::insert($this->_refined_very_market_table, $market_insert_columns);
		$batch_row_count = 0;
		$rows_inserted = 0;
		$index = 0;
		foreach($new_tickets as $ticket) {
			// process new ticket and insert into new market table
			if ( $batch_row_count >= 100 ) {	//999
				$asoview_market_row_insert_query->execute();
				$asoview_market_row_insert_query = DB::insert($this->_refined_very_market_table, $market_insert_columns);
				$rows_inserted += $batch_row_count;
				$batch_row_count = 0;
				$info = 'ingested(accumulated):'.$rows_inserted;
				$this->write_log($info);
			}

			// process inserting_values in function
			$inserting_values = $this->_process_market_asoview_ticket_upsert_values($ticket, true, $index);
	
			if ($genre_mapping && isset($genre_mapping[$ticket['genre_id']])) {
				$inserting_values[$class_cd_insert_index] = $genre_mapping[$ticket['genre_id']];
			}
			if ($area_mapping && isset($area_mapping[$ticket['are_cd']])) {
				$inserting_values[$area_cd_insert_index] = $area_mapping[$ticket['are_cd']];
			}

			$asoview_market_row_insert_query->values($inserting_values);
			$batch_row_count ++;
			$index ++;
		}
		if ( $batch_row_count > 0 ) {
			$asoview_market_row_insert_query->execute();
			$rows_inserted += $batch_row_count;
			$batch_row_count = 0;
			$info = 'ingested(accumulated):'.$rows_inserted;
			$this->write_log($info);

			// insert new ticket into market desc 
			$market_description_batch_insert_sql = "INSERT INTO $this->_very_market_description_table  
				SELECT item_id, 'ja', item_name, JSON_UNQUOTE(JSON_EXTRACT(link_data, '$.description')), 1, NOW() FROM t_market_item 
				WHERE `item_cd` LIKE 'AS%' AND `link_type_cd` = 'as' AND `item_div` = $item_div AND `delete_flg` = 0 AND item_id NOT IN ($market_desc_duplicates_sub_select)";
			$market_description_batch_insert_query = DB::query(Database::INSERT, $market_description_batch_insert_sql);
			$inserted = $market_description_batch_insert_query->execute();
			$info = 'Inserted market item(ticket) descriptions '.json_encode($inserted);
			$this->write_log($info);
		}
	}

	//extract from z_asoview_plan into t_market_item
	private function _extract_market_data_from_asoview_plans ($genre_mapping=NULL, $area_mapping=NULL, $debug_mode=0, $raw_flg=0) {
		$item_div=15;
		$link_type_cd='as';

		$plan_table = $this->_asoview_activity_tables($raw_flg)[7];
		$meeting_table = $this->_asoview_activity_tables($raw_flg)[9];
		// item_id : mgr_id?
		$duplicates_sub_select = "SELECT link_id FROM $this->_refined_very_market_table WHERE item_cd LIKE 'AS%' AND link_type_cd= '$link_type_cd' AND item_div= $item_div";

		// market_desc_duplicates_sub_select
		$market_desc_duplicates_sub_select = "SELECT item_id FROM $this->_very_market_description_table";

		$dup_sql = "SELECT pln.*, mtg.*, ST_X(mtg.geo_point) as location_lng, ST_Y(mtg.geo_point) as location_lat
			FROM $plan_table AS pln
			JOIN $meeting_table AS mtg ON pln.pln_cd=mtg.pln_cd 
			WHERE pln.pln_cd IN ($duplicates_sub_select)
			AND mtg.mtg_place_flg=1";
		$dup_query = DB::query(Database::SELECT, $dup_sql);
		$dup_results = $dup_query->execute();
		// $dup_plans = $dup_results->as_array();
		$dup_plans = $dup_results;
		

		$info = 'Processing market data, to update old plans count:'.count($dup_plans);
		$this->write_log($info);

		$rows_updated = 0;
		$index = 0;
		$batch_row_count = 0;
		$rows_inserted = 0;
		// no transaction update
		$time_start = time();
		$unmapped_genre = [];
		foreach($dup_plans as $plan) {
			// process old ticket and update in new market table
			if ($debug_mode==1) break;	//break for insert test
			// entry by entry update
			$asoview_market_row_update_query = DB::update($this->_refined_very_market_table)
				->where('item_cd', 'LIKE', 'AS%')
				->where('link_type_cd', '=', $link_type_cd)
				->where('item_div', '=', $item_div)
				->where('link_id', '=', $plan['pln_cd'])
				;

			// process updating link data in function
			$plan_object = $this->_process_market_asoview_plan_upsert_values($plan, false, $index, $raw_flg);

			if (!$plan_object) {
				$error = 'unable to process link_data to update old plan:'.json_encode($plan_object);
				$this->write_log($error);
				Log::instance()->add(Log::ERROR, $error);
				break;
			}

			// process genre_id and are_cd to class_cd, could be multi genre_id per plan
			$class_cd = '';
			$area_cd = '';
			if ($genre_mapping && $plan_object['genre_ids']) {
				$genre_class_codes = [];
				foreach($plan_object['genre_ids'] as $genre_id){
					if (isset($genre_mapping[$genre_id])) {
						$genre_class_codes []= $genre_mapping[$genre_id];
					} else if(!in_array($genre_id, $unmapped_genre)){
						$error = 'unable to mapping genre_id to class_cd :'.$genre_id;
						$this->write_log($error);
						$unmapped_genre []= $genre_id;
					}
				}
				$class_cd = implode(' ', $genre_class_codes);			
			}
			if ($area_mapping && isset($area_mapping[$plan['are_cd']])) {
				$area_cd = $area_mapping[$plan['are_cd']];
			}
			unset($plan_object['genre_ids']);
			$link_data = json_encode($plan_object, JSON_UNESCAPED_UNICODE);

			$asoview_market_row_update_query
				->value('item_cd', $this->_db_expr_market_item_cd($item_div, $plan['pln_cd']))
				->value('class_cd', $class_cd)
				->value('area_cd', $area_cd)
				// fetch geo from meeting table
				->value('geo_point', $plan['geo_point'])
				->value('location_lng', $plan['location_lng'])
				->value('location_lat', $plan['location_lat'])
				->value('item_name', $plan['title'])
				->value('delete_flg', 0)
				->value('upd_time', date('Y-m-d H:i:s', time()))
				->value('link_data', $link_data)
				;
			$updated = $asoview_market_row_update_query->execute();
			$rows_updated += (int)$updated;

			if ( !$updated ) {
				$error = 'failed to update old plan:'.($link_data);
				$this->write_log($error);
				Log::instance()->add(Log::ERROR, $error);
			}

			if ( $index % 500== 0) {
				$info = 'updated(accumulated) market plans:'.$rows_updated.", seconds lapsed ".(time()-$time_start);
				$this->write_log($info);
			}

			$index ++;
			if ($debug_mode==2) break;	//break for update only one test mode
		}
		$info = 'updated market plans count:'.$rows_updated.", seconds lapsed ".(time()-$time_start);
		$this->write_log($info);

		if ($rows_updated) {
			// update old plans into market desc 
			$info = "Updating $rows_updated market item(plan) descriptions... ";
			$this->write_log($info);
			$market_description_batch_insert_sql = "UPDATE $this->_very_market_description_table AS d INNER JOIN $this->_refined_very_market_table AS market ON d.item_id = market.item_id
			SET d.title = market.item_name, d.description = JSON_UNQUOTE(JSON_EXTRACT(market.link_data, '$.description')), d.upd_time = NOW()
					WHERE `item_cd` LIKE 'AS%' AND `link_type_cd` = 'as' AND `item_div` = $item_div AND `delete_flg` = 0";
			$market_description_batch_insert_query = DB::query(Database::INSERT, $market_description_batch_insert_sql);
			$updated = $market_description_batch_insert_query->execute();
			$info = 'Updated market item(plan) descriptions '.json_encode($updated);
			$this->write_log($info);
		}

		if ($debug_mode==2) return;	//break for update only test mode
		//new inserts
		$new_plan_sql = "SELECT pln.*, mtg.*, ST_X(mtg.geo_point) as location_lng, ST_Y(mtg.geo_point) as location_lat
			FROM $plan_table AS pln 
			JOIN $meeting_table AS mtg ON pln.pln_cd=mtg.pln_cd 
			WHERE pln.pln_cd NOT IN ($duplicates_sub_select)
			AND mtg.mtg_place_flg=1";
		$new_query = DB::query(Database::SELECT, $new_plan_sql);
		$new_results = $new_query->execute();
		$new_plans = $new_results->as_array();

		$market_insert_columns = array(
			'item_cd', 'link_type_cd', 'link_id', 'item_div', 
			'class_cd', 'area_cd',
			'geo_point', 'location_lng', 'location_lat',
			'item_name',
			'item_data',
			'link_data',
			'delete_flg', 'upd_user', 
		);
		$class_cd_insert_index = 4;
		$area_cd_insert_index = 5;
		$link_data_insert_index = 11;
		
		$info = 'Processing market data, to ingest new plans count:'.count($new_plans);
		$this->write_log($info);
		$asoview_market_row_insert_query = DB::insert($this->_refined_very_market_table, $market_insert_columns);
		$batch_row_count = 0;
		$rows_inserted = 0;
		$index = 0;
		foreach($new_plans as $plan) {
			// process new ticket and insert into new market table
			if ( $batch_row_count >= 100 ) {	//999
				$asoview_market_row_insert_query->execute();
				$asoview_market_row_insert_query = DB::insert($this->_refined_very_market_table, $market_insert_columns);
				$rows_inserted += $batch_row_count;
				$batch_row_count = 0;
				$info = 'ingested(accumulated):'.$rows_inserted.", seconds lapsed ".(time()-$time_start);
				$this->write_log($info);
			}

			$meeting_are_cd = $plan['are_cd'];
			// process inserting_values in function
			$inserting_values = $this->_process_market_asoview_plan_upsert_values($plan, true, $index, $raw_flg);

			// process genre_id and are_cd to class_cd, could be multi genre_id per plan
			$plan_object = $inserting_values[$link_data_insert_index];
			if ($genre_mapping && $plan_object['genre_ids']) {
				$genre_class_codes = [];
				foreach($plan_object['genre_ids'] as $genre_id){
					if (isset($genre_mapping[$genre_id])) {
						$genre_class_codes []= $genre_mapping[$genre_id];
					} else if(!in_array($genre_id, $unmapped_genre)){
						$error = 'unable to mapping genre_id to class_cd :'.$genre_id;
						$this->write_log($error);
						$unmapped_genre []= $genre_id;
					}
				}
				$inserting_values[$class_cd_insert_index] = implode(' ', $genre_class_codes);
			}
			unset($plan_object['genre_ids']);
			if ($area_mapping && isset($area_mapping[$meeting_are_cd])) {
				$inserting_values[$area_cd_insert_index] = $area_mapping[$meeting_are_cd];
			}
			$inserting_values[$link_data_insert_index] = json_encode($plan_object, JSON_UNESCAPED_UNICODE);

			$asoview_market_row_insert_query->values($inserting_values);
			$batch_row_count ++;
			$index ++;
		}
		if ( $batch_row_count > 0 ) {
			$asoview_market_row_insert_query->execute();
			$rows_inserted += $batch_row_count;
			$batch_row_count = 0;
			$info = 'ingested(accumulated):'.$rows_inserted.", seconds lapsed ".(time()-$time_start);
			$this->write_log($info);

			// insert new plan into market desc 
			$market_description_batch_insert_sql = "INSERT INTO $this->_very_market_description_table  
				SELECT item_id, 'ja', item_name, JSON_UNQUOTE(JSON_EXTRACT(link_data, '$.description')), 1, NOW() FROM t_market_item 
				WHERE `item_cd` LIKE 'AS%' AND `link_type_cd` = 'as' AND `item_div` = $item_div AND `delete_flg` = 0 AND item_id NOT IN ($market_desc_duplicates_sub_select)";
			$market_description_batch_insert_query = DB::query(Database::INSERT, $market_description_batch_insert_sql);
			$inserted = $market_description_batch_insert_query->execute();
			$info = 'Inserted market item(ticket) descriptions '.json_encode($inserted);
			$this->write_log($info);
		}		
	}

	private function _process_market_asoview_plan_prices($fee_tsv_file){// latest pln_cd>item_id mapping
		$item_div=15;
		$link_type_cd='as';

		// clean up plan prices in t_market_item_price_mtx
		$market_prices_clean_sql = "DELETE FROM $this->_market_price_matrix_table 
			WHERE item_id IN (SELECT item_id from t_market_item 
				WHERE item_cd LIKE 'AS%' AND link_type_cd= '$link_type_cd' AND item_div= $item_div AND delete_flg=0
				)";
		$market_prices_clean_query = DB::query(Database::DELETE, $market_prices_clean_sql);
		$cleaned = $market_prices_clean_query->execute();
		$info = 'Cleaned up(before inserting) market prices '.$cleaned;
		$this->write_log($info);

		// Ingesting asoview fee data via php processing into database market price matrix table
		$plan_item_select = "SELECT item_id, link_id FROM $this->_refined_very_market_table WHERE item_cd LIKE 'AS%' AND link_type_cd= '$link_type_cd' AND item_div= $item_div";
		$plan_item_query = DB::query(Database::SELECT, $plan_item_select);
		$plan_item_results = $plan_item_query->execute();
		$plan_item_mapping = $plan_item_results->as_array('link_id', 'item_id');
		
		$info = 'Ingesting asoview fee data via php processing into database market price matrix table...';
		$this->write_log($info);

		if (!$fee_tsv_file) {
			$month_string = date("Ym");
			$date_string = date("Ymd");
			$date_string = '20240307';
			$fee_tsv_file = APPPATH . "../../files/temp/asoview/$month_string/activity/activalues_{$date_string}_3_4_fee.tsv";// asoview/tar/
		}
		$this->_read_tsv($fee_tsv_file, $plan_item_mapping, $this->_market_price_matrix_table);

		
		$info = 'Ingested market price matrix';
		$this->write_log($info);
	}

	private function _cleanup_market_item_description() {
		//clean up asoview tickets/plans in market item description table
		// clean up those not updated/inserted
		$market_description_clean_sql = "DELETE FROM $this->_very_market_description_table 
			WHERE item_id IN (SELECT item_id from t_market_item 
				WHERE item_div IN (15,16) AND `delete_flg` = 1)";
				
		$market_description_clean_query = DB::query(Database::DELETE, $market_description_clean_sql);
		$cleaned = $market_description_clean_query->execute();
		$info = 'Cleaned up(delete outdated after updating/inserting) market item descriptions '.$cleaned;
		$this->write_log($info);
	}

	private function _explode_ignoring_parentheses($string) {
		// 正規表現でカンマを分割。ただし、（）内のカンマは無視
		$pattern = '/,(?![^（]*）)/';
		return preg_split($pattern, $string);
	}

	private function _decimal_separator_converter($str, $reverse=false) {
		$pattern = '/([0-9]+),([0-9]+)/i';
		$replacement = '${1}.${2}';
		if ($reverse) {
			$pattern = '/([0-9]+)\.([0-9]+)/i';
			$replacement = '${1},${2}';
		}
		return preg_replace($pattern, $replacement, $str);
	}

	protected function _test_process_market_asoview_ticket_upsert_values() {
		// test function for _process_market_asoview_ticket_upsert_values
		$tickets_sql = "SELECT *, ST_X(location_geo_point) as location_lng, ST_Y(location_geo_point) as location_lat FROM z_asoview_ticket WHERE genre_id IN (0, 1)";
		$tickets_query = DB::query(Database::SELECT, $tickets_sql);
		$tickets_results = $tickets_query->execute();
		$tickets = $tickets_results->as_array();
		foreach($tickets as $ticket) {
			// process each ticket for testing, we can use the sample data
			echo '#'.$ticket['ticket_id'].':'.$ticket['ticket_name'].PHP_EOL.'チケット価格タイプ<'.$ticket['ticket_price_type'].'>'.PHP_EOL;
			$link_data = $this->_process_market_asoview_ticket_upsert_values($ticket, false);
			print_r(json_decode($link_data,true)['ticket_price_type_array']);
		}
	}

	private function _process_market_asoview_ticket_upsert_values($ticket, $insert=true, $index=0) {
		$item_div = 16;
		//geo point of location
		$geo_point = $ticket['location_geo_point'];
		unset($ticket['location_geo_point']);
		$location_lng = $ticket['location_lng'];
		unset($ticket['location_lng']);
		$location_lat = $ticket['location_lat'];
		unset($ticket['location_lat']);
		if (isset($ticket['v_location_geo'])) {
			unset($ticket['v_location_geo']);
		}

		//配列に整形する前の値はどちらの値を参照しているかわからず、バグの原因になりそうなため不要
		//images
		$images_url = [];
		if ($ticket['ticket_image_url']) {
			$images_url = explode(',', $ticket['ticket_image_url']);
		}
		$ticket['ticket_image_url_array'] = $images_url;
		unset($ticket['ticket_image_url']);
		
		//ticket price types
		$price_types = [];
		if ($ticket['ticket_price_type'] && ($ticket['ticket_retail_price_tax_included'] || $ticket['ticket_retail_price_tax_included'] === '0') && ($ticket['ticket_list_price'] || $ticket['ticket_list_price'] === '0')) {	// && $ticket['ticket_price_type_age']
			$ticket_price_types = $this->_explode_ignoring_parentheses($this->_decimal_separator_converter($ticket['ticket_price_type']));
			$ticket_price_type_ages = explode(',', $ticket['ticket_price_type_age']);
			$ticket_retail_prices_tax_included = explode(',', $ticket['ticket_retail_price_tax_included']);
			$ticket_list_prices = explode(',', $ticket['ticket_list_price']);
			if ( (count($ticket_price_types) == count($ticket_price_type_ages) || !$ticket_price_type_ages) && 
				count($ticket_price_types) == count($ticket_retail_prices_tax_included) && 
				count($ticket_price_types) == count($ticket_list_prices)) {
					foreach($ticket_price_types as $index=>$ticket_price_type){
						$price_types []= [
							'ticket_price_type' => $this->_decimal_separator_converter($ticket_price_type, true),
							'ticket_price_type_age' => $ticket_price_type_ages[$index],
							'ticket_retail_price_tax_included' => $ticket_retail_prices_tax_included[$index],
							'ticket_list_price' => $ticket_list_prices[$index],
						];
					}
				}
		}
		$ticket['ticket_price_type_array'] = $price_types;
		unset($ticket['ticket_price_type']);
		unset($ticket['ticket_price_type_age']);
		unset($ticket['ticket_retail_price_tax_included']);
		unset($ticket['ticket_list_price']);

		
		//unavailable_periods
		$unavailable_periods = [];
		if ($ticket['unavailable_period']) {
			$unavailable_periods = explode(',', $ticket['unavailable_period']);
		}
		$ticket['unavailable_period_array'] = $unavailable_periods;
		unset($ticket['unavailable_period']);

		//ticket available_date_types 
		$available_date_types_array = [];
		if ($ticket['available_date_types'] && $ticket['available_time_segments']) {	// && $ticket['last_reception_time_points']
			$available_date_types = explode(',', $ticket['available_date_types']);
			$available_time_segments = explode(',', $ticket['available_time_segments']);
			$last_reception_time_points = explode(',', $ticket['last_reception_time_points']);
			if (count($available_date_types) == count($available_time_segments) && (count($available_date_types) == count($last_reception_time_points) || !$last_reception_time_points )) {
					foreach($available_date_types as $index=>$available_date_type){
						$last_reception_time_point = '';
						if ($last_reception_time_points) {
							$last_reception_time_point = $last_reception_time_points[$index];
						}
						$available_date_types_array []= [
							'available_date_type' => $available_date_type,
							'available_time_segment' => $available_time_segments[$index],
							'last_reception_time_point' => $last_reception_time_point,
						];
					}
				}
		}
		$ticket['available_date_types_array'] = $available_date_types_array;
		unset($ticket['available_date_types']);
		unset($ticket['available_time_segments']);
		unset($ticket['last_reception_time_points']);

		// ticket_id dup as link_id
		$ticket_id = $ticket['ticket_id'];
		unset($ticket['ticket_id']);
		// encode link_data in json
		$link_data = json_encode($ticket, JSON_UNESCAPED_UNICODE);
		if ($insert) {
			return array(
				$this->_db_expr_market_item_cd($item_div, $ticket_id), 'as', $ticket_id, $item_div,
				'', '',
				$geo_point, $location_lng, $location_lat,
				$ticket['ticket_name'],'',
				$link_data,
				0, 1, 
			);
		} else {	// update values array
			return $link_data;
		}
	}

	private function _process_market_asoview_plan_upsert_values($plan, $insert=true, $index=0,$raw_flg=0) {
		// process
		$item_div=15;
		$pln_cd = $plan['pln_cd'];
		// pln_cd dup as link_id
		unset($plan['pln_cd']);

		$meeting_table = $this->_asoview_activity_tables($raw_flg)[9];
		$image_table = $this->_asoview_activity_tables($raw_flg)[11];
		$genre_table = $this->_asoview_activity_tables($raw_flg)[3];
		$plan_genre_table = $this->_asoview_activity_tables($raw_flg)[8];
		//geo point of meeting place
		$geo_point = $plan['geo_point'];
		unset($plan['geo_point']);
		$location_lng = $plan['location_lng'];
		unset($plan['location_lng']);
		$location_lat = $plan['location_lat'];
		unset($plan['location_lat']);

		//meeting place nested object
		$meeting = [
			'pln_cd' => $pln_cd,
			'mtg_place_flg' => $plan['mtg_place_flg'],
			'mtg_place' => $plan['mtg_place'],
			'are_cd' => $plan['are_cd'],
			'zip' => $plan['zip'],
			'location' => $plan['location'],
		];
		$plan['meeting'] = $meeting;
		unset($plan['mtg_place_flg']);
		unset($plan['mtg_place']);
		unset($plan['are_cd']);
		unset($plan['zip']);
		unset($plan['location']);
		if (isset($plan['v_geo'])) {
			$meeting ['geo_text'] = $plan['v_geo'];
			unset($plan['v_geo']);
		}

		// plan details including meeting places and images
		$detail_plan_sql = "SELECT 
			(SELECT JSON_ARRAYAGG(JSON_OBJECT('image_flg', image_flg, 'image_index', image_index, 'link', link,  'image_text', image_text)) 
				FROM $image_table WHERE pln_cd = $pln_cd) AS images,
			(SELECT JSON_ARRAYAGG(JSON_OBJECT('genre_id', LPAD( genre.genre_id, 4, '0'), 'genre_name', genre.genre_name))
				FROM $plan_genre_table AS m_genre 
				JOIN $genre_table AS genre ON m_genre.genre_id=genre.genre_id 
				WHERE m_genre.pln_cd = $pln_cd) AS genres, 
			(SELECT JSON_ARRAYAGG(JSON_OBJECT('mtg_place_flg', mtg_place_flg, 
					'mtg_place', mtg_place, 
					'are_cd', are_cd, 
					'zip', zip, 
					'location', location, 
					'location_lng', ST_X(geo_point), 'location_lat', ST_Y(geo_point)
					)
				) FROM $meeting_table
				WHERE pln_cd = $pln_cd
			) AS places
			";
		$detail_query = DB::query(Database::SELECT, $detail_plan_sql);
		$detail_results = $detail_query->execute();
		$plan_details = $detail_results->as_array();
		if ($plan_details) {
			$plan_detail = $plan_details[0];
		}

		//places
		$places = json_decode($plan_detail['places']);
		$plan['places'] = $places;

		//images
		$images = json_decode($plan_detail['images']);
		$plan['images'] = $images;

		//genres
		$genres = json_decode($plan_detail['genres']);
		$plan['genres'] = [];
		$plan['genre_ids'] = [];
		if ( $genres ) {
			foreach($genres as $genre) {
				$plan['genres'] []= $genre->genre_name;
				$plan['genre_ids'] []= $genre->genre_id;
			}
		}

		if (!$insert) { //update plan link_data
			return $plan;
		} else { // insert new plan
			return array(
				$this->_db_expr_market_item_cd($item_div, $pln_cd), 'as', $pln_cd, $item_div,
				'', '',
				$geo_point, $location_lng, $location_lat,
				$plan['title'],'',
				$plan,
				0, 1, );
		}
	}

	private function _db_expr_market_item_cd($item_div, $link_id) {
		return 'AS'.$item_div.$link_id;
	}

	function generate_recent_week_array() {
        $recent_week_days = [];
        $period = new DatePeriod(
            new DateTime(),
            new DateInterval('P1D'),
            6   
        );
        foreach ($period as $dt) {
            $recent_week_days[] = $dt->format("Y-m-d");
        }
        return $recent_week_days;
    }

	/**
	 * @param string[] $tsv_row fee_tsv_line
	 */
	private function _process_asoview_fee_tsv_row ( $tsv_row, &$asoview_fee_row_insert_query, $table='t_very_fee', $from_id = NULL ) {
		if ( count($tsv_row) > 2) { // tsv row contains plan code and date month
			$plan_code = str_replace('pln', '', $tsv_row[0]);
			$inserting_count = 0;

			if ($plan_code && $from_id && $plan_code < $from_id) {	// skip until plan from_id
				return $inserting_count;
			}

			$plan_date_month=date_create_from_format("Ym", $tsv_row[1]);
			for ($i = 1; $i <= 31; $i++) {
				$index = $i + 1;
				$fee = $tsv_row[$index];
				if ($fee) {
					$date_day = $plan_date_month->format('Y-m-'.$i);
					$asoview_fee_row_insert_query
						->values(array(
							$plan_code,
							$date_day,
							$fee
						));
						$inserting_count ++;
				}
			}
			return $inserting_count;
		}
	}

	/**
	 * @param string[] $tsv_row fee_tsv_line
	 */
	private function _process_market_price_mtx_tsv_row ( $tsv_row, &$asoview_market_item_price_insert_query, $plan_item_mapping, &$plan_recent_prices, $table='t_market_item_price_mtx' ) {
		if ( count($tsv_row) > 2) { // tsv row contains plan code and date month
			$plan_code = str_replace('pln', '', $tsv_row[0]);
			$inserting_count = 0;
			if ( !$plan_code || !isset($plan_item_mapping[$plan_code])) {	// skip if plan>item not mapped
				return $inserting_count;
			}
			$item_id = $plan_item_mapping[$plan_code];// get item_id from plan code;

			if ( !isset($plan_recent_prices['item_id']) || $plan_recent_prices['item_id'] != $item_id ) {//next item_id
				if ( isset($plan_recent_prices['item_id']) && $plan_recent_prices['item_id'] 
					// also update json(even empty) for recent fee info in case of last few days in the end of year within available data set for last plan entry
					&& ( !isset($plan_recent_prices['updated']) || !$plan_recent_prices['updated'])
					) {
					$asoview_market_fee_update_query = DB::update($this->_refined_very_market_table)
						->where('item_cd', 'LIKE', 'AS%')
						->where('link_type_cd', '=', 'as')
						->where('item_div', '=', 15)
						->where('item_id', '=', $plan_recent_prices['item_id'])
						;

					$json_fee = json_encode($plan_recent_prices);
					$asoview_market_fee_update_query
						->value('upd_time', date('Y-m-d H:i:s', time()))
						->value('link_data', DB::Expr("JSON_SET(link_data, '$.fee', JSON_EXTRACT('$json_fee', '$'))"))
						;
					$rows_updated = $asoview_market_fee_update_query->execute();
					// reset after updated into link_data
					unset($plan_recent_prices['recent_min']);
					$plan_recent_prices['recent'] = [];
				}
				$plan_recent_prices['item_id'] = $item_id;
				unset($plan_recent_prices['updated']);
			}

			$plan_date_month=date_create_from_format("Ym", $tsv_row[1]);

			$today = date("Y-m-d");
			$week_later = date("Y-m-d", strtotime("+7 day"));

			for ($i = 1; $i <= 31; $i++) {
				$index = $i + 1;
				$fee = $tsv_row[$index];
				if ($fee) {
					$date_day = $plan_date_month->format('Y-m-'.str_pad($i, 2, '0', STR_PAD_LEFT));
					if ($date_day >= $today && $date_day < $week_later) {
						// fill in plan recent prices
						if ( ! isset($plan_recent_prices['recent_min']) || $plan_recent_prices['recent_min'] > $fee) {
							$plan_recent_prices['recent_min'] = $fee;
						}
						$plan_recent_prices['recent'][$date_day]= $fee;
					} else if ($date_day >= $week_later) { 	
						// if (isset($plan_recent_prices['recent_min']) || $plan_recent_prices['recent']) {　
						// also update empty json for recent fee info
						if ( !isset($plan_recent_prices['updated']) || !$plan_recent_prices['updated']) {
							// update and insert into link_data
							$asoview_market_fee_update_query = DB::update($this->_refined_very_market_table)
								->where('item_cd', 'LIKE', 'AS%')
								->where('link_type_cd', '=', 'as')
								->where('item_div', '=', 15)
								->where('item_id', '=', $item_id)
								;

							unset($plan_recent_prices['item_id']);
							$json_fee = json_encode($plan_recent_prices);
							$asoview_market_fee_update_query
								->value('upd_time', date('Y-m-d H:i:s', time()))
								->value('link_data', DB::Expr("JSON_SET(link_data, '$.fee', JSON_EXTRACT('$json_fee', '$'))"))
								;

							$rows_updated = $asoview_market_fee_update_query->execute();
							if (!$rows_updated) {	
								$this->write_log("market item $item_id failed to update recent fee");
							}

							// reset after updated into link_data
							unset($plan_recent_prices['recent_min']);
							$plan_recent_prices['item_id'] = $item_id;
							$plan_recent_prices['updated'] = true;
							$plan_recent_prices['recent'] = [];
						}
					}

					$asoview_market_item_price_insert_query
						->values(array(
							$item_id,
							$date_day,
							$fee
						));
						$inserting_count ++;
				}
			}
			return $inserting_count;
		}
	}

	private function _asoview_load_data_local_infile_tsv ($filepath, $table, $columns_set, $fields_terminated_by='\t', $fields_enclosed_by='"', $ignore_lines=0){
		$ignore = $ignore_lines ? " IGNORE $ignore_lines LINES" : "";
		$enclosed = $fields_enclosed_by ? " ENCLOSED BY '$fields_enclosed_by'" : "";
		$mysql_query = "LOAD DATA LOCAL INFILE '$filepath' INTO TABLE $table FIELDS TERMINATED BY '$fields_terminated_by' $enclosed $ignore $columns_set;";
		$Database_QUERY_TYPE_STATEMENT = 5;
		$this->write_log($mysql_query);
		DB::query($Database_QUERY_TYPE_STATEMENT, $mysql_query)->execute();
	}

	private function _asoview_clear_data_from_tables($raw_flg=0) {	//TODO only clear raw tables?
		//clear market table
		if (!$raw_flg) {
			DB::delete($this->_asoview_very_market_table)->execute();
		}
		//clear ticket table
		DB::delete($this->_asoview_ticket_table[$raw_flg])->execute();
		//clear activity tables
		foreach($this->_asoview_activity_tables($raw_flg) as $asoview_activity_table) {
			if ( $asoview_activity_table ) {
				DB::delete($asoview_activity_table)->execute();
			}
		}
	}

	private function _read_tsv ($tsv_file, $plan_item_mapping=NULL, $table=NULL, $from_id=NULL, $compressed_in_gz=false) {
		$tsv_stream = fopen($tsv_file,'r');
		if ($compressed_in_gz) {
			$tsv_stream = gzopen($tsv_file,'r');
		} 
		$rows_inserted = 0;

		$checkedBOM = false;
		$isJIS = '';
		if ($tsv_stream) {
			$line_count = 0;
			$time_start = time();
			$asoview_fee_row_insert_query = DB::insert($table, array('pln_cd', 'date', 'fee'));
			// market item price matrix
			if ($plan_item_mapping && $table == 't_market_item_price_mtx') {
				$asoview_fee_row_insert_query = DB::insert($table, array('item_id', 'date', 'price'));
			}
			$batch_row_count = 0;
			$plan_recent_prices = ['recent'=>[]];
			while (($tsv_line = fgets($tsv_stream)) !== false) {
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($tsv_line, 'SJIS-mac') ) {
						$tsv_line = mb_convert_encoding($tsv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($tsv_line, 'SJIS-win') ) {
						$tsv_line = mb_convert_encoding($tsv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($tsv_line, 'SJIS') ) {
						$tsv_line = mb_convert_encoding($tsv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$checkedBOM = true;
				} else if ($isJIS) {
					$tsv_line = mb_convert_encoding($tsv_line, 'UTF-8', $isJIS);
				}
				$tsv_row = str_getcsv($tsv_line	, "\t");	//	, "\t"
				$line_count ++;	
				if ($table && ($table == 't_market_item_price_mtx' || $table == 't_very_fee' || $table == 't_asoview_fee' || $table == 'z_asoview_fee_refined')) {
					if ($plan_item_mapping && $table == 't_market_item_price_mtx') {
						$fee_inserted_for_line = $this->_process_market_price_mtx_tsv_row($tsv_row, $asoview_fee_row_insert_query, $plan_item_mapping, $plan_recent_prices, $table);
					} else {
						$fee_inserted_for_line = $this->_process_asoview_fee_tsv_row($tsv_row, $asoview_fee_row_insert_query, $table, $from_id);
					}
					if ( $batch_row_count + $fee_inserted_for_line > 500 ) {	//999
						$asoview_fee_row_insert_query->execute();
						$asoview_fee_row_insert_query = DB::insert($table, array('pln_cd', 'date', 'fee'));
						if ($plan_item_mapping && $table == 't_market_item_price_mtx') {
							$asoview_fee_row_insert_query = DB::insert($table, array('item_id', 'date', 'price'));
						}
						$rows_inserted += $batch_row_count;
						$batch_row_count = $fee_inserted_for_line;
					} else {
						$batch_row_count += $fee_inserted_for_line;
					}
				} 
				if ($rows_inserted && $line_count % 10000 == 0) {
					$info = "Till now $rows_inserted records(accumulated) inserted to $table , seconds lapsed ".(time()-$time_start);
					$this->write_log($info);
				}
			}
			
			if ( $batch_row_count  > 0 ) {
				$asoview_fee_row_insert_query->execute();
				$rows_inserted += $batch_row_count;
				$info = "Till now $rows_inserted records(accumulated) inserted to $table , seconds lapsed ".(time()-$time_start);
				$this->write_log($info);
			}
		}
	}

	private function _extract_gz_compressed_file ($file_name, $extension='.tsv') {
		// $target_ticket_gz_file = '../cache/asoview_tickets_20231030.gz';
		// $file_name = $target_ticket_gz_file;
		// Raising this value may increase performance
		$buffer_size = 4096; // read 4kb at a time
		$out_file_name = str_replace('.gz', $extension, $file_name); 

		// Open our files (in binary mode)
		$file = gzopen($file_name, 'rb');
		$out_file = fopen($out_file_name, 'wb'); 

		// Keep repeating until the end of the input file
		$bytes_written = 0;
		while (!gzeof($file)) {
			// Read buffer-size bytes
			// Both fwrite and gzread and binary-safe
			$bytes_written += fwrite($out_file, gzread($file, $buffer_size));
			if (! $bytes_written) {
				$this->write_log("$file_name failed to written");
			}
		}
		if ($bytes_written) {
			$info = "$file_name bytes written: $bytes_written";
			$this->write_log($info);

		} else {
			$warning = "$file_name failed to written";
			$this->write_log($warning);
		}
		// Files are done, close files
		fclose($out_file);
		gzclose($file);

		// delete gz file on success/failure
		unlink($file_name);
		return $bytes_written;
	}

	private function _extract_tar_gz_compressed_folder ($file_path, $target_folder) {
		try {
			$decompressed = str_replace(".gz", "", $file_path);
			// delete existing decompressed tar file
			if (file_exists($decompressed)) {
				unlink($decompressed);
				$debug = "cleaned $decompressed before decompressing";
				$this->write_log($debug);
			}
			$phar = new PharData($file_path);
			$phar_decompressed = $phar->decompress(); // creates /path/to/my

			// delete existing target folder and tsv files inside
			if (is_dir($target_folder)) {
				if ($dh = opendir($target_folder)) {
					$debug = "cleaning before decompressing... ";
					$this->write_log($debug);
					while (($tsv_file = readdir($dh)) !== false ) {
						if (($tsv_file != ".") && ($tsv_file != "") && ($tsv_file != "..")) {
							$debug = "cleaning filename: $tsv_file : filetype: " . filetype($target_folder . $tsv_file);
							$this->write_log($debug);
							unlink($target_folder . $tsv_file);
						} else {
							$debug = "skipping filename: $tsv_file : filetype: " . filetype($target_folder . $tsv_file);
						}
					}
					closedir($dh);
					rmdir($target_folder);
				}
			}
			// unarchive from the tar
			$extracted =  $phar_decompressed->extractTo($target_folder); // extract all files
			unlink($file_path);
			unlink($decompressed);
			return $extracted;
		} catch (Exception $e) {
			$error = "$file_path failed to extract to $target_folder".PHP_EOL.$e->getMessage();
			/**
			 * TODO improve:do more to handle errors?
			 * 1.
			 * phar "/data/dev8/files/temp/asoview/activalues_20240325.tar" exists and must be unlinked prior to conversion
			 * 2.
			 * Extraction from phar "/data/dev8/files/temp/asoview/activalues_20240325.tar" failed: Cannot extract "./activalues_20240325_1_1_category_group.tsv" to "/data/dev8/api-mgr/application/../../files/temp/asoview/202403/activity//activalues_20240325_1_1_category_group.tsv", path already exists
			 */ 
			Log::instance()->add(Log::ERROR, $error);
			$this->write_log($error);
			unlink($file_path);
			return false;
		}
	}

}