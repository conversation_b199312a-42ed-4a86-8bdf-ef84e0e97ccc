<?php

defined('SYSPATH') or die('No direct script access.');

//include DOCROOT. '/application/class/model/simple_html_dom.php';

class CLI_Tool extends CLI_BaseCli
{
    public $_div = 'tool';

    public function __construct()
    {
        parent::__construct();
    }

    public function inquirycanceltime()
    {
        $sql = "SELECT * FROM t_inquiry_result WHERE status_cd = '03'";
        $query = DB::query(Database::SELECT, $sql);
        $results = $query->execute()->as_array();
        foreach ($results as $r) {
            if ($r['upd_time'] === null) continue;
            if ($r['upd_user'] === null) {
                $result_data = json_decode($r['result_data'], true);
                if (isset($result_data['error_code'])) {
                    continue;
                }
                $c = DB::update('t_inquiry_result')->set(['cancel_time'=>$r['upd_time']])->where('id', '=', $r['id'])->execute();
            } else {
                if ($r['upd_user'] == 0 ) continue;
                $c = DB::update('t_inquiry_result')->set(['cancel_time'=>$r['upd_time']])->where('id', '=', $r['id'])->execute();
            }
        }
    }

    public function slacksetting()
    {
        DB::delete('t_bot_setting')->where('setting_cd', '=', 'json_slack_setting')->execute();
        $sql = "SELECT *  FROM t_bot_setting WHERE setting_cd = 'txt_slack_channel_url'";
        $query = DB::query(Database::SELECT, $sql);
        $results = $query->execute()->as_array();
        foreach ($results as $r) {
            try {
                $orm = ORM::factory('botsetting');
                $orm->bot_id = $r['bot_id'];
                $orm->setting_cd = 'json_slack_setting';
                $orm->setting_value = json_encode(['default'=>['channel_url' => $r['setting_value']]], JSON_UNESCAPED_UNICODE);
                $orm->delete_flg = $r['delete_flg'];
                $orm->upd_user = $r['upd_user'];
                $orm->upd_time = $r['upd_time'];
                $orm->save();
            } catch (Exception $e) {
            }
        }

        $sql = "SELECT t.*, d.content FROM t_bot_msg t LEFT JOIN t_bot_msg_desc_txt d ON t.msg_id=d.msg_id and d.lang_cd='ja' WHERE msg_cd like'%slack_webhook%'";
        $query = DB::query(Database::SELECT, $sql);
        $results = $query->execute()->as_array();
        foreach ($results as $r) {
            try {
                $orm = ORM::factory('botsetting')->where('bot_id', '=', $r['bot_id'])->where('setting_cd', '=', 'json_slack_setting')->find();
                if ($orm->setting_value) {
                    $json = json_decode($orm->setting_value, true);
                    $json[$r['msg_cd']] = ['channel_url'=>$r['content']];
                    $num = DB::update('t_bot_setting')->set(['setting_value'=>json_encode($json, JSON_UNESCAPED_UNICODE)])->where('bot_id', '=', $r['bot_id'])->where('setting_cd', '=', 'json_slack_setting')->execute();
                }
                else {
                    $orm = ORM::factory('botsetting');
                    $orm->bot_id = $r['bot_id'];
                    $orm->setting_cd = 'json_slack_setting';
                    $orm->setting_value = json_encode([$r['msg_cd']=>['channel_url' => $r['content']]], JSON_UNESCAPED_UNICODE);
                    $orm->upd_user = 1;
                    $orm->save();
                }
            } catch (Exception $e) {
            }
        }
    }
    
    public function init_inquiry_seq()
    {
        $sql = "SELECT inquiry_id, inquiry_data  FROM t_inquiry WHERE inquiry_data LIKE '%receiption_id_prefix%' and inquiry_data not like '" . '%"receiption_id_prefix":""%' .
            " and bot_id not in(17,88,99,9999,8888888,9999888,99999900) and delete_flg=0 and (end_date is null or end_date > '" . date('Y-m-d') . "') ORDER BY bot_id";
        $query = DB::query(Database::SELECT, $sql);
        $results = $query->execute()->as_array();
        foreach ($results as $r) {
            $inquiry_id = $r['inquiry_id'];
            $inquiry_data = json_decode($r['inquiry_data'], true);
            $receiption_id_prefix = strtoupper($inquiry_data['receiption_id_prefix']);
            $result = preg_match('/N{2,}/', $receiption_id_prefix, $m);
            $nformat = $m[0];
            if (!$result) {
                $this->_write_log('prefix error ' . $inquiry_id);
                continue;
            }
            $ptr = strpos($receiption_id_prefix, $nformat);
            $prefix_head = str_replace($nformat, '', $receiption_id_prefix);
            $sql = "SELECT MAX(result_cd) AS no FROM t_inquiry_result WHERE inquiry_id = :inquiry_id AND result_cd LIKE :result_cd";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':inquiry_id' => $inquiry_id,
                ':result_cd' => $prefix_head . '%'
            ));
            $results = $query->execute()->as_array();
            if ($results[0]['no'] == NULL) {
                $no = 0;
            } else {
                $no = intval(substr($results[0]['no'], $ptr, strlen($nformat)));
            }
            $orm = ORM::factory('sequence');
            $orm->app_div = 1;
            $orm->id = $inquiry_id;
            $orm->seq = $no;
            $orm->save();
        }
    }

    public function botsurveyfix()
    {
        $sql = "SELECT bot_id, setting_value FROM t_bot_setting WHERE setting_cd='survey_flg'";
        $query = DB::query(Database::SELECT, $sql);
        $settings = $query->execute()->as_array('bot_id', 'setting_value');
        foreach ($settings as $bot_id => $value) {
            if ($value == 1) continue;
            $grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
            if ($grp_bot_id > 0) {
                $survey_flg = $this->_model->get_bot_setting($grp_bot_id, 'survey_flg');
                if ($survey_flg == 0) continue;
                $template_bot_id = $this->_model->get_bot_setting($bot_id, 'template_bot');
                $survey_flg = $this->_model->get_bot_setting($template_bot_id, 'survey_flg');
                if ($survey_flg == 0) continue;
                DB::delete('t_bot_setting')->where('bot_id', '=', $bot_id)->where('setting_cd', '=', 'survey_flg')->execute();
                $this->_write_log("botsurveyfix bot_id=$bot_id");
            }
        }
    }

    public function botsurveydefault()
    {
        $sql = "SELECT bot_id, setting_value FROM t_bot_setting WHERE setting_cd='survey_flg'";
        $query = DB::query(Database::SELECT, $sql);
        $settings = $query->execute()->as_array('bot_id', 'setting_value');
        $bots = ORM::factory('bot')->find_all();
        foreach ($bots as $bot) {
            if (!isset($settings[$bot->bot_id])) {
                $orm = ORM::factory('botsetting');
                $orm->bot_id = $bot->bot_id;
                $orm->setting_cd = 'survey_flg';
                $orm->setting_value = 0;
                $orm->save();
            }
        }
        DB::update('t_bot_setting')->set(['setting_value' => 1])->where('bot_id', '=', 0)->where('setting_cd', '=', 'survey_flg')->execute();
    }

    public function asoview_class_code()
    {

        $sql = "SELECT * FROM z_asoview_category_group ORDER BY category_group_id";
        $query = DB::query(Database::SELECT, $sql);
        $catetory_group = $query->execute()->as_array();

        $sql = "SELECT * FROM z_asoview_category ORDER BY category_group_id, category_id";
        $query = DB::query(Database::SELECT, $sql);
        $catetory = $query->execute()->as_array();

        $sql = "SELECT * FROM z_asoview_category_genre ORDER BY category_id, genre_id";
        $query = DB::query(Database::SELECT, $sql);
        $catetory_genre = $query->execute()->as_array();

        $sql = "SELECT * FROM z_asoview_genre";
        $query = DB::query(Database::SELECT, $sql);
        $genre = $query->execute()->as_array('genre_id', 'genre_name');

        $div = 999915;
        $p = 1;
        foreach ($catetory_group as $r) {
            $orm = ORM::factory('classcode');
            $orm->code_div = $div;
            $orm->class_cd = sprintf('%02d', $p);
            $orm->lang_cd = 'ja';
            $orm->name = $r['category_group_name'];
            $orm->word = $r['category_group_id'];
            $orm->sort = $p++;
            $orm->parent_cd = '';
            $orm->save();
        }

        $p = 1;
        $cd = '';
        foreach ($catetory as $r) {
            if ($cd != $r['category_group_id']) {
                $cd = $r['category_group_id'];
                $p = 1;
            }
            $parent = ORM::factory('classcode')->where('code_div', '=', $div)->where('word', '=', $r['category_group_id'])->where('parent_cd', '=', '')->where('lang_cd', '=', 'ja')->find();
            $orm = ORM::factory('classcode');
            $orm->code_div = $div;
            $orm->class_cd = $parent->class_cd . sprintf('%02d', $p);
            $orm->lang_cd = 'ja';
            $orm->name = $r['category_name'];
            $orm->word = $r['category_id'];
            $orm->sort = $p++;
            $orm->parent_cd = $parent->class_cd;
            $orm->save();
        }

        $p = 1;
        $cd = '';
        foreach ($catetory_genre as $r) {
            if ($cd != $r['category_id']) {
                $cd = $r['category_id'];
                $p = 1;
            }
            $parent = ORM::factory('classcode')->where('code_div', '=', $div)->where('word', '=', $r['category_id'])->where('parent_cd', '<>', '')->where('lang_cd', '=', 'ja')->find();
            $orm = ORM::factory('classcode');
            $orm->code_div = $div;
            $orm->class_cd = $parent->class_cd . sprintf('%02d', $p);
            $orm->lang_cd = 'ja';
            if (isset($genre[$r['genre_id']])) {
                $orm->name = $genre[$r['genre_id']];
            } else {
                $orm->name = '';
            }
            $orm->word = $r['genre_id'];
            $orm->sort = $p++;
            $orm->parent_cd = $parent->class_cd;
            $orm->save();
        }


        // area_cd
        $sql = "SELECT * FROM z_asoview_region";
        $query = DB::query(Database::SELECT, $sql);
        $region = $query->execute()->as_array();

        $sql = "SELECT * FROM z_asoview_prefecture ORDER BY rgn_cd, prf_cd";
        $query = DB::query(Database::SELECT, $sql);
        $prefecture = $query->execute()->as_array();

        $sql = "SELECT * FROM z_asoview_area ORDER BY prf_cd, are_cd";
        $query = DB::query(Database::SELECT, $sql);
        $area = $query->execute()->as_array();
        $div = 999925;
        $p = 1;
        foreach ($region as $r) {
            $orm = ORM::factory('classcode');
            $orm->code_div = $div;
            $orm->class_cd = $r['rgn_cd'];
            $orm->lang_cd = 'ja';
            $orm->name = $r['region'];
            $orm->word = $r['rgn_cd'];
            $orm->sort = $p++;
            $orm->parent_cd = '';
            $orm->save();
        }
        $p = 1;
        $rgn_cd = '';
        foreach ($prefecture as $r) {
            if ($rgn_cd != $r['rgn_cd']) {
                $rgn_cd = $r['rgn_cd'];
                $p = 1;
            }
            $orm = ORM::factory('classcode');
            $orm->code_div = $div;
            $orm->class_cd = $r['rgn_cd'] . sprintf('%02d', $p);
            $orm->lang_cd = 'ja';
            $orm->name = $r['prefecture'];
            $orm->word = $r['prf_cd'];
            $orm->sort = $p++;
            $orm->parent_cd = $r['rgn_cd'];
            $orm->save();
        }
        $p = 1;
        $prf_cd = '';
        foreach ($area as $r) {
            if ($prf_cd != $r['prf_cd']) {
                $prf_cd = $r['prf_cd'];
                $p = 1;
            }
            $parent = ORM::factory('classcode')->where('code_div', '=', $div)->where('word', '=', $r['prf_cd'])->where('lang_cd', '=', 'ja')->find();
            $orm = ORM::factory('classcode');
            $orm->code_div = $div;
            $orm->class_cd = $parent->class_cd . sprintf('%02d', $p);
            $orm->lang_cd = 'ja';
            $orm->name = $r['area_big'];
            $orm->word = $r['are_cd'];
            $orm->sort = $p++;
            $orm->parent_cd = $parent->class_cd;
            $orm->save();
        }
    }

    public function deletecsv($end_day)
    {
        $this->_write_log("======= deletecsv start! ======== ");
        $end_time = $end_day . ' 00:00:00';
        $maximum_model = new Model_Maximummodel();
        $sql = 'SELECT id FROM t_inquiry_result WHERE inquiry_id=2260040003 AND end_time=:end_time';
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':end_time' => $end_time,
        ));
        $result = $query->execute()->as_array();
        Database::instance()->begin();
        try {
            foreach ($result as $r) {
                $maximum = ORM::factory('maximumorder')->where('link_id', '=', $r['id'])->find();
                if (isset($maximum->maximum_id)) {
                    $m = ['maximum_id' => $maximum->maximum_id, 'day' => $maximum->day, 'time' => $maximum->time, 'num' => $maximum->num];
                    $maximum_model->restore_maximum_only(226004, $m);
                    DB::delete('t_maximum_order')->where('link_id', '=', $r['id'])->execute();
                }
                DB::delete('t_inquiry_result')->where('id', '=', $r['id'])->execute();
                DB::delete('t_inquiry_result_entry')->where('result_id', '=', $r['id'])->execute();
            }
            Database::instance()->commit();
        } catch (Exception $e) {
            Database::instance()->rollback();
            $this->_write_log("======= deletecsv failed! ======== ");
        }
        $this->_write_log("======= deletecsv done! ======== ");
    }
    public function refurlwhitelist($bot_id = '')
    {
        $db = 'slave';
        $result = [];
        $domains = [];

        $user_id = 1;
        $sql = 'SELECT item_id, btn1_url_sp, btn2_url_sp, btn3_url_sp FROM t_item_description';
        if ($bot_id != '') {
            $sql = $sql . ' WHERE item_id IN (SELECT item_id FROM t_item WHERE bot_id=' . $bot_id . ')';
            $user_id = 1;
        }
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            for ($i = 1; $i <= 3; $i++) {
                $this->_model->create_whitelist($r['btn' . $i . '_url_sp'], $domains, 'item', $r['item_id'], $user_id);
            }
        }
        $this->_write_log("======= item ref whitelist done! ======== ");
        $sql = 'SELECT msg_id, btn1_url_sp, btn2_url_sp, btn3_url_sp FROM t_bot_msg_desc_car';
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            for ($i = 1; $i <= 3; $i++) {
                $this->_model->create_whitelist($r['btn' . $i . '_url_sp'], $domains, 'message', $r['msg_id']);
            }
        }
        $this->_write_log("======= message ref whitelist done! ======== ");
        //return;

        /*
        $url = '[{"skill":"URL_TRANSITION","params":{"url":" http:\/\/qrsm.net\/a\/111504480","url_sp":"","lang":""}}]';
        $this->_model->create_whitelist($url, $domains, 'message', 1);
        return;
        */
        $user_id = 0;
        // faq データは多いので、言語ごとに作成
        $lang_list = ['ja', 'en', 'cn', 'tw', 'kr'];
        foreach ($lang_list as $lang_cd) {
            $sql = "SELECT intent_id, answer1, answer2, answer3, answer4, answer5, answer6, answer7, answer8, answer9 FROM t_bot_intent WHERE lang_cd='" . $lang_cd . "'";
            if ($bot_id != '') {
                $sql = $sql . ' AND bot_id=' . $bot_id;
                $user_id = 1;
            }
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute($db)->as_array();
            foreach ($result as $r) {
                for ($i = 1; $i <= 9; $i++) {
                    $this->_model->create_whitelist($r['answer' . $i], $domains, 'faq', $r['intent_id'], $user_id);
                }
            }
        }
        $this->_write_log("======= faq ref whitelist done! ======== ");

        $sql = 'SELECT item_id, btn1_url, btn2_url, btn3_url, url FROM t_item_description';
        if ($bot_id != '') {
            $sql = $sql . ' WHERE item_id IN (SELECT item_id FROM t_item WHERE bot_id=' . $bot_id . ')';
            $user_id = 1;
        }
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            for ($i = 1; $i <= 3; $i++) {
                $this->_model->create_whitelist($r['btn' . $i . '_url'], $domains, 'item', $r['item_id'], $user_id);
            }
            $this->_model->create_whitelist($r['url'], $domains, 'item', $r['item_id'], $user_id);
        }
        $this->_write_log("======= item ref whitelist done! ======== ");
        // bot_id指定すれば、faq/itemのみ作成する
        if ($bot_id != '') {
            return;
        }
        $sql = 'SELECT product_id, btn1_url, btn2_url, btn3_url, url FROM t_product_description';
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            for ($i = 1; $i <= 3; $i++) {
                $this->_model->create_whitelist($r['btn' . $i . '_url'], $domains, 'product', $r['product_id']);
            }
            $this->_model->create_whitelist($r['url'], $domains, 'product', $r['product_id']);
        }
        $this->_write_log("======= product ref whitelist done! ======== ");

        $sql = 'SELECT msg_id, btn1_url, btn2_url, btn3_url, url FROM t_bot_msg_desc_car';
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            for ($i = 1; $i <= 3; $i++) {
                $this->_model->create_whitelist($r['btn' . $i . '_url'], $domains, 'message', $r['msg_id']);
            }
            $this->_model->create_whitelist($r['url'], $domains, 'message', $r['msg_id']);
        }
        $sql = 'SELECT msg_id, url FROM t_bot_msg_desc_lst';
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            $this->_model->create_whitelist($r['url'], $domains, 'message', $r['msg_id']);
        }
        $sql = 'SELECT msg_id, url FROM t_bot_msg_desc_img';
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            $this->_model->create_whitelist($r['url'], $domains, 'message', $r['msg_id']);
        }
        $this->_write_log("======= message ref whitelist done! ======== ");

        $sql = 'SELECT intent_cd, skill FROM t_intent_skill';
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute($db)->as_array();
        foreach ($result as $r) {
            $this->_model->create_whitelist($r['skill'], $domains, 'skill', $r['intent_cd']);
        }
        $this->_write_log("======= skill ref whitelist done! ======== ");
    }
}
