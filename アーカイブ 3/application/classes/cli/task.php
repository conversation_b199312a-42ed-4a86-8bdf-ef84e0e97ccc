<?php defined('SYSPATH') or die('No direct script access.');

define("DEBUG", "DEBUG");
define("INFO", " INFO");
define("WARN", " WARN");
define("ERROR", "ERROR");

class CLI_Task {

	public $_model;
	public $_log_file;

	private $_business_task_functions = [
		'05'=>[
			'class'=>'Newsletter'
		],
		'06'=>[
			'class'=>'Linerichmenuupdate'
			]
	];

	public function __construct()
	{
		$this->_model = new Model_Basemodel();
	}
	
	public function _call_method($params)
	{
		$method = array_shift($params);
		return call_user_func_array(array($this, $method), $params);
	}

	private function _call_business_func($task_type_cd, $task) {
		
		$business_task_func = $this->_business_task_functions[$task_type_cd];
		$business_task_class_name = 'CLI_'.$business_task_func['class'];

		$business_task = new $business_task_class_name();
		$method = '_call_method';
		$params = json_decode($task->task_data, true);
		return call_user_func_array(array($business_task, $method), $params);
	}

	private function _log($level, $content, $bot_id=0) {
		$f = fopen($this->_log_file, 'a');
		fwrite($f, date('Y-m-d H:i:s') . ' ' . $level . ' ' . $content . PHP_EOL);
		fclose($f);
		echo($content);
		if ($level == INFO) $this->_model->log_info('task', $content, $bot_id);
		if ($level == WARN) $this->_model->log_warning('task', $content, $bot_id);
		if ($level == ERROR) $this->_model->log_error('task', $content, $bot_id);
		if ($level == DEBUG) $this->_model->log_debug('task', $content, $bot_id);
	}

	private function _update_task($task, $task_status_cd) {
		$task->task_status_cd = $task_status_cd;
		if ($task_status_cd == '02') {
			$task->process_time = date('Y-m-d H:i:s');
		}
		else {
			$task->finish_time = date('Y-m-d H:i:s');
		}
		$task->save();
	}


	/*
		task_type_cd
			01:全体送信
			02:個別送信
			03:CSV出力
			04:バッチ実行
			05:メルマガ
			06: Linerichmenuupdate
			07: 非同期バッチ実行
		task_status_cd
			'00' => '仮登録',
			'01' => '新規',
			'02' => '実行中',
			'03' => '完了（成功）',
			'04' => '完了（警告あり）',
			'05' => '完了（失敗）',
		repeat_cd
			'00' => '一回のみ',
			'01' => '毎日',
	*/
	public function execute($task_type_cd='') {
		$batch = new CLI_Batch();
		$timestamp = str_replace(['-', ' ', ':'], '', date('Y-m-d', time()));
		$this->_log_file = APPPATH . "../../files/log/task_" . $timestamp . ".log";
		$this->_log(INFO, __FUNCTION__ . " start.");
		// tasks
		$this->_log(INFO, 'task_type_cd=' . $task_type_cd);
		if ($task_type_cd == '') {
			$tasks = ORM::factory('task')->where('task_status_cd', '=', '01')->find_all();
		}
		else {
			$tasks = ORM::factory('task')->where('task_status_cd', '=', '01')->where('task_type_cd', '=', $task_type_cd)->find_all();
		}
		$this->_log(INFO, 'tasks count=' . count($tasks));
		foreach($tasks as $task) {
			$this->_log(INFO, 'task_id=' . $task->task_id. 'task_name='. $task->task_name.  ' task_type_cd=' . $task->task_type_cd . ' repeat_cd=' . $task->repeat_cd . ' scheduled_time=' . $task->scheduled_time. ' task_status_cd=' . $task->task_status_cd, $task->bot_id);
			$task = ORM::factory('task', $task->task_id);
			$this->_log(INFO, 'task_id=' . $task->task_id. ' task_type_cd=' . $task->task_type_cd . ' repeat_cd=' . $task->repeat_cd . ' scheduled_time=' . $task->scheduled_time. ' task_status_cd=' . $task->task_status_cd);
			if ($task->task_status_cd != '01') {
				$this->_log(INFO, $task->task_name . ' status_cd !== 01', $task->bot_id);
				continue;
			}
			if ($task->task_type_cd == '01') {
				$this->_log(INFO, $task->task_name . ' task_type_cd == 01', $task->bot_id);
				continue;
			}
			if ($task->task_type_cd == '02') {
				$this->_log(INFO, $task->task_name . ' task_type_cd == 02', $task->bot_id);
				continue;
			}
			if ($task->repeat_cd != '00') {
				$this->_log(INFO, $task->task_name . ' repeat_cd != 00', $task->bot_id);
				continue;
			}
			if ($task->scheduled_time > date('Y-m-d H:i:s')) {
				$this->_log(INFO, $task->task_name . ' scheduled_time > now', $task->bot_id);
				continue;
			}

			// parse task data 
			$param = json_decode($task->task_data, true);
			if (!is_array($param)) {
				$this->_log(ERROR, $task->task_name . ':' . $task->task_data . '(タスクパラメータ不正)', $task->bot_id);
				$this->_update_task($task, '05');
				continue;
			}

			// execute
			try {
				$this->_log(INFO, $task->task_name . ' start.', $task->bot_id);
				// csv
				if ($task->task_type_cd == '03') {
					$this->_update_task($task, '02');
					$sql = $param['sql'];
					if (preg_match("/update/i", $sql) || preg_match("/delete/i", $sql) || preg_match("/insert/i", $sql)) {
						$this->_log(ERROR, $task->task_name . ':' . $task->task_data . '(危険なタスクパラメータ)', $task->bot_id);
						$this->_update_task($task, '05');
						continue;
					}
					$query = DB::query(Database::SELECT, $sql);
					$results = $query->execute('slave')->as_array();
					mkdir(APPPATH . "../../files/task/". $task->task_id);
					$filename = APPPATH . "../../files/task/". $task->task_id . "/" . time() . '.csv';
					$f = fopen($filename, 'w');
					fwrite($f, "\xEF\xBB\xBF");
					if (array_key_exists('header', $param)) {
						fputcsv($f, explode(',', $param['header']), ",");
					}
					foreach($results as $r) {
						fputcsv($f, $r, ",");
					}
					fclose($f);
					$this->_update_task($task, '03');
				}
				// batch
				else if ($task->task_type_cd == '04' && $task->repeat_cd == '00') {
					$this->_update_task($task, '02');
					$params = explode('-', $param['cmd']);
					$method = array_shift($params);
					call_user_func_array([$batch, $method], $params);
					if ($task->repeat_cd == '00') {
						$this->_update_task($task, '03');
					}
				}

				//business functions
				else if ($task->task_type_cd >= '05' && isset($this->_business_task_functions[$task->task_type_cd]) && $task->repeat_cd == '00') {
					$this->_log(DEBUG, 'starting registed task into id: '.$task->task_id, $task->bot_id);

					$this->_update_task($task, '02');
					$task_status_cd = $this->_call_business_func($task->task_type_cd, $task);
					$this->_update_task($task, $task_status_cd);
				}

				// batch 
				else if ($task->task_type_cd == '07' && $task->repeat_cd == '00') {
					// Mark the task as "in progress"
					$this->_update_task($task, '02');
					$params = explode('-', $param['cmd']);
					$method = array_shift($params);
				
					// Build the command to run the separate PHP process.
					$arguments = escapeshellarg($method);
					foreach ($params as $p) {
						$arguments .= ' ' . escapeshellarg($p);
					}
					// Append the task_id so that the child process can update the task.
					$arguments .= ' ' . escapeshellarg($task->task_id);
					// The command runs in the background (the trailing '&') so it returns immediately.
					$command = "php cli.php taskbatchrunner/execute/" . $task->task_id . " >> " . APPPATH . "../../files/log/task_" . $timestamp . ".log 2>&1 &";
					exec($command);
				}

				/*
				$now_date = date('Y-m-d H:i:s');
				$now_date = date('Y-m-d') . ' 00:00:00';
				if ($task->repeat_cd == '00') {
					if ($task->scheduled_time >= date("Y-m-d", strtotime("+1 day")) . ' 00:00:00') continue;
				}
				*/
				$this->_log(INFO, $task->task_name . ' end.', $task->bot_id);
			}
			catch(Exception $e) {
				if ($task->repeat_cd == '00') {
					$this->_update_task($task, '05');
				}
				$this->_log(ERROR, $e->getMessage(), $task->bot_id);
			} catch (\Throwable $th) {
				if ($task->repeat_cd == '00') {
					$this->_update_task($task, '05');
				}
				$this->_log(ERROR, $th->getMessage(), $task->bot_id);
			}
		}
		$this->_log(INFO, __FUNCTION__ . " end.");
	}

	public function itemreminder($task_type_cd='') {
		$timestamp = str_replace(['-', ' ', ':'], '', date('Y-m-d', time()));
		$this->_log_file = APPPATH . "../../files/log/task_" . $timestamp . ".log";
		$this->_log(INFO, __FUNCTION__ . " start.");
		$tasks = ORM::factory('itemreminder')->where('upd_user', '!=', 0)->find_all();
		$admin_url = $this->_model->get_env('admin_url');
		foreach($tasks as $task) {
			$task = ORM::factory('itemreminder', $task->id);
			if ($task->remind_time > date('Y-m-d H:i:s')) continue;
			try {
				$task->upd_user = 0;
				$task->upd_time = date('Y-m-d H:i:s');
				$task->save();
				$param = ['bot_id'=>$task->bot_id];
				$param['message_cd'] = 'item_remind_mail_' . $task->item_div;
				$param['params'] = json_decode($task->mail_params, true);
				// FAQ
				if ($task->item_div == 0) {
					$faq = ORM::factory('botintent')->where('intent_id', '=', $task->item_id)->where('lang_cd', '=', $task->lang_cd)->find();
					$bot = ORM::factory('bot', $task->bot_id);
					if (isset($faq->bot_id)) {
						if ($faq->facility_question_title == '') {
							// ->where('intent_type_cd', '=', $faq->intent_type_cd)
							$m = ORM::factory('botintentmst')->where('intent_class_cd', '=', $bot->bot_class_cd)->where('intent_cd', '=', $faq->intent_cd)->where('sub_intent_cd', '=', $faq->sub_intent_cd)->where('lang_cd', '=', $task->lang_cd)->find();
							if (isset($m->faq_id)) {
								$param['params']['faq_title'] = $m->question;
							}
						}
						else {
							$param['params']['faq_title'] = $faq->facility_question_title;
						}
						$param['params']['remind_time'] = $task->remind_time;
						$param['params']['url'] = $admin_url . 'admin/talknew?facility_cd=' . $bot->facility_cd . '&cd=' . $faq->intent_cd . '&sub_cd=' . $faq->sub_intent_cd . '&area_cd=' . $faq->area_cd . '&lang=' . $task->lang_cd;
					}
				}
				// コンテンツ
				if ($task->item_div == 1 || $task->item_div == 2 || $task->item_div == 3) {
					// 施設コンテンツ,広域コンテンツ,グループ施設は共通のメッセージコードを使用する
					$param['message_cd'] = 'item_remind_mail_1';
					$item_desc = ORM::factory('itemdescription')->where('item_id', '=', $task->item_id)->where('lang_cd', '=', $task->lang_cd)->find();
					if (isset($item_desc->item_id)) {
						$param['params']['item_name'] = $item_desc->item_name;
						$param['params']['remind_time'] = $task->remind_time;
						$param['params']['url'] = $admin_url . 'admin//item?id='. $task->item_id;
					}
				}
				// 枠
				if ($task->item_div == 12) {
					$maximum = ORM::factory('botmaximum')->where('bot_id', '=', $task->bot_id)->where('id', '=', $task->item_id)->find();
					if (isset($maximum->id)) {
						$param['params']['maximum_name'] = $maximum->name;
						$param['params']['remind_time'] = $task->remind_time;
						$param['params']['url'] = $admin_url . 'admin/maximum?id='. $task->item_id;
					}
				}
				$param['type'] = '07'; // remind
				$param['link_id'] = $task->id;
				$mail_users = explode(',', $task->mail_users);
				$users = ORM::factory('user')->where('user_id', 'IN', $mail_users)->find_all();
				foreach($users as $user) {
					if ($user->email_flg == 0) continue;
					if ($user->chat_available_lang == '') {
						$lang_cd = key($this->_model->get_setting('admin_support_lang'));
					}
					else {
						$langs = explode(',', $user->chat_available_lang);
						$lang_cd = $langs[0];
					}
					$param['lang_cd'] = $lang_cd;
					$param['receiver'] = $user->email;
					$data = $this->_model->post_enginehook('service', 'sendmail','', $param);
					if ($data['success'] == 'False') {
						Log::instance()->add(Log::DEBUG, 'sendusermail (' . $param['message_cd'] . ') failure=' . json_encode($data));
					}
				}
			}
			catch(Exception $e) {
				$this->_log(ERROR, $e->getMessage(), $task->bot_id);
			}
		}
		$this->_log(INFO, __FUNCTION__ . " end.");
	}

}
