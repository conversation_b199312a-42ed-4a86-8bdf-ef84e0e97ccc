<?php defined('SYSPATH') or die('No direct script access.');

define("DEBUG", "DEBUG");
define("INFO", " INFO");
define("WARN", " WARN");
define("ERROR", "ERROR");
define("MINIMUM_MEMORY", 500 * 1024 * 1024); // 500MB in bytes

class CLI_Taskbatchrunner {

	public $_model;
	public $_log_file;

	public function __construct()
	{
		$this->_model = new Model_Basemodel();
		$this->_log_file = APPPATH . "../../files/log/taskbatchrunner_" . date('Y-m-d') . ".log";;
	}
	
	public function _call_method($params)
	{
		$method = array_shift($params);
		return call_user_func_array(array($this, $method), $params);
	}

	private function _check_memory()
    {
        if (PHP_OS === 'Linux') {
            $free = shell_exec('free');
            if ($free !== null) {
                $free_arr = explode("\n", $free);
                $mem = explode(" ", $free_arr[1]);
                $mem = array_filter($mem);
                $mem = array_merge($mem); // Reindex array
                $available_memory = $mem[6] * 1024; // Convert KB to bytes
                
                if ($available_memory < MINIMUM_MEMORY) {
                    $this->_log(ERROR, "Available memory below threshold: " . round($available_memory/1024/1024, 2) . "MB remaining", 0);
                    return false;
                }
                return true;
            }
        }
        
        // Fallback for non-Linux systems or if free command fails
        $memory_info = array();
        if (PHP_OS === 'Windows') {
            $cmd = "wmic OS get FreePhysicalMemory /Value";
            @exec($cmd, $memory_info);
            foreach ($memory_info as $value) {
                if (strpos($value, "FreePhysicalMemory") !== false) {
                    $available_memory = (int)filter_var($value, FILTER_SANITIZE_NUMBER_INT) * 1024; // Convert KB to bytes
                    if ($available_memory < MINIMUM_MEMORY) {
                        $this->_log(ERROR, "Available memory below threshold: " . round($available_memory/1024/1024, 2) . "MB remaining", 0);
                        return false;
                    }
                    return true;
                }
            }
        }
        
        // If we can't determine free memory, log a warning and return true to allow execution
        $this->_log(WARN, "Could not determine available system memory. Proceeding with caution.", 0);
        return true;
    }

	private function _convert_to_bytes($memory_limit)
    {
        $unit = strtoupper(substr($memory_limit, -1));
        $value = (int)substr($memory_limit, 0, -1);
        
        switch ($unit) {
            case 'G':
                $value *= 1024;
            case 'M':
                $value *= 1024;
            case 'K':
                $value *= 1024;
        }
        
        return $value;
    }

	private function _log($level, $content, $bot_id=0) {
		$f = fopen($this->_log_file, 'a');
		fwrite($f, date('Y-m-d H:i:s') . ' ' . $level . ' ' . $content . PHP_EOL);
		fclose($f);
		echo($content);
		if ($level == INFO) $this->_model->log_info('task', $content, $bot_id);
		if ($level == WARN) $this->_model->log_warning('task', $content, $bot_id);
		if ($level == ERROR) $this->_model->log_error('task', $content, $bot_id);
		if ($level == DEBUG) $this->_model->log_debug('task', $content, $bot_id);
	}

	private function _update_task($task, $task_status_cd) {
		$task->task_status_cd = $task_status_cd;
		if ($task_status_cd == '02') {
			$task->process_time = date('Y-m-d H:i:s');
		}
        else if ($task_status_cd == '01') {
            $task->finish_time = null;
            $task->process_time = null;
        }
		else {
			$task->finish_time = date('Y-m-d H:i:s');
		}
		$task->save();
	}

	public function execute($task_id) {
		// Check memory before starting execution
        if (!$this->_check_memory()) {
            $this->_log(ERROR, "Insufficient memory available to execute tasks. Exiting.", 0);
            $this->_model->send_watch_mail('=====Excute t_task(task_id=' . $task_id . ') Error (Reason: Insufficient memory available)=====', 'Insufficient memory available to execute t_task with task_id='. $task_id .'. Exiting.');
            $tasks = ORM::factory('task')->where('task_id', '=', $task_id)->find_all();
            foreach($tasks as $task) {
                $this->_update_task($task, '01');
            }
            exit(1);
        }

		$batch = new CLI_Batch();
		// tasks
		$tasks = ORM::factory('task')->where('task_id', '=', $task_id)->find_all();
		foreach($tasks as $task) {
			$param = json_decode($task->task_data, true);
			if (!is_array($param)) {
				$this->_update_task($task, '05');
				continue;
			}
			$params = explode('-', $param['cmd']);
			$method = array_shift($params);
			call_user_func_array([$batch, $method], $params);
			if ($task->repeat_cd == '00') {
				$this->_update_task($task, '03');
			}
		}
	}
}
