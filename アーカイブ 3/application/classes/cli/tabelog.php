<?php
defined('SYSPATH') or die('No direct script access.');

class CLI_Tabelog extends CLI_BaseCli
{

	private $_area_mapping = null;
	private $_area_upd_mapping = null;	
	private $_area_to_delete_array = null;
	private $_genre_mapping = null;
	private $_genre_mapping2 = null;
	private $_display_mapping = null;

	private $_market_item_model;
	public $_div = 'tabelog';

	public function __construct()
	{
		parent::__construct();
		$this->_market_item_model = new Model_Marketitemmodel();
	}

	public function download_process_tabelog_weekly($area='*', $seg='*')
	{
		$this->_write_log("=======START download_process_tabelog_weekly");
		
		$zip_file_name = 'tabelog-data.zip';
		$tabelog_xml_zip_url = "http://tabelog.com/tabelog_data/$zip_file_name";
		$target_folder = APPPATH . '../../files/temp/tabelog/';

		$command = "wget -P " . escapeshellarg($target_folder) . " " . escapeshellarg($tabelog_xml_zip_url);
		$downloaded = exec($command, $output, $return_var);
		echo "zip file downloaded >>$downloaded<<".PHP_EOL;
		
		$xml_folder = $this->extract_zip_compressed_folder($zip_file_name, $area, $seg, $target_folder, null, true);
		if (!$xml_folder) {
			$this->_write_log("Failed to extract $zip_file_name");
			$this->_model->log_info(__FUNCTION__, " 結果:failed", 0);
			return;
		} else {
			$info = "tabelog-data.zip extracted to $xml_folder, going to analyze xml files...";
			$this->_write_log($info);
			echo $info . PHP_EOL;
		}
		$this->_analyze_extracted_xml_files($xml_folder.'/', null, null);
		if($area=='*' && $seg=='*') {
			// if area and seg are not specified, clear all xml files
			$this->clear_xml_data_folder($xml_folder.'/');
		} else if ($area != '*' && $seg == '*') {
			// if area is specified, clear xml files for that area
			$this->clear_xml_data_folder($xml_folder.'/', $area);
		} else if ($area != '*' && $seg != '*') {
			// if area and seg are specified, clear xml files for that area and segment
			$this->clear_xml_data_folder($xml_folder.'/', $area, $seg);
		}
		$this->_write_log("=======END download_process_tabelog_weekly");
		$this->_model->log_info(__FUNCTION__, " 結果:success", 0);
	}

	protected function extract_zip_compressed_folder ($file_name, $area='*', $seg='*', $source_folder=null, $target_folder=null, $clean_zip=false) {
		$this->_write_log("=======START extract_zip_compressed_folder");

		try {
			if (!$source_folder) {
				$source_folder = APPPATH . '../../files/temp/tabelog/';
			}
			$file_path = $source_folder . $file_name;
			$zip_size = 0;
			$zip_size = filesize($file_path);
			if (!$target_folder) {
				$target_folder = str_replace(".zip", "", $file_path);
			}
			echo "extracting $file_path($zip_size) to $target_folder...".PHP_EOL;
			// delete existing target folder and XML files inside
			if (is_dir($target_folder)) {
				if ($dh = opendir($target_folder)) {
					$debug = "cleaning before decompressing... ";
					$this->_write_log($debug);
					$deleted = 0;
					while (($xml_file = readdir($dh)) !== false ) {
						if (($xml_file != ".") && ($xml_file != "") && ($xml_file != "..")) {
							unlink($target_folder . '/' . $xml_file);
							$deleted++;
							echo "deleted $xml_file".PHP_EOL;
						} else {
							$debug = "skipping filename: $xml_file";
						}
					}
					echo "deleted $deleted xml files".PHP_EOL;
					closedir($dh);
					rmdir($target_folder);
				}
			}
			mkdir($target_folder);
			// unarchive from the zip file
			// $command = "unzip -jFF  -o " . escapeshellarg($file_path) . " 'tabelog-data-$area-$seg.xml' -d " . escapeshellarg($target_folder);
			// /home/<USER>/workwuzhao/software/7zz e tabelog-data.zip -otabelog-data tabelog-data-1-*.xml
			$zip_exe_path =  $this->_model->get_env('7zz_tool_path');
			$command = "$zip_exe_path e $file_path -o" . escapeshellarg($target_folder) . " tabelog-data-$area-$seg.xml -bsp2";
			$extracted = exec($command, $output, $return_var);
			if ($clean_zip) {
				// delete the zip file
				if (file_exists($file_path)) {
					unlink($file_path);
					echo "deleted zip $file_path".PHP_EOL;
				}
			}
			echo "extracted >>$extracted<<".PHP_EOL;
			$this->_write_log("=======END extract_zip_compressed_folder");
			return $target_folder;
		} catch (Exception $e) {
			$error = "$file_path failed to extract to $target_folder".PHP_EOL.$e->getMessage();
			Log::instance()->add(Log::ERROR, $error);
			$this->_write_log($error);
			echo "<pre>";
			print_r(error_get_last());
			return false;
		}
	}

	protected function clear_xml_data_folder($target_folder=null, $area=null, $seg=null) {
		$this->_write_log("=======START clear_xml_data_folder");
		if (!$target_folder || $target_folder == 'null') {
			$target_folder = APPPATH . '../../files/temp/tabelog/tabelog-data/';
			if ($area) {
				$target_folder = APPPATH . "../../files/temp/tabelog/tabelog-data$area/";
			}
		}
		$info = "clearing xml files in $target_folder";
		$this->_write_log($info);
		$unlinked = 0;
		if ($area == null) {
			echo "clearing all areas...".PHP_EOL;
			for($area0 = 0; $area0 <= 47; $area0++) {
				$xml_files = glob($target_folder."tabelog-data-$area0-*.xml");
				foreach($xml_files as $xml_file) {
					unlink($xml_file);
					echo "deleted $xml_file".PHP_EOL;
					$unlinked++;
				}
			}
		} else if ($seg === null) {
			echo "clearing area $area...".PHP_EOL;
			$xml_files = glob($target_folder."tabelog-data-$area-*.xml");
			foreach($xml_files as $xml_file) {
				unlink($xml_file);
				echo "deleted $xml_file".PHP_EOL;
				$unlinked++;
			}
		} else {
			echo "clearing area $area, segment $seg...".PHP_EOL;
			unlink($target_folder."tabelog-data-$area-$seg.xml");
			echo "deleted tabelog-data-$area-$seg.xml".PHP_EOL;
			$unlinked++;
		}
		$info = "cleared $unlinked xml file(s) in $target_folder";
		$this->_write_log($info);
		$this->_write_log("=======END clear_xml_data_folder");
	}

	protected function _analyze_extracted_xml_files($target_folder=null, $area=null, $seg=null)
	{
		$this->_write_log("=======START _analyze_extracted_xml_files");
		$this->_process_mapping();
		if ($seg === null) {
			if (!$target_folder || $target_folder == 'null') {
				$target_folder = APPPATH . '../../files/temp/tabelog/tabelog-data/';
				if ($area) {
					$target_folder = APPPATH . "../../files/temp/tabelog/tabelog-data$area/";
				}
			}
			echo "analyzing xml files in $target_folder: ";
			if ($area == null) {
				echo "all areas".PHP_EOL;
				// $last_area = 47; // last area code
				for($area0 = 0; $area0 <= 47; $area0++) {
					// $tabelog_xml_files = glob($target_folder."tabelog-data-$area0-*.{xml}", GLOB_BRACE);
					echo "area $area0 scanning: $target_folder"."tabelog-data-$area0-*.xml ";
					// foreach($tabelog_xml_files as $tabelog_xml_file) {
						// $info = 'ingesting tabelog_xml_file(店舗情報取り込み開始):'.$tabelog_xml_file.' サイズ:'.filesize($tabelog_xml_file);
						// $this->_write_log($info);
						// $this->_analyze_xml($tabelog_xml_file, $area0);
					// }
					$this->_analyze_area_xml_files($target_folder, $area0);
				}
			}else {
				$target_folder = APPPATH . "../../files/temp/tabelog/tabelog-data$area/";
				$this->_analyze_area_xml_files($target_folder, $area, false);
			}
		} else if ($area) {
			echo "area $area, segment $seg".PHP_EOL;
			$tabelog_xml_file = APPPATH . '../../files/temp/tabelog/tabelog-data-'.$area.'-'.$seg.'.xml';
			if (!file_exists($tabelog_xml_file)) {
				throw new Exception("XML file not found: $tabelog_xml_file");
			}
			$this->_analyze_xml($tabelog_xml_file, $area);
		}
		$progress = $this->_market_item_model->batch_process_tabelog_update_continue();
		echo("Updating crawling data: processed $progress item_data=1 flagged items".PHP_EOL);				
		$this->_write_log("=======END _analyze_extracted_xml_files");
	}

	protected function _analyze_area_xml_files($target_folder, $area, $cleanup=true){
		$tabelog_xml_files = glob($target_folder."tabelog-data-$area-*.{xml}", GLOB_BRACE);
		if (count($tabelog_xml_files) == 0) {
			$info = "[Warning]no xml files found for area $area, skip".PHP_EOL;
			$this->_write_log($info);
			return;
		} else {
			echo "area $area found ".count($tabelog_xml_files)." xml files".PHP_EOL;
		}

		$this->_process_upd_mapping($area);
		$update_count = 0;
		$new_count = 0;

		foreach($tabelog_xml_files as $tabelog_xml_file) {
			$info = 'ingesting tabelog_xml_file(店舗情報取り込み開始):'.$tabelog_xml_file.' サイズ:'.filesize($tabelog_xml_file);
			// $this->_write_log($info);
			$result = $this->_analyze_xml($tabelog_xml_file, $area);
			if ($result) {
				$update_count += $result['update_count'];
				$new_count += $result['new_count'];
			}
		}
		$deleted_count = count($this->_area_to_delete_array);
		$this->_write_log("Updates/Newadded $update_count/$new_count shops from area $area");
		$this->_write_log("TO DELETE $deleted_count shops from area $area");
		if ($deleted_count > 0 && $cleanup) {
			$this->_process_deleted($area);
			// $this->_write_log("Deleting items from t_market_item: ".implode(',', $this->_area_to_delete_array));
			//TODO delete items from t_market_item
			
		} else {
			$this->_write_log("No items deleted for area $area");
		}
	}

	protected function _process_mapping()
	{
		$this->_write_log("=======START _process_mapping");
		$class_code_table = Model_Marketitemmodel::MAPPING_TBL;
		$item_table = Model_Marketitemmodel::ITEM_TBL;

		$genre_mapping_sql = "SELECT * FROM $class_code_table AS code WHERE code_div=999920 AND lang_cd='ja' AND CHAR_LENGTH(parent_cd)=4";
		$genre_mapping_query = DB::query(Database::SELECT, $genre_mapping_sql);
		$this->_genre_mapping = $genre_mapping_query->execute()->as_array('name', 'class_cd');

		$genre_mapping2_sql = "SELECT * FROM $class_code_table AS code WHERE code_div=999920 AND lang_cd='ja' AND CHAR_LENGTH(parent_cd)=2";
		$genre_mapping2_query = DB::query(Database::SELECT, $genre_mapping2_sql);
		$this->_genre_mapping2 = $genre_mapping2_query->execute()->as_array('name', 'class_cd');
		
		$area_mapping_sql = "SELECT * FROM $class_code_table AS code WHERE code_div=999921 AND lang_cd='ja'";
		$area_mapping_query = DB::query(Database::SELECT, $area_mapping_sql);
		$this->_area_mapping = $area_mapping_query->execute()->as_array('name', 'class_cd');

		$display_mapping_sql = "SELECT DISTINCT item_id FROM t_item_display dis WHERE item_div = 17 AND item_id IN (SELECT item_id FROM $item_table WHERE item_div=17 AND link_type_cd='ta' AND delete_flg!=1)";
		$display_mapping_query = DB::query(Database::SELECT, $display_mapping_sql);
		$this->_display_mapping = $display_mapping_query->execute()->as_array('item_id', 'item_id');

		$info = 'Mapping genre_id and are_cd into m_class_code: '.count($this->_genre_mapping2).'/'.count($this->_genre_mapping).'/'.count($this->_area_mapping).'/'.count($this->_display_mapping);
		$this->_write_log($info);
		$this->_write_log("=======END _process_mapping");
	}

	protected function _get_last_inserted_items(){
		$table = Model_Marketitemmodel::ITEM_TBL;
		$last_inserted_sql = "SELECT link_id, item_id FROM $table WHERE item_div=17 AND link_type_cd='ta' AND item_data = '0'";
		$last_inserted_query = DB::query(Database::SELECT, $last_inserted_sql);
		$last_inserted_items = $last_inserted_query->execute()->as_array('link_id', 'item_id');
		
		$info = 'Last inserted market items: '.count($last_inserted_items);
		$this->_write_log($info);
		return $last_inserted_items;
	}

	protected function _reset_last_inserted_items_flag(){
		$table = Model_Marketitemmodel::ITEM_TBL;
		
		$update_sql = "UPDATE $table SET item_data = '' WHERE item_div=17 AND link_type_cd='ta' AND item_data = '0'";	
		$update_query = DB::query(Database::UPDATE, $update_sql);
		$last_updated_items = $update_query->execute();

		$this->_write_log("Updated $last_updated_items rows in $table to reset new inserted flag");
		return $last_updated_items;
	}

	protected function _process_upd_mapping($area='%')
	{
		// $this->_write_log("=======START _process_upd_mapping");
		$table = Model_Marketitemmodel::ITEM_TBL;
		$upd_mapping_sql = "SELECT link_id, item_id, start_time FROM $table WHERE item_div=17 AND link_type_cd='ta' AND delete_flg!=1 AND JSON_UNQUOTE(link_data->'$.source_xml_filename') LIKE 'tabelog-data-$area-%'";//item_cd LIKE 'TA1700$area%'
		$upd_mapping_query = DB::query(Database::SELECT, $upd_mapping_sql);
		$this->_area_upd_mapping = $upd_mapping_query->execute()->as_array('link_id');
		$this->_area_to_delete_array = array_keys($this->_area_upd_mapping);

		$info = 'Mapping link_id > start_time: '.count($this->_area_upd_mapping);
		$this->_write_log($info);

		// $this->_write_log("=======END _process_upd_mapping");
	}

	protected function _process_deleted($area='')
	{
		// $this->_write_log("=======START _process_deleted");
		$table = Model_Marketitemmodel::ITEM_TBL;
		$delete_sql = "UPDATE $table SET delete_flg=1, upd_user=0, upd_time=NOW() WHERE item_div=17 AND link_type_cd='ta' AND delete_flg!=1 AND link_id IN (".implode(',', $this->_area_to_delete_array).")";
		$delete_query = DB::query(Database::DELETE, $delete_sql);
		$deleted_items = $delete_query->execute();
		$info = "Deleted(SET delete_flg) $deleted_items rows from $table for area $area";
		$this->_write_log($info);
		// $this->_write_log("=======END _process_deleted");
	}

	protected function _analyze_xml($xml_file_path, $area)
	{
		$this->_write_log("=======START _analyze_xml");
		if (!$xml_file_path) {
			$xml_file_path = APPPATH . '../../files/temp/tabelog/tabelog-data-0-0.xml';
		}
		// list($m_usage0, $p_usage0) = $this->_memory_usage();	
		// XMLファイルを読み込む
		$reader = new XMLReader();
		if (!$reader->open($xml_file_path)) {
			throw new Exception("Failed to open XML file: $xml_file_path");
		}
		// XMLの内容を表示
		// $area_cd = str_pad($area, 2, '0', STR_PAD_LEFT);
		$area_cd = '';
		
        // XMLデータを解析
		$shop_inserted = 0;
		$shop_updated = 0;
		// $shop_count = count($listings->listing);
		$shop_count = 0;
		$insert_buffer = 0;
		$buffer_size = 1000;
		$market_item_desc_values_buffer = [];
		// $name_changed_items = [];
		// $desc_changed_items = [];

		$table = Model_Marketitemmodel::ITEM_TBL;
		$desc_table = Model_Marketitemmodel::DESC_TBL;
		$keys = [
			'link_id',
			'item_div',
			'link_type_cd',
			'class_cd',
			'area_cd',
			'item_cd',
			'item_name',
			'location_lat',
			'location_lng',
			'geo_point',
			'start_time',
			'item_data',
			'link_data'
		];
		$desc_keys = [
			'item_id',
			'lang_cd',
			'title',
			'description'
		];
		$market_item_insert_query = DB::insert($table, $keys);
		$market_item_desc_insert_query = DB::insert($desc_table, $desc_keys);
		 
		$update_count = 0;
		$new_count = 0;

		while ($reader->read()) {
		if ($reader->nodeType == XMLReader::ELEMENT && $reader->name == 'listing') {
			$restaurant = new SimpleXMLElement($reader->readOuterXML());
            $name = (string)$restaurant->name;
            $address = (string)$restaurant->address;
            $id = (int)$restaurant->id;
			$shop_count ++;
			// echo "Processing restaurant: $name, ID: $id, Address: $address".PHP_EOL;
			$link_id = str_pad($id, 8, '0', STR_PAD_LEFT);
			$country = (string)$restaurant->country;
			$latitude = (float)$restaurant->latitude;
			$longitude = (float)$restaurant->longitude;
			$phone = (string)$restaurant->phone;
			$date = $restaurant->date;
			$updated_date = $date['year'].'-'.str_pad($date['month'], 2, '0', STR_PAD_LEFT).'-'.str_pad($date['day'], 2, '0', STR_PAD_LEFT);
			$mapped_time = null;
			if (isset($this->_area_upd_mapping[$link_id])) {
				$mapped_time = date('Y-m-d', strtotime($this->_area_upd_mapping[$link_id]['start_time']));
			}
			// $update = false;
			$insert = false;
			$item_id = null;
			if (!$mapped_time)  {
				$new_count++;
				$insert = true;
				// echo "New shop: $name, ID: $id, Updated: $updated_date".PHP_EOL;
			} else if ( $mapped_time < $updated_date){
				$update_count++;
				// $update = true;
				$item_id = $this->_area_upd_mapping[$link_id]['item_id'];
				// echo "Updated shop: $name, ID: $id, Updated: $mapped_time>>$updated_date".PHP_EOL;
			}
			
			$found_index = array_search($link_id, $this->_area_to_delete_array);
			if ( $found_index !== false && $found_index >= 0 ) {
				array_splice($this->_area_to_delete_array, $found_index, 1);
			}

			// continue; // TODO: remove this line to process all shops

			$categories = $restaurant->category;
			$category_count = count($categories);
			$index = 0;
			$category_array = [];
			$genre_codes = [];
			foreach ($categories as $category) {
				$index++;
				if ($index == $category_count) {
					//area_small/bus stop/subway station
					$category_array['area_small'] = (string)$category;
				} else if ($index == $category_count - 1) {
					//area_large
					$category_array['area'] = (string)$category;
					if ($this->_area_mapping && isset($this->_area_mapping[(string)$category])) {
						$area_cd = $this->_area_mapping[(string)$category];
					}
				} else {
					//genre
					if (!isset($category_array['genre'])) {
						$category_array['genre'] = [(string)$category];
					} else {
						$category_array['genre'] []= (string)$category;
					}
					if ($this->_genre_mapping && isset($this->_genre_mapping[(string)$category])) {
						$genre_codes []= $this->_genre_mapping[(string)$category];
					} elseif ($this->_genre_mapping2 && isset($this->_genre_mapping2[(string)$category])) {
						$genre_codes []= $this->_genre_mapping2[(string)$category];
					}
				}
			}
			$class_cd = implode(' ', $genre_codes);
			$content = $restaurant->content;
			// $images = $content->image;
			// $reviews = $content->review;
			$attributes = $content->attributes;
			$link = (string)$attributes->link[0];
			$sp_link = (string)$attributes->sp_link[0];
			$title = (string)$attributes->title[0];
			$author = (string)$attributes->author[0];

			$link_data = [
				'address' => $address,
				'country' => $country,
				'phone' => $phone,
				'category' => $category_array,
				'updated_date' => $updated_date,
				'link' => $link,
				'sp_link' => $sp_link,
				'title' => trim($title),
				'author' => $author,
				'source_xml_filename' => basename($xml_file_path, '.xml'),
				'xml_listing_offset' => $shop_count - 1, // offset starts from 0
			];

			$attrs = $attributes->attr;
			$budget_range = null;
			foreach ($attrs as $attr) {
				$attr_name = (string)$attr['name'];
				$attr_value = (string)$attr;
				if ($attr_name == 'カナ') {
					$link_data['kana'] = $attr_value;
				} else if ($attr_name == '定休日') {
					$link_data['closed_day'] = $attr_value;
				} else if ($attr_name == '友人・同僚と') {
					$link_data['with_fellows'] = $attr_value;
				} else if ($attr_name == '家族・子供と') {
					$link_data['with_family'] = $attr_value;
				} else if ($attr_name == '一人で') {
					$link_data['by_oneself'] = $attr_value;
				} else if ($attr_name == 'デート') {
					$link_data['for_date'] = $attr_value;
				} else if ($attr_name == '評価・採点') {
					$attr_name = '--RATING--';
				} else if ($attr_name == '料金（夜）') {
					$attr_name = 'Budget(Night)';
					$budget_range = $this->_process_budget_range($attr_value, $budget_range);
					$link_data['budget_night'] = $attr_value;
				} else if ($attr_name == '料金（昼）') {
					$attr_name = 'Budget(Daytime)';
					$budget_range = $this->_process_budget_range($attr_value, $budget_range);
					$link_data['budget_daytime'] = $attr_value;
				} else if ($attr_name == '営業時間') {
					$attr_name = 'business_hours';
					$attr_value = trim($attr_value);
					$link_data['business_hours'] = $attr_value;
				} else if ($attr_name == 'オープン日') {
					$attr_name = 'Opening Date';
					$link_data['opening_date'] = $attr_value;
				}
			}
			if ($budget_range) {
				$link_data['budget_range'] = $budget_range;
			}
            // 必要に応じてデータベースに保存
			$market_item_values = [
				$link_id,
				17,
				'ta', //tabelog
				$class_cd,
				$area_cd,
				'TA1700'.$link_id,
				$name,
				$latitude,
				$longitude,
				DB::Expr("POINT($longitude, $latitude)"), // DB Expression POINT(listing.longitude, listing.latitude)
				$updated_date,
				'0',
				json_encode($link_data, JSON_UNESCAPED_UNICODE),
			];
			// return $market_item;
			
			$only_count_updates = false; // set to true to only count updates, false to insert/update items
			if ($only_count_updates) {	
				if ($item_id){
					$market_item_desc_orm = ORM::factory('marketitemdescription', ['item_id' => $item_id, 'lang_cd' => 'ja']);
					if ($market_item_desc_orm->item_id == $item_id) {
						$description = isset($link_data['business_hours']) ? $this->_process_bussiness_hours($link_data['business_hours']) : '';
						if ($description != $market_item_desc_orm->description) {
							// update description
							$desc_changed_items []= $link_id;
						}
						// check if title is different
						if ($name != $market_item_desc_orm->title) {
							$name_changed_items []= $link_id;
						}
					}
				}
				continue; // TODO: remove this line to process all shops
			}
			if ( $insert ) {
				$market_item_insert_query->values($market_item_values);

				$insert_buffer++;
				// insert into t_market_item_description
				$market_item_inserted = false;
				// $market_item_inserted = $market_item_insert_query->execute();
				$market_item_inserted_id = 0;
				if (!$market_item_inserted) {
					// $this->_write_log('Failed to insert market item: '.$link_id);
					// continue;
				} else {
					// $market_item_inserted_id = $market_item_inserted[0];
					// $info = "market item table inserted: item_id = $market_item_inserted_id, link_id = $link_id ";
					// $this->_write_log($info);
				}
				// process description/business_hours
				$description = isset($link_data['business_hours']) ? $this->_process_bussiness_hours($link_data['business_hours']) : '';
				$market_item_desc_values = [
					$market_item_inserted_id,
					'ja',
					$name,
					$description,
				];
				$market_item_desc_values_buffer[$link_id] = $market_item_desc_values;
				// echo "Inserting market item description: values ".implode(',', $market_item_desc_values).PHP_EOL;
				// $market_item_desc_insert_query->values($market_item_desc_values)->execute();
				// $shop_inserted ++;
				// $market_item_insert_query = DB::insert($table, $keys);
				// $market_item_desc_insert_query = DB::insert($desc_table, $desc_keys);

				if ($insert_buffer >= $buffer_size) {
					$this->_execute_insert_and_reset_buffer($market_item_insert_query, $market_item_desc_insert_query, $market_item_desc_values_buffer);
					// $this->_memory_usage_delta($m_usage0, $p_usage0, "Inserted $buffer_size records");

					$shop_inserted+= $insert_buffer;
					$insert_buffer = 0;
					$market_item_insert_query = DB::insert($table, $keys);
					$market_item_desc_insert_query = DB::insert($desc_table, $desc_keys);
				}
			} else if ($item_id) {
				// update existing item
				$market_item_orm = ORM::factory('marketitem', $item_id);
				$market_item_orm->item_name = $name;
				$market_item_orm->class_cd = $class_cd;
				$market_item_orm->area_cd = $area_cd;
				$market_item_orm->location_lat = $latitude;
				$market_item_orm->location_lng = $longitude;
				$market_item_orm->geo_point = DB::Expr("POINT($longitude, $latitude)"); // DB Expression POINT(listing.longitude, listing.latitude)
				$market_item_orm->start_time = $updated_date;
				$old_link_data = $market_item_orm->link_data;
				$old_link_data_array = json_decode($old_link_data, true);
				if (isset($old_link_data_array['restaurant_image_url_array'])) {
					// merge restaurant_image_url_array
					$restaurant_image_url_array = $old_link_data_array['restaurant_image_url_array'];
					$link_data['restaurant_image_url_array'] = $restaurant_image_url_array;
				}
				if (isset($old_link_data_array['reservation_available'])) {
					// merge reservation_available
					$reservation_available = $old_link_data_array['reservation_available'];
					$link_data['reservation_available'] = $reservation_available;
				}
				$market_item_orm->link_data = json_encode($link_data, JSON_UNESCAPED_UNICODE);
				$market_item_orm->upd_time = DB::expr('NOW()');
				$updated_orm = $market_item_orm->save();
				$updated = $updated_orm->start_time == $updated_date;
				if ($updated) {
					// echo "Updated shop: $name, ID: $id, Updated: $mapped_time>>$updated_date".PHP_EOL;
					// update description/business_hours
					$description = isset($link_data['business_hours']) ? $this->_process_bussiness_hours($link_data['business_hours']) : '';
					$market_item_desc_values = [
						$item_id,
						'ja',
						$name,
						$description,
					];
					$old_desc_orm = new Model_Marketitemdescription(array('item_id'=>$item_id, 'lang_cd'=>'ja')); // find description by item_id and lang_cd
					$update_translation = false;
					if ($old_desc_orm->item_id != $item_id) {
						// re-insert new description
						$market_item_desc_reinsert_query = DB::insert(Model_Marketitemmodel::DESC_TBL, $desc_keys);
						$market_item_desc_reinsert_query->values($market_item_desc_values);
						$market_item_desc_inserted = $market_item_desc_reinsert_query->execute();
						if ($market_item_desc_inserted) {
							$info = "Inserted market item description: item_id = $item_id, link_id = $link_id, name = $name, description = $description";
							// $this->_write_log($info);
						} else {
							$info = "Failed to insert market item description: item_id = $item_id, link_id = $link_id, name = $name, description = $description";
							echo $info.PHP_EOL;
							// $this->_write_log($info);
						}
					} else if ($name != $old_desc_orm->title || $description != $old_desc_orm->description) {
						// if description or title is different, update description
						$set_pairs = ['upd_time' => DB::expr('NOW()')];
						if ($name != $old_desc_orm->title) {
							$set_pairs['title'] = $name;
						}
						if ($description != $old_desc_orm->description) {
							$set_pairs['description'] = $description;
							$update_translation = true;
						}
						$market_item_desc_update_query = DB::update(Model_Marketitemmodel::DESC_TBL)
							->set($set_pairs)
							->where('item_id', '=', $item_id)
							->where('lang_cd', '=', 'ja');
						$desc_tbl_updated = $market_item_desc_update_query->execute();
						if ($desc_tbl_updated) {
							$info = "Updated market item: item_id = $item_id, link_id = $link_id, name = $name, description = $description";
							// $this->_write_log($info);
						} elseif (!$desc_tbl_updated) { // not found or no difference
							echo "Market item description NOT updated: $link_id, item_id: $item_id, name: $name, description: $description".PHP_EOL;
						}
						if ($desc_tbl_updated && isset($this->_display_mapping[$item_id])){
							// $this->_write_log('Updated market item description: '.$link_id);
							// check if title is different
							if ($name != $old_desc_orm->title) {
								// mark flag of updated title for english title fetch
								$market_item_update_query = DB::update($table)
									->set([
										'item_data' => 1, // set item_data to 1 as title_update_flg
										'upd_time' => DB::expr('NOW()'),
									])
									->where('item_id', '=', $item_id)
									->where('link_id', '=', $link_id)
									->where('item_div', '=', 17)
									->where('link_type_cd', '=', 'ta');
	
								// echo "Updating shop: $name, ID: $id > ".$market_item_update_query->compile().PHP_EOL;
								$updated = $market_item_update_query->execute();
								$update_translation = false; // set to false to update translations later during crawling data update
							}
						}
					}
					// only update translations if description is changed and being displayed
					if ($update_translation && isset($this->_display_mapping[$item_id])) {
						// update description translations
						$langs = ['en', 'tw', 'kr', 'cn'];
						$failed_translations = [];
						foreach ($langs as $lang) {
							$business_hours_translated = $this->_admin_model->translate($description, $lang, 'ja');
							$market_item_desc_update_query = DB::update($desc_table)
								->set([
									'description' => $business_hours_translated,
									'upd_time' => DB::expr('NOW()'),
								])
								->where('item_id', '=', $item_id)
								->where('lang_cd', '=', $lang);
							$desc_translate_updated = $market_item_desc_update_query->execute();
							if (!$desc_translate_updated) {
								$failed_translations[] = $lang;
							} else {
								// $this->_write_log('Updated market item description translation: '.$link_id);
							}
						}
						if (count($failed_translations) > 0) {
							$this->_write_log('Failed to update market item description translations: '.implode(',', $failed_translations));
						} else {
							// $this->_write_log('Updated all market item description translations: '.$link_id);
						}
					}
					$shop_updated++;
				} else {
					echo "Failed to update shop: $name, ID: $id".PHP_EOL;
				}				
			}			
		}
		}
		if ($insert_buffer){
			$this->_execute_insert_and_reset_buffer($market_item_insert_query, $market_item_desc_insert_query, $market_item_desc_values_buffer);
			$shop_inserted+= $insert_buffer;
			unset($market_item_insert_query); // メモリ解放
			$market_item_insert_query = DB::insert($table, $keys);
			// $this->_memory_usage_delta($m_usage0, $p_usage0, "Inserted last $buffer records");
		}
		if ($only_count_updates) {
			// $this->_write_log("Updates of sum/names/descriptions from $xml_file_path: $update_count/".json_encode($name_changed_items).'/'.json_encode($desc_changed_items));
			if (isset($name_changed_items) && count($name_changed_items) > 0) {
				$this->_write_log("Updates of names from $xml_file_path: \n".json_encode($name_changed_items));
			}
		} else {
			$this->_write_log("Inserted/Updated/Total $shop_inserted($new_count)/$shop_updated($update_count)/$shop_count shops from $xml_file_path");
		}

		// $this->_memory_usage_delta($m_usage0, $p_usage0, "Analyzed one xml");
		
		$this->_write_log("=======END _analyze_xml");
		return array(
			'shop_inserted' => $shop_inserted,
			'shop_count' => $shop_count,
			'update_count' => $update_count,
			'new_count' => $new_count
		);
	}

	private function _execute_insert_and_reset_buffer(&$market_item_insert_query, &$market_item_desc_insert_query, &$market_item_desc_values_buffer){
		$market_items_inserted = $market_item_insert_query->execute();
			echo('Batch inserted market items buffer(last inserted id, count): '.json_encode($market_items_inserted).PHP_EOL);

			$last_inserted_items = $this->_get_last_inserted_items();
			foreach ($last_inserted_items as $link_id => $item_id) {
				if (isset($market_item_desc_values_buffer[$link_id])) {
					$market_item_desc_values = $market_item_desc_values_buffer[$link_id];
					$market_item_desc_values[0] = $item_id; // update item_id
					$market_item_desc_insert_query->values($market_item_desc_values);
				}
				//TODO what if $item_id is not in $market_item_desc_values_buffer? not likely
			}
			$market_item_desc_insert_query->execute();
			$market_item_desc_values_buffer = []; // reset buffer 
			$this->_reset_last_inserted_items_flag(); // reset item_data flag to empty string
	}

	private function _memory_usage_delta($m_usage0, $p_usage0, $msg_prefix)
	{
		$m_usage = memory_get_usage(true);
		$p_usage = memory_get_peak_usage(true);
		$this->_write_log($msg_prefix.", Memory usage delta: ".($m_usage-$m_usage0).'/'.($p_usage-$p_usage0));
		return array($m_usage, $p_usage);
	}

	private function _memory_usage()
	{
		$memory_usage = memory_get_usage(true);
		$memory_get_peak_usage = memory_get_peak_usage(true);
		$this->_write_log("Memory usage usage/peek $memory_usage/$memory_get_peak_usage");
		return array($memory_usage, $memory_get_peak_usage);
	}

	private function _process_budget_range($budget, $budget_range = null)
	{
		if ($budget_range) {
			$min = $budget_range['min'] ?? null;
			$max = $budget_range['max'] ?? null;
		} else {
			$min = null;
			$max = null;
		}
		$budget = str_replace("円", "", $budget);
		$budget = str_replace("￥", "", $budget);
		$budget = str_replace(",", "", $budget);
		$budget = str_replace("～", "-", $budget);
		$budget = str_replace(" ", "", $budget);
		if (strpos($budget, "-") !== false) {
			list($min0, $max0) = explode("-", $budget);
			if ($min0 && (!$min || $min == '' ||  $min > (int)$min0)) {
				$budget_range['min'] = (int)$min0;
			}
			if ($max0 && (!$max || $max == '' || $max < (int)$max0)) {
				$budget_range['max'] = (int)$max0;
			}
			return $budget_range;
		} else {
			return null;
		}
	}

	protected function _process_bussiness_hours($business_hours = null)
	{
		if (!$business_hours) {
			$business_hours = "[月]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00
[火]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00
[水]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00
[木]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00
[金]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00
[土]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00
[日]
　06:30 - 10:00（L.O. 09:30）
　11:30 - 15:00（L.O. 14:30）
　18:00 - 22:00

■ 営業時間
コース料理 L.O. 21:00／アラカルト L.O. 21:30

■ 定休日
年中無休";
		}
		// 曜日や祝日情報を取得し、営業時間をグループ化
		$lines = explode("\n", $business_hours);
		$grouped_hours = [];
		$current_days = null;
		$notes = [];
		$current_note = null;
		$last_line = null;
		$last_hours = null;
		foreach ($lines as $line) {
			$line = trim($line);
			if (preg_match('/^\[(.+?)\]$/', $line, $matches)) {
				// 曜日や祝日情報を取得
				$current_days = explode('、', $matches[1]);
				$last_line = '<days>';
				if ($last_hours) {
					if (!isset($grouped_hours[$last_hours])) {
						$grouped_hours[$last_hours] = [];
					}
					$grouped_hours[$last_hours] = array_merge($grouped_hours[$last_hours], $current_days);
					// echo "Adding hours: $line to days: ".implode(',', $current_days).PHP_EOL;
				}
				$last_hours = null; // reset last_hours for next iteration
				// echo "Processing days(曜日や祝日): ".implode(',', $current_days).PHP_EOL;
			} else if (preg_match('/^■/', $line, $matches)) {
				//'■ 営業時間' or '■定休日'
				$current_note = $matches[0];
				$notes [] = $line;// メモを追加
				// echo "Processing note(■ 営業時間や■定休日?$current_note): $line".PHP_EOL;
			} else if ($current_note) {
				$notes []= $line;// メモを追加
				// echo "Adding note: $line".PHP_EOL;
			} else if (!empty($line)) {
				// 営業時間情報を取得
				if ($current_days) {
					if($last_line == '<days>') {
						// if last line is current_days, then we need to add it
						$last_hours = $line;
						// echo "Adding hours: $line to days: ".implode(',', $current_days).PHP_EOL;
					} else {
						// if last line is not current_days, then we need to add it
						$last_hours .= "\n".$line;
						// echo "Adding more hours: $line to days: ".implode(',', $current_days).PHP_EOL;
					}
					$last_line = $line;
				} else {
					// 営業時間情報がない場合は、最後の営業時間を使用
					// echo "[Exception]No current days, abort without processing: $line".PHP_EOL;
					return $business_hours; // 何も処理されなかった場合は元の文字列を返す
				}
			}
		}
		if (!$grouped_hours) {
			// echo "[Exception]No grouped_hours, abort without processing: $line".PHP_EOL;
			return $business_hours; // 何も処理されなかった場合は元の文字列を返す
		}
	
		// 結果をフォーマット
		$result = [];
		foreach ($grouped_hours as $hours => $days) {
			$result[] = '[' . implode('、', $days) . "]\n" . $hours;
		}
		if ($grouped_hours && $notes) {
			$result[] = "";
		} 
		foreach ($notes as $note) {
			$result[] = $note;
		}
		// echo "Processed business hours: ".implode("\n", $result).PHP_EOL;
		return implode("\n", $result);
	}

	//TODO: 将来他の親子施設も「全てリンクする」できるように改善する
	//1. リンクする範囲を標す:
	public function prepare_market_item_filtered_link($bot_id=0, $distance=0.3) {
		$bot_id0 = null;
		$bot_id1 = null;
		if ($bot_id == '0') {
			$bot_id = 201000; // default bot_id
			$bot_id0 = 201001; // first bot bot_id0
			$bot_id1 = 201100; // last bot bot_id1
		} else if (0< $bot_id && $bot_id < 1000) {
			$bot_id = 201000 + $bot_id; // bot_id is 3 digits, so add 201000 to it
			$bot_id0 = $bot_id; // first bot bot_id0
			$bot_id1 = $bot_id; // last bot bot_id1
		} else {
			$bot_id0 = $bot_id; // first bot bot_id0
			$bot_id1 = $bot_id; // last bot bot_id1
		}
		if ($bot_id < 201000 || $bot_id > 202000) {
			throw new Exception("Invalid bot_id: $bot_id, should be between 201000 and 202000");
		}		
		$excludes = [27, 46, 101, 102]; // 69?
		// $location_mapping = json_decode(file_get_contents(APPPATH.'../../files/temp/tabelog/location_mapping.json'), true);
		$location_mapping = $this->_market_item_model->get_bot_location_mapping($bot_id0, $bot_id1);
		echo("Location mapping loaded: ".count($location_mapping)." entries".PHP_EOL);
		foreach ($excludes as $exclude) {
			if (isset($location_mapping[$bot_id + $exclude])) {
				unset($location_mapping[$bot_id + $exclude]); // remove excluded bots from location mapping
			}
		}
		$locations = array_values($location_mapping);
		$items_count = $this->_market_item_model->count_filtered_tabelog_market_items($locations, $distance);
		echo("Filtered market items for bot_id $bot_id, distance $distance: ".$items_count." items found".PHP_EOL);
		$flagged = $this->_market_item_model->flag_filtered_tabelog_market_items($locations,$distance);
		echo($flagged." items flagged to apply crawling...".PHP_EOL);		
	}

	//2. リンクする前にクローリングデータの用意: (test modeで51件しかクローリングしない)
	public function continue_market_item_filtered_link($test=false) {
		if ($test) {
			echo("Test mode enabled: only processing a limited number(51) of items".PHP_EOL);
		} 
		$progress = $this->_market_item_model->process_flagged_tabelog_market_items_to_link($test);
		$info = "continue_market_item_filtered_link: processed $progress flagged items";
		$this->_write_log($info);
	}

	//3. 店舗ごとに公開するために用意したクローリング済みのデータをリンクする:
	public function finish_market_item_filtered_link($bot_id=0, $distance=0.3) {
		$bot_id0 = null; // first bot bot_id0
		$bot_id1 = null; // last bot bot_id1
		if ($bot_id == '0') {
			$bot_id = 201000; // default bot_id
			$bot_id0 = 201001; // first bot bot_id0
			$bot_id1 = 201100; // last bot bot_id1
		} else if (0< $bot_id && $bot_id < 1000) {
			$bot_id = 201000 + $bot_id; // bot_id is 3 digits, so add 201000 to it
			$bot_id0 = $bot_id; // first bot bot_id0
			$bot_id1 = $bot_id; // last bot bot_id1
		} else {
			$bot_id0 = $bot_id; // first bot bot_id0
			$bot_id1 = $bot_id; // last bot bot_id1
		}
		if ($bot_id < 201000 || $bot_id > 202000) {
			throw new Exception("Invalid bot_id: $bot_id, should be between 201000 and 202000");
		}		
		$excludes = [27, 46, 101, 102]; // 69?
		// $progress = $this->_market_item_model->process_flagged_tabelog_market_items_to_link();
		$location_mapping = $this->_market_item_model->get_bot_location_mapping($bot_id0, $bot_id1);
		foreach ($excludes as $exclude) {
			if (isset($location_mapping[$bot_id + $exclude])) {
				unset($location_mapping[$bot_id + $exclude]); // remove excluded bots from location mapping
			}
		}
		if ($bot_id0 && $bot_id1) {
			$ready_to_link = $this->_market_item_model->check_flagged_tabelog_market_items_to_link();
			if (count($ready_to_link) == 0) {
				echo "No batch crawling processed items found with item_data = 2, nothing to process.".PHP_EOL;
				return 0;
			}
			$not_ready_to_link = $this->_market_item_model->check_flagged_tabelog_market_items_to_link(3);
			if (count($not_ready_to_link) > 0) {
				echo "[Warning] There are still items with item_data = 3, not ready to link: ".count($not_ready_to_link)." items found".PHP_EOL;
				echo "You can continue processing them with 'continue_market_item_filtered_link' method.".PHP_EOL;
			}
			for($sub_bot_id = $bot_id0; $sub_bot_id <= $bot_id1; $sub_bot_id++) {
				if (in_array($sub_bot_id-$bot_id, $excludes)) {
					continue; // skip excluded bots
				}
				$location = isset($location_mapping[$sub_bot_id]) ? $location_mapping[$sub_bot_id] : null;
				if (!$location) {
					echo("[Warning]No location mapping found for bot_id $sub_bot_id, incorrect items might be linked...".PHP_EOL);
				} else {
					echo("Processing bot_id $sub_bot_id with location: ".json_encode($location).PHP_EOL);
				}
				$linked = $this->_market_item_model->link_flagged_tabelog_market_items($sub_bot_id, $location, $distance);
				echo("Linked $linked items for bot_id $sub_bot_id".PHP_EOL);
			}
		} else {
			echo("Not valid bot_id $bot_id!");
		}
		$last_updated = $this->_market_item_model->finish_linked_tabelog_market_items();
		echo("Updating item_data: finished linking $last_updated flagged items, last updated items.(全て実行完了)".PHP_EOL);
	}

	//4. 更新あり、且つリンク済みデータをクローリングデータの更新用意: (test modeで51件しかクローリングしない)
	// if manual batch processing is needed, then use this method (更新対象が多すぎて、手動でクローリングバッチ実行する必要(当日11:00前に終わらない)がある場合は、このメソッドを使用してください)
	public function continue_market_items_update($test=false) {
		if ($test) {
			echo("Test mode enabled: only processing a limited number(51) of items".PHP_EOL);
		} 
		$progress = $this->_market_item_model->batch_process_tabelog_update_continue($test);
		$info = "continue_market_items_update: processed $progress flagged items";
		$this->_write_log($info);
	}

}
