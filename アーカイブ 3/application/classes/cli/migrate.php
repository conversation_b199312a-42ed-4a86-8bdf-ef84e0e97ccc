<?php
 defined('SYSPATH') or die('No direct script access.');

class CLI_Migrate {

    public $_model;
	public $_mail;
	public $_aws;
	public $_admin_model;
	public $_very_model;
    public $_facility_cd;
    public $_bot_name;

    public function __construct()
	{
		ob_end_flush();
        $this->_model = new Model_Batchmodel();
		$this->_admin_model = new Model_Adminmodel();
		$this->_very_model = new Model_Adminverymodel();
		$this->_mail = new Model_Email();
		$this->_aws = new Model_Aws();
        $this->_facility_cd = substr(md5(uniqid(rand())), 0, 32);
	}
	
	public function _call_method($params)
	{
		$method = array_shift($params);
		return call_user_func_array(array($this, $method), $params);
	}
	
    private function write_log($log) {
		$file =  APPPATH . "../../files/log/migrate_" . date('Y-m-d') . ".log";
		$f = fopen($file, 'a');
		$log = date('Y-m-d H:i:s') . ' ' . $log . PHP_EOL;
		fwrite($f, $log);
		fclose($f);
		echo($log);
	}

    public function setting($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {

        // ■ 基本設定
        $this->write_log("------begin to copy basic settings");
        
        // t_bot
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot')->where('bot_id', '=', $desc_id)->execute();

        $src_orm = ORM::factory('bot', $src_id);
        $this->_bot_name = $src_orm->bot_name . "_コピー_" . "$desc_id";
        $orm = ORM::factory('bot');
        $orm->values($src_orm->as_array());
        $orm->bot_id = $desc_id;
        $orm->bot_name = $this->_bot_name;
        $orm->facility_cd = $this->_facility_cd;
        $orm->save();
        $this->write_log("------copy t_bot done");

        // t_bot_scene
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_scene')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botsetting')->where('bot_id', '=', $src_id)->find_all();
        $orm = ORM::factory('botscene');
        $orm->bot_id = $desc_id;
        $orm->scene_name = $this->_facility_cd;
        $orm->label = $this->_bot_name;
        $orm->upd_user = 0;
        $orm->upd_time = date('Y-m-d H:i:s');;
        $orm->save();
        $this->write_log("------t_bot_scene created.");
        // t_bot_setting
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_setting')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botsetting')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botsetting');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_setting done");

        //　m_class_code　→ ボット専用の分類がある場合

        // t_bot_class_code　→ ボット専用の分類がある場合
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_class_code')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botclasscode')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botclasscode');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_class_code done");

        // m_intent　→ 標準以外のリクエストを定義する場合
        if($overwrite_desc_if_exsit == 'true') DB::delete('m_intent')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('intent')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('intent');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy m_intent done");

        $this->write_log("------finish to copy basic settings");
    }

    public function content($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {
        $this->write_log("------begin to copy contents");

        // t_item
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_item')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('item')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('item');
            $orm->values($src_orm->as_array());
            $orm->item_id = substr_replace($src_orm->item_id, $desc_id, 0, strlen($src_id));
            $orm->bot_id = $desc_id;
            if ($src_orm->item_cd == $src_orm->item_id) {
                $orm->item_cd = substr_replace($src_orm->item_cd, $desc_id, 0, strlen($src_id));
            }
            $orm->save();
        }
        $this->write_log("------copy t_item done");

        // t_item_display
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_item_display')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('itemdisplay')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('itemdisplay');
            $orm->values($src_orm->as_array());
            $orm->item_id = substr_replace($src_orm->item_id, $desc_id, 0, strlen($src_id));
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_item_display done");

        // t_item_description
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_item_description')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('itemdescription')->where('item_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('itemdescription');
            $orm->values($src_orm->as_array());
            $orm->item_id = substr_replace($src_orm->item_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_item_description done");

        // t_item_keyword
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_item_keyword')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('itemkeyword')->where('item_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('itemkeyword');
            $orm->values($src_orm->as_array());
            $orm->item_id = substr_replace($src_orm->item_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_item_keyword done");

        // t_item_congestion
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_item_congestion')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('itemcongestion')->where('item_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('itemcongestion');
            $orm->values($src_orm->as_array());
            $orm->item_id = substr_replace($src_orm->item_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_item_congestion done");

        // t_item_description_fulltext
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_item_description_fulltext')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('itemdescriptionfulltext')->where('item_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('itemdescriptionfulltext');
            $orm->values($src_orm->as_array());
            $orm->item_id = substr_replace($src_orm->item_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_item_description_fulltext done");

        $this->write_log("------finish to copy contents");
    }

    public function faq($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {
        $this->write_log("------begin to copy faqs");

        // ■ FAQ関連テーブル
        // m_bot_intent → level 3とlevel 4のデータが更新対象
        $sql = "UPDATE m_bot_intent SET item_ids= CONCAT(item_ids, CONCAT(',', :desc_id)) WHERE level IN (3,4) AND FIND_IN_SET(:src_id, item_ids) > 0 AND item_ids='901002'";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
            ':src_id' => $src_id,
            ':desc_id' => $desc_id
        ));
		$query->execute();

        $this->write_log("------update m_bot_intent with level 3 or 4 done");

        // m_intent_relation
        if($overwrite_desc_if_exsit == 'true') DB::delete('m_intent_relation')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('intentrelationmst')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('intentrelationmst');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy m_intent_relation done");

        // t_bot_intent
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_intent')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botintent')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botintent');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_intent done");

        // t_bot_intent_his → FAQ編集履歴
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_intent_his')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botintenthis')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botintenthis');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_intent_his done");

        // t_intent_skill
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_intent_skill')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botintentskill')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botintentskill');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_intent_skill done");

        // t_bot_intent_editing
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_intent_editing')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botintentediting')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botintentediting');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_intent_editing done");

        // t_bot_intent_fulltext
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_intent_fulltext')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botintentfulltext')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botintentfulltext');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_intent_fulltext done");

        $this->write_log("------finish to copy faqs");
    }


    public function msg($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {
        $this->write_log("------begin to copy msgs");

        // t_bot_msg
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_msg')->where('bot_id', '=', $desc_id)->execute();

        $src_orms = ORM::factory('botmsg')->where('delete_flg', '=', 0)->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmsg');
            $orm->values($src_orm->as_array());
            $orm->msg_id = substr_replace($src_orm->msg_id, $desc_id, 0, strlen($src_id));
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_msg done");

        // t_bot_msg_desc_car
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_msg_desc_car')->where('msg_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('botmsgdesccar')->where('delete_flg', '=', 0)->where('msg_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmsgdesccar');
            $orm->values($src_orm->as_array());
            $orm->msg_id = substr_replace($src_orm->msg_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_msg_desc_car done");

        // t_bot_msg_desc_img
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_msg_desc_img')->where('msg_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('botmsgdescimg')->where('delete_flg', '=', 0)->where('msg_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmsgdescimg');
            $orm->values($src_orm->as_array());
            $orm->msg_id = substr_replace($src_orm->msg_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_msg_desc_img done");

        // t_bot_msg_desc_lst
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_msg_desc_lst')->where('msg_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('botmsgdesclst')->where('delete_flg', '=', 0)->where('msg_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmsgdesclst');
            $orm->values($src_orm->as_array());
            $orm->msg_id = substr_replace($src_orm->msg_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_msg_desc_lst done");

        // t_bot_msg_desc_tpl
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_msg_desc_tpl')->where('msg_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('botmsgdesctpl')->where('delete_flg', '=', 0)->where('msg_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmsgdesctpl');
            $orm->values($src_orm->as_array());
            $orm->msg_id = substr_replace($src_orm->msg_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_msg_desc_tpl done");

        // t_bot_msg_desc_txt
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_msg_desc_txt')->where('msg_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('botmsgdesctxt')->where('delete_flg', '=', 0)->where('msg_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmsgdesctxt');
            $orm->values($src_orm->as_array());
            $orm->msg_id = substr_replace($src_orm->msg_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_msg_desc_txt done");

        // t_bot_menu_group　→ 削除予定が、初期段階のbotはまだ利用している可能性がある

        $this->write_log("------finish to copy msgs");
        
    }

    public function flow($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {
        $this->write_log("------begin to copy flows");

        // t_bot_def_intent
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_def_intent')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('botdefintent')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botdefintent');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_bot_def_intent done");

        // t_autoanswer_flow
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_autoanswer_flow')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('autoanswerflow')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('autoanswerflow');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_autoanswer_flow done");

        // t_autoanswer_action
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_autoanswer_action')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('autoansweraction')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('autoansweraction');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_autoanswer_action done");

        // t_autoanswer_node
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_autoanswer_node')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('autoanswernode')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('autoanswernode');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_autoanswer_node done");

        // t_autoanswer_text
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_autoanswer_text')->where('bot_id', '=', $desc_id)->execute();
        
        $src_orms = ORM::factory('autoanswertext')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('autoanswertext');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->save();
        }
        $this->write_log("------copy t_autoanswer_text done");

        $this->write_log("------finish to copy flows");
    }

    public function user($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {
        // t_user
        // t_user_access
        // t_user_notify
    }

    public function inquiry($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {

        // t_inquiry
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_inquiry')->where('bot_id', '=', $desc_id)->execute();

        $src_orms = ORM::factory('inquiry')->where('bot_id', '=', $src_id)->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('inquiry');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->inquiry_id = substr_replace($src_orm->inquiry_id, $desc_id, 0, strlen($src_id));
            $orm->inquiry_cd = substr(md5(uniqid(mt_rand(), true)), 0, 16);
            $orm->save();
        }
        $this->write_log("------copy t_inquiry done");

        // t_inquiry_section
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_inquiry_section')->where('inquiry_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('inquirysection')->where('delete_flg', '=', 0)->where('inquiry_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('inquirysection');
            $orm->values($src_orm->as_array());
            $orm->inquiry_id = substr_replace($src_orm->inquiry_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_inquiry_section done");

        // t_inquiry_branch
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_inquiry_branch')->where('inquiry_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('inquirybranch')->where('inquiry_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('inquirybranch');
            $orm->values($src_orm->as_array());
            $orm->inquiry_id = substr_replace($src_orm->inquiry_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_inquiry_branch done");

        // t_inquiry_description
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_inquiry_description')->where('inquiry_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('inquirydescription')->where('delete_flg', '=', 0)->where('inquiry_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('inquirydescription');
            $orm->values($src_orm->as_array());
            $orm->inquiry_id = substr_replace($src_orm->inquiry_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_inquiry_description done");

        // t_inquiry_entry
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_inquiry_entry')->where('inquiry_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('inquiryentry')->where('delete_flg', '=', 0)->where('inquiry_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('inquiryentry');
            $orm->values($src_orm->as_array());
            $orm->inquiry_id = substr_replace($src_orm->inquiry_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_inquiry_entry done");

        // t_inquiry_entry_action
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_inquiry_entry_action')->where('inquiry_id', 'LIKE', $desc_id . '____')->execute();

        $src_orms = ORM::factory('inquiryentryaction')->where('inquiry_id', 'LIKE', $src_id . '____')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('inquiryentryaction');
            $orm->values($src_orm->as_array());
            $orm->inquiry_id = substr_replace($src_orm->inquiry_id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_inquiry_entry_action done");

        // t_bot_maximum
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_maximum')->where('id', 'LIKE', $desc_id . '______')->execute();

        $src_orms = ORM::factory('botmaximum')->where('bot_id', '=', $src_id)->where('id', 'LIKE', $src_id . '______')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmaximum');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->id = substr_replace($src_orm->id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_maximum done");

        // t_bot_maximum_remains
        if($overwrite_desc_if_exsit == 'true') DB::delete('t_bot_maximum_remains')->where('id', 'LIKE', $desc_id . '______')->execute();

        $src_orms = ORM::factory('botmaximumremains')->where('bot_id', '=', $src_id)->where('id', 'LIKE', $src_id . '______')->find_all();
        foreach ($src_orms as $src_orm) {
            $orm = ORM::factory('botmaximumremains');
            $orm->values($src_orm->as_array());
            $orm->bot_id = $desc_id;
            $orm->id = substr_replace($src_orm->id, $desc_id, 0, strlen($src_id));
            $orm->save();
        }
        $this->write_log("------copy t_bot_maximum_remains done");


    }

    public function extra($desc_id) {
        $this->write_log("------start to do extra");

        $this->_admin_model->bot_create_log_table($desc_id);
        $this->_admin_model->dirty_bot_control('t_bot');
        $this->_admin_model->dirty_bot_control('t_bot_setting');
        $this->_admin_model->dirty_bot_control('t_bot_scene');

        $this->write_log("------finish to do extra");
    }

	public function bot($src_id, $desc_id, $overwrite_desc_if_exsit = 'false', $del_src_after_migrate = 'false') {

        $this->write_log("=======START data migration from " . $src_id . " to " . $desc_id . ". overwrite: " . $overwrite_desc_if_exsit . ", del_src: " . $del_src_after_migrate);

        // ■ 基本設定
        $this->setting($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);

        //  ■ INQUIRY
        $this->inquiry($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);

        // // ■ コンテンツ
        // $this->content($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);

        // // ■ FAQ
        // $this->faq($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);
        
        // // ■ メッセージ定義
        // $this->msg($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);

        // // ■ リクエスト受付
        // $this->flow($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);

        // // ■ 管理者設定
        // $this->user($src_id, $desc_id, $overwrite_desc_if_exsit, $del_src_after_migrate);

        // 追加設定
        $this->extra($desc_id);

        $this->write_log("=======FINISH data migration from " . $src_id . " to " . $desc_id . ". overwrite: " . $overwrite_desc_if_exsit . ", del_src: " . $del_src_after_migrate);

	}

}