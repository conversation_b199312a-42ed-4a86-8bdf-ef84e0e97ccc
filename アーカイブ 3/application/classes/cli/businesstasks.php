<?php defined('SYSPATH') or die('No direct script access.');

define("DEBUG", "DEBUG");
define("INFO", " INFO");
define("WARN", " WARN");
define("ERROR", "ERROR");

class BusinessTasks {

	public $_model;
	public $_log_file;

	public function __construct()
	{
		$this->_model = new Model_Basemodel();
	}
	
	public function _business_method_05($params)	//_call_method
	{
		//TODO 05->newsletter
	}
	
	public function _business_method_newsletter($params)	//_call_method
	{
		//TODO
	}
}