<?php defined('SYSPATH') or die('No direct script access.');

class CLI_Linerichmenuupdate {

	public $_model;
	public $_log_file;

	public function __construct()
	{
		$this->_model = new Model_Basemodel();
	}

	private function _logEcho($_to_echo)	//, $line=false
	{
		Log::instance()->add(Log::DEBUG, $_to_echo);
	}

	private function _changingrichmenu($bot_id, $link_id=NULL, $member_id=NULL) {	//$link_id = NULL, $member_id = NULL
		$data = [
			'bot_id' => $bot_id,
			'test' => 0
		];
		return $this->_model->post_enginehook('service', 'line_richmenu_changed', '', $data);
	}

	public function _call_method($bot_id)	//_business_method	$regist_task	$regist_task_data
	{
		$success = true;	//complete success
		$failed = true;		//all failed

		$this->_logEcho('Updating LINE rich menu task calling engine API ...<br/>');

		$engine_response = $this->_changingrichmenu($bot_id);
		$user_num_need_process_final = 0;
		if ( isset($engine_response['success']) && $engine_response['success'] == 'False') {
			Log::instance()->add(Log::DEBUG, 'API service.line_richmenu_changed failure=' . json_encode($engine_response));
			$this->_model->log_error(__FUNCTION__, "API service.line_richmenu_changed failure=" . json_encode($engine_response, JSON_UNESCAPED_UNICODE));

			if( isset($engine_response['error_message']) ) {
				$engine_error_msg = $engine_response['error_message'];
				//TODO deal with error_message=e.stack
				$this->_model->log_error(__FUNCTION__, "API service.line_richmenu_changed error_msg=" . json_encode($engine_error_msg, JSON_UNESCAPED_UNICODE));
			}
			
			$success &= false;
		} else {
			$failed &= false;

			if( isset($engine_response['info_need_process']) ) {
				$info_need_process = $engine_response['info_need_process'];
				//TODO deal with info_need_process
				$user_num_need_process_final = $info_need_process['user_num_need_process_final'];
			}
		}

		$this->_logEcho('user_num_need_process_final='.$user_num_need_process_final.'...<br/>');
		

		// regist task_status_cd
		// 	'00' => '仮登録',
		// 	'01' => '新規',
		// 	'02' => '実行中',
		// 	'03' => '完了（成功）',
		// 	'04' => '完了（警告あり）',
		// 	'05' => '完了（失敗）',
		if ($success) {

			$regist_task_status = '03';
		} else if ($failed) {

			$regist_task_status = '05';
		} else {

			$regist_task_status = '04';
			//TODO warnings handling
		}

		return $regist_task_status;
	}
}