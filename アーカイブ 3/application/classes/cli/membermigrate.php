<?php
defined('SYSPATH') or die('No direct script access.');

class CLI_Membermigrate extends CLI_BaseCli
{
	public $_div = 'member_migrate';
	
	private $_db = 'member2';
	private $_t_member = 't_crm_member';

	private $_member_insert_columns = array('first_name', 'last_name', 'first_name_furigana', 'last_name_furigana', 'post_cd', 'address', 'phone', 'birthday', 'gender');
	private $_t_mprogram = 't_crm_member_program';
	private $_mprogram_insert_columns = array();
	private $_t_mpoint = 't_crm_member_point';	
	private $_mpoint_insert_columns = array('');

	private $_last_member_no = 0;

	private $_default_program_id = 'H00001';//T00004

	public function __construct()
	{
		parent::__construct();
		// $this->_model = new Model_Reportmodel();
	}

	private function _backup_prompt($member_program_select_query, $member_type, $exec=true){
		$member_program_select_sql_prompt = $member_program_select_query->compile($this->_db);
		if ($exec) {
			$members_to_apply = $member_program_select_query->execute($this->_db)->as_array();
			$count = count($members_to_apply);
			$info = "TODO$member_type 修正対象会員数: ".$count;
			$this->_write_log($info);
		} else {
			$count = 0;
		}
		if ($count>0){
			echo "修正対象会員をバックアップしますか? Y: バックアップ用SELECT SQLを提示。 N: 修正UPDATE実行。(Enter for Y)? ";
			$input = rtrim(fgets(STDIN));
			if ($input == 'N' || $input == 'n') {
				echo "修正UPDATE実行...\n";
			} else {
				echo ">>".$member_program_select_sql_prompt."<<\n";
				echo "上記のバックアップSQLをコピーしてphpmyadminでSELECT SQL実行結果の全ての行を選択して、エクスポートよりバックアップしてください(https://admin-dev8.talkappi.com:20001/phpmyadminhonban/index.php?route=/sql&db=member2&table=t_crm_member_program&pos=0)\n";
				$continue_info = "修正対象会員をバックアップしましたか? (y)es: continue, A: abort(中止): ";
				echo $continue_info;
				$input = rtrim(fgets(STDIN));
				while ($input != 'YES' && $input != 'y' && $input != 'Y' && $input != 'yes') {
					if ($input == 'A' || $input == 'a') {
						echo __FUNCTION__."中止しました!\n";
						return;
					}
					echo $continue_info;
					$input = rtrim(fgets(STDIN));
				}
			}
		}
		return $count;
	}

	/**
	 * @param mixed $bot_id
	 * @param mixed $migrate_date
	 * @return void
	 */
	function checkpoints($csv_file="", $program_id='H00001', $checking_members=null, $bot_id = 0, $migrate_date='')
	{
		$this->_write_log("=======START ".__FUNCTION__);
		//read member csv file
		if (!$csv_file) {
			$csv_file = APPPATH . "../../files/temp/member/tmp/point_history/POINT_HISTORY_RPR_20250219124415.csv";//POINT_HISTORY_RPR20241126100243.csv POINT_HISTORY_ミケーラ_20250219124144.txt
		}
		$csv_stream = fopen($csv_file,'r');
		
		$rows_inserted = 0;
		$old_kanji_members_2_update = [];
		$old_kanji_members = [];
		$old_kanji_members_impacted = [];
		$cancel_members = [];
		$impacted_records_count = 0; 
		$cancel_records_count = 0;

		$checkedBOM = false;
		$isJIS = '';
		if ($csv_stream) {
			$line_count = 0;
			$time_start = time();
			$point_history_insert_sql = "INSERT INTO  t_crm_member_point SET process_time = :process_time, shop_cd = :shop_cd, transaction_type = :transaction_type, point_sales_amount = :point_sales_amount, `point` = :point, program_id = :program_id, point_data = :point_data, member_id = (SELECT member_id FROM t_crm_member_program WHERE program_id = :program_id AND old_member_cd = :old_member_cd AND old_member_flg = 1 AND join_channel = 'MI')";
			$point_history_insert_query = DB::query(Database::INSERT, $point_history_insert_sql);
			$rows_updated = 0;
			while (($csv_line = fgets($csv_stream)) !== false) {
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($csv_line, 'SJIS-mac') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($csv_line, 'SJIS-win') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($csv_line, 'SJIS') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$checkedBOM = true;
				} else if ($isJIS) {
					$csv_line = mb_convert_encoding($csv_line, 'UTF-8', $isJIS);
				}
				$csv_row = str_getcsv($csv_line);	//	, "\t"

				if ( $csv_row && $line_count == 0 && $this->_isCSVHeaderLine($csv_row, true) ) {
					$this->_write_log('_isCSVHeaderLine(先頭行は各列のタイトルです): <'.$csv_line.'>');
					continue;
				}
				$old_member_cd = $csv_row[4];
				$shop_cd = $this->_parseShopCode($csv_row[7]);
				$correct_shop_cd = is_numeric($csv_row[7]);
				$point_sales_amount = $csv_row[12];
				$point = $csv_row[13];
				$terminal_cycle_no = $csv_row[10];
				$terminal_identify_number = $csv_row[9];
				$fix_insert = false;
				//2022/11/04 13:58:38
				$process_time = date('Y-m-d H:i:s', strtotime($csv_row[0]));
				switch ($csv_row[11]) {
					case 'ポイント-付与':
						$transaction_type = '01';
						break;
					case 'ポイント-加算':
						$transaction_type = '02';
						break;
					case 'ポイント-減算':
						$transaction_type = '03';
						break;
					case 'ポイント-交換':
						$transaction_type = '04';
						break;
					case 'ポイント-失効':
						$transaction_type = '05';
						break;
					case 'ポイント-カード移行':
						$transaction_type = '06';
						break;
					case 'ポイント-取消':
						$transaction_type = '07';
						break;
					default:
						// $this->_write_log('transaction_type is unknown(取引内容): cd='.$old_member_cd.'>tx='.$csv_row[11].'>name='.$csv_row[5].' '.$csv_row[6].'>shop=(correct_shop_cd?'.$correct_shop_cd.')'.$csv_row[7].'|'.$csv_row[8]
						// .'>terminal='.$csv_row[9].'|'.$csv_row[10].'>point='.$csv_row[13].'/'.$csv_row[12]);
						// if 旧漢字会員(correct_shop_cd is not true), count it and record the summary
						$transaction_type_fix = NULL;
						if (!$correct_shop_cd) {
							if (!in_array($old_member_cd, $old_kanji_members)) {
								$old_kanji_members[] = $old_member_cd;
							}
							$transaction_type_fix = $this->_parseTransactionType($csv_row[10]);
							if ($transaction_type_fix) {
								// 旧漢字会員可能 漏れ対応
								// get correct shop code, amount of sales, point amount and transaction type
								$shop_cd = $this->_parseShopCode($csv_row[6]);
								$point_sales_amount = $csv_row[11];
								$point = $csv_row[12];
								$terminal_cycle_no = $csv_row[9];
								$terminal_identify_number = $csv_row[8];
								$fix_insert = true;
								if ($checking_members && in_array($old_member_cd, $checking_members) && !in_array($old_member_cd, $old_kanji_members_impacted)) {
									if (in_array($transaction_type_fix, ['01','02','04'])) {
										$old_kanji_members_impacted[] = $old_member_cd;
									}
								}
								if (in_array($transaction_type_fix, ['01','02','04'])) {
									if (!array_key_exists($old_member_cd, $old_kanji_members_2_update)) {
										$old_kanji_members_2_update[$old_member_cd] = $process_time;
									} else if ($old_kanji_members_2_update[$old_member_cd] < $process_time) {
										$old_kanji_members_2_update[$old_member_cd] = $process_time;
									}
								}
								$transaction_type = $transaction_type_fix;
							}
							$impacted_records_count ++;
						}
						if (!$transaction_type_fix) continue 2;
				}
				if ($transaction_type == '07') {
					// 07:取消 漏れ対応
					$cancel_records_count ++;
					$fix_insert = true;
					if (!in_array($old_member_cd, $cancel_members)) {
						$cancel_members[] = $old_member_cd;
					}
				}
				$line_count ++;					

				// increment mode
				if ($checking_members && count($checking_members)> 0 && in_array($old_member_cd, $checking_members)) {
					$this->_write_log('Member point history not imported(ポイント履歴があるのに、取り込まれていません) CD:'.$old_member_cd.'>Type:'.$csv_row[11].'>point:'.$point.'/'.$point_sales_amount);
					continue;
				} else if ($checking_members || !$fix_insert) {
					// $this->_write_log('Member point history fixing mode: (ポイント履歴があるので、追加取り込み必要ない) CD:'.$old_member_cd.'>Type:'.$csv_row[11].'>point:'.$csv_row[13].'/'.$csv_row[12]);
					continue;
				} else {
					$this->_write_log('Member point history fixing mode: (ポイント履歴があるのに、取り込まれなかった対象だけ) CD:'.$old_member_cd.'>Type:'.$transaction_type.'>point:'.$point.'/'.$point_sales_amount);
				}

				$point_data = ['old' => ['approval_no' => $csv_row[1], 'sub_no' => $csv_row[2], 'card_master_name' => $csv_row[3], 'TID' => $terminal_identify_number, 'terminal_cycle_no' => $terminal_cycle_no]];
				$point_data_json = json_encode($point_data, JSON_UNESCAPED_UNICODE);
				// $this->_write_log('CSV line: '.json_encode($csv_row, JSON_UNESCAPED_UNICODE));
				$point_history_insert_query->parameters(array(
					':program_id' => $program_id,
					':old_member_cd' => $old_member_cd,
					':shop_cd' => $shop_cd, //店舗コード
					':transaction_type' => $transaction_type,
					':point_sales_amount' => $point_sales_amount,
					':point' => $point,
					':point_data' => $point_data_json,
					':process_time' => $process_time,
				));
				try {
					$point_history_inserted = $point_history_insert_query->execute($this->_db); // member_id
					// $this->_write_log('inserted: '.json_encode($point_history_inserted, JSON_UNESCAPED_UNICODE));
					if (!$point_history_inserted) {
						$this->_write_log('Failed to insert point_history: '.$old_member_cd);
						continue;
					} else if (is_array($point_history_inserted) && count($point_history_inserted) == 2) {
						$rows_updated += $point_history_inserted[1];
					}
					$info = "point_history table inserted: recode no = $point_history_inserted[0], old member_cd = $old_member_cd ";
					$this->_write_log($info);
					// break;//TODO debug
				} catch (mysqli_sql_exception $e) {
					if ($e->getCode() == 1062) {
						$this->_write_log('Duplicate entry(1062重複エントリ): '.$old_member_cd.' '.$e->getMessage());
						continue;
					} else if ($e->getCode() == 1048 && strpos($e->getMessage(), "Column 'member_id' cannot be null") !== false) {
						$this->_write_log('会員情報はt_crm_memberに取り込みされていないので、point_historyに挿入できません: '.$csv_row[4]);
						continue;
					} else {
						throw $e;
					}
				}	
			}
			$info = "point_history checked count (out of $line_count lines) : $program_id > $rows_updated ";
			$this->_write_log($info);

			if ($checking_members && count($checking_members)> 0) {
				$info = "point_history checked impacted_records_count (旧漢字名前など原因)取り込まなかったポイント履歴記録数(このファイル内) : $impacted_records_count ";
				$this->_write_log($info);
				$info = "point_history checked old_kanji_members (旧漢字名前など原因)取り込まなかったポイント履歴記録がある(このファイル内)旧会員コード: ".implode(',', $old_kanji_members);
				$this->_write_log($info);
				$info = "point_history checked old_kanji_members (旧漢字名前など原因)取り込まなかったポイント履歴記録がある(このファイル内)旧会員の中に、結局ポイント有効期限が不正になった旧会員コード: ".implode(',', $old_kanji_members_impacted);
				$this->_write_log($info);
				$info = "point_history checked cancel_records_count (取り消し>取消原因で)取り込まなかったポイント履歴記録数(このファイル内) : $cancel_records_count ";
				$this->_write_log($info);
				return [$cancel_records_count, $cancel_members, $impacted_records_count, $old_kanji_members, $old_kanji_members_impacted];
			}
		}
		return $old_kanji_members_2_update;
		$this->_write_log("=======END ".__FUNCTION__);
	}

	function checkpointsdiff($csv_file="", $program_id='H00001', $diff_not_matched=null, $start_date="2025/02/17", $bot_id = 0, $migrate_date='')
	{
		// $this->_write_log("=======START ".__FUNCTION__);
		//read member csv file
		if (!$csv_file) {
			$csv_file = APPPATH . "../../files/temp/member/tmp/point_history/POINT_HISTORY_RPR_20250219124415.csv";//POINT_HISTORY_RPR20241126100243.csv POINT_HISTORY_ミケーラ_20250219124144.txt
		}
		$csv_stream = fopen($csv_file,'r');
		
		$rows_inserted = 0;
		$impacted_records_count = 0; 
		//old_member_cd => member_id
		$member_cd_id_map = ["R61U007712"=>3801, "R61U000466"=> 10259, "R61U000549"=> 10188, "R61U001084"=> 9744, "R61U001122"=> 9707, "R61U001276"=> 9583, "R61U003347"=> 7733, "R61U003376"=> 7707, "R61U007382"=> 4097, "R61U007712"=> 3801, "R61U008681"=> 2930, "R61U010482"=> 1231, "R61U010548"=> 1175, "R61U010758"=> 980, "R61U010965"=> 796, "R61U011187"=> 602, "R61U013421"=> 319, "R61U013725"=> 116, "R61U013827"=> 61];
;

		$checkedBOM = false;
		$isJIS = '';
		if ($csv_stream) {
			$line_count = 0;
			$time_start = time();
			$point_history_insert_sql = "INSERT INTO  t_crm_member_point SET process_time = :process_time, shop_cd = :shop_cd, transaction_type = :transaction_type, point_sales_amount = :point_sales_amount, `point` = :point, program_id = :program_id, point_data = :point_data, member_id = (SELECT member_id FROM t_crm_member_program WHERE program_id = :program_id AND old_member_cd = :old_member_cd AND old_member_flg = 1 AND join_channel = 'MI')";
			$point_history_insert_query = DB::query(Database::INSERT, $point_history_insert_sql);
			$rows_updated = 0;
			while (($csv_line = fgets($csv_stream)) !== false) {
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($csv_line, 'SJIS-mac') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($csv_line, 'SJIS-win') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($csv_line, 'SJIS') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$checkedBOM = true;
				} else if ($isJIS) {
					$csv_line = mb_convert_encoding($csv_line, 'UTF-8', $isJIS);
				}
				$csv_row = str_getcsv($csv_line);	//	, "\t"

				if ( $csv_row && $line_count == 0 && $this->_isCSVHeaderLine($csv_row, true) ) {
					// $this->_write_log('_isCSVHeaderLine(先頭行は各列のタイトルです): <'.$csv_line.'>');
					continue;
				}
				$old_member_cd = $csv_row[4];
				$shop_cd = $this->_parseShopCode($csv_row[7]);
				$correct_shop_cd = is_numeric($csv_row[7]);
				$point_sales_amount = $csv_row[12];
				$point = $csv_row[13];
				$terminal_cycle_no = $csv_row[10];
				$terminal_identify_number = $csv_row[9];
				$fix_insert = false;
				switch ($csv_row[11]) {
					case 'ポイント-付与':
						$transaction_type = '01';
						break;
					case 'ポイント-加算':
						$transaction_type = '02';
						break;
					case 'ポイント-減算':
						$transaction_type = '03';
						break;
					case 'ポイント-交換':
						$transaction_type = '04';
						break;
					case 'ポイント-失効':
						$transaction_type = '05';
						break;
					case 'ポイント-カード移行':
						$transaction_type = '06';
						break;
					case 'ポイント-取消':
						$transaction_type = '07';
						break;
					default:
						// $this->_write_log('transaction_type is unknown(取引内容): cd='.$old_member_cd.'>tx='.$csv_row[11].'>name='.$csv_row[5].' '.$csv_row[6].'>shop=(correct_shop_cd?'.$correct_shop_cd.')'.$csv_row[7].'|'.$csv_row[8]
						// .'>terminal='.$csv_row[9].'|'.$csv_row[10].'>point='.$csv_row[13].'/'.$csv_row[12]);
						// if 旧漢字会員(correct_shop_cd is not true), fix it
						$transaction_type_fix = NULL;
						if (!$correct_shop_cd) {
							$transaction_type_fix = $this->_parseTransactionType($csv_row[10]);
							if ($transaction_type_fix) {
								// 旧漢字会員可能 カラム列ズレ対応
								// get correct shop code, amount of sales, point amount and transaction type
								$shop_cd = $this->_parseShopCode($csv_row[6]);
								$point_sales_amount = $csv_row[11];
								$point = $csv_row[12];
								$terminal_cycle_no = $csv_row[9];
								$terminal_identify_number = $csv_row[8];
								$transaction_type = $transaction_type_fix;
							}
							// $impacted_records_count ++;
						}
						if (!$transaction_type_fix) continue 2;
				}
				$line_count ++;					
				//2022/11/04 13:58:38
				$process_time = date('Y-m-d H:i:s', strtotime($csv_row[0]));
				$check_time = strtotime($start_date);
				if ($member_cd_id_map && array_key_exists($old_member_cd, $member_cd_id_map) && strtotime($csv_row[0]) >= $check_time) {
					// 新取り引き可能 漏れ対応
					$fix_insert = true;
					if ( in_array($transaction_type, ['01', '02']) ) {
						// $checking_members[$old_member_cd] = $checking_members[$old_member_cd] - $point;
						$impacted_records_count ++;
					} else if ( in_array($transaction_type, ['03', '04', '05', '06', '07']) ) {
						// $checking_members[$old_member_cd] = $checking_members[$old_member_cd] + $point;
						$impacted_records_count ++;
					} else {
						$this->_write_log('[tx_type unexcepted]Member point history fixing mode: (ポイント履歴があるのに、取り込まれなかった対象だけ) CD:'.$old_member_cd.'>Type:'.$transaction_type.'>point:'.$point.'/'.$point_sales_amount);
						continue;
					}
					// $this->_write_log('Member point history fixing mode: (ポイント履歴があるのに、取り込まれなかった対象だけ) CD:'.$old_member_cd.'>Type:'.$transaction_type.'>point:'.$point.'/'.$point_sales_amount.'>process_time:'.$process_time);
					// continue;
				} else {
					continue;
				}

				$point_data = ['old' => ['approval_no' => $csv_row[1], 'sub_no' => $csv_row[2], 'card_master_name' => $csv_row[3], 'TID' => $terminal_identify_number, 'terminal_cycle_no' => $terminal_cycle_no]];
				$point_data_json = json_encode($point_data, JSON_UNESCAPED_UNICODE);
				// $this->_write_log('CSV line: '.json_encode($csv_row, JSON_UNESCAPED_UNICODE));
				$point_history_insert_query->parameters(array(
					':program_id' => $program_id,
					':old_member_cd' => $old_member_cd,
					':shop_cd' => $shop_cd, //店舗コード
					':transaction_type' => $transaction_type,
					':point_sales_amount' => $point_sales_amount,
					':point' => $point,
					':point_data' => $point_data_json,
					':process_time' => $process_time,
				));
				try {
					$point_history_insert_sql = $point_history_insert_query->compile($this->_db); //TODO member_id
					echo "$point_history_insert_sql;".PHP_EOL;
					$info = "point_history table to insert: recode TID = $terminal_identify_number, old member_cd = $old_member_cd, process_time = $process_time ";

					/* not insert in batch, only output insert sql
					$point_history_inserted = $point_history_insert_query->execute($this->_db); // member_id
					$this->_write_log('inserted: '.json_encode($point_history_inserted, JSON_UNESCAPED_UNICODE));
					if (!$point_history_inserted) {
						$this->_write_log('Failed to insert point_history: '.$old_member_cd);
						continue;
					} else if (is_array($point_history_inserted) && count($point_history_inserted) == 2) {
						$rows_updated += $point_history_inserted[1];
					}
					$info = "point_history table inserted: recode no = $point_history_inserted[0], old member_cd = $old_member_cd ";
					$this->_write_log($info);
					*/
					// break;//TODO debug
				} catch (mysqli_sql_exception $e) {
					if ($e->getCode() == 1062) {
						$this->_write_log('Duplicate entry(1062重複エントリ): '.$old_member_cd.' '.$e->getMessage());
						continue;
					} else if ($e->getCode() == 1048 && strpos($e->getMessage(), "Column 'member_id' cannot be null") !== false) {
						$this->_write_log('会員情報はt_crm_memberに取り込みされていないので、point_historyに挿入できません: '.$csv_row[4]);
						continue;
					} else {
						throw $e;
					}
				}	
			}
			// $info = "point_history updated count (out of $line_count lines) : $program_id > $rows_updated ";
			// $this->_write_log($info);

			if ($member_cd_id_map && count($member_cd_id_map)> 0) {
				$info = "point_history checked diff count 取り込まなかったポイント履歴記録数(このファイル内) : $impacted_records_count ";
				$this->_write_log($info);
			}
		}
		return $member_cd_id_map;
		$this->_write_log("=======END ".__FUNCTION__);
	}

	/**
	 * csv_file files/temp/member/MEMBER_CARD.csv
	 * @param mixed $bot_id
	 * @param mixed $migrate_date
	 */
	public function checkbalance($csv_file='', $program_id='H00001', $checking_members=null, $bot_id = 0, $migrate_date='')
	{
		$this->_write_log("=======START ".__FUNCTION__);
		//read member csv file
		$csv_stream = fopen($csv_file,'r');
		
		$rows_detected = 0;
		// $unused_detected = [];
		// $zero_detected = [];
		// $others_detected = [];
		$diff_matched = [];
		$diff_not_matched = [];
		$diff_not_matched_exp = [];
		$members_point_balances = $checking_members[0];
		$members_point_differences = $checking_members[1];

		$checkedBOM = false;

		$isJIS = '';
		if ($csv_stream) {
			$line_count = 0;
			$time_start = time();
			while (($csv_line = fgets($csv_stream)) !== false) {
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($csv_line, 'SJIS-mac') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($csv_line, 'SJIS-win') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($csv_line, 'SJIS') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$checkedBOM = true;
				} else if ($isJIS) {
					$csv_line = mb_convert_encoding($csv_line, 'UTF-8', $isJIS);
				}
				$csv_row = str_getcsv($csv_line);	//	, "\t"

				if ( $csv_row && $line_count == 0 && $this->_isCSVHeaderLine($csv_row, true) ) {
					$this->_write_log('_isCSVHeaderLine(先頭行は各列のタイトルです): <'.$csv_line.'>');
					continue;
				}
				$line_count ++;					
				$point_balance = $csv_row[8];
				$old_member_cd = $csv_row[3];


				// expiration_time = 2104/06, parse from yyyy/MM to date 
				$expiration_time = $csv_row[13];
				$last_used_time = $csv_row[12];
				$expiration_time = date_parse_from_format('Y/m', $expiration_time);
				$expiration_time = $expiration_time['year'].'-'.$expiration_time['month'].'-01';
				// last day of month
				$expiration_time = date('Y-m-t 23:59:59', strtotime($expiration_time));
				$extended_expiration_time = date('Y-m-t 23:59:59', strtotime('+1 year', strtotime($last_used_time)));

				// カード状態 check if card is active
				if ($checking_members && ($csv_row[9] == '未使用' || $csv_row[9] == '使用中') && array_key_exists($old_member_cd, $members_point_balances) && $point_balance != $members_point_balances[$old_member_cd]) {
					//point_balance = 0
					// $this->_write_log('Card point balance diff is not ingested(残高差分未取り込み): '.$old_member_cd.'<'.$csv_row[9].'>name='.$csv_row[4].' '.$csv_row[5].'>balance(CSV/DB)='.$point_balance.'/'.$checking_members[$old_member_cd]);
					$rows_detected ++;
					if ($csv_row[9] == '未使用') {
						// $unused_detected [] = $old_member_cd;
					} else if ($point_balance == 0) {
						// $zero_detected []= $old_member_cd;
					} else {
						// $others_detected []= $old_member_cd;
						if (array_key_exists($old_member_cd, $members_point_differences) && $point_balance == $members_point_balances[$old_member_cd] - $members_point_differences[$old_member_cd]) {
							$diff_matched[$old_member_cd] = $old_member_cd;
						} else {
							$diff = 0;
							if (array_key_exists($old_member_cd, $members_point_differences)) {
								$diff = $members_point_differences[$old_member_cd];
							}
							$todo = $point_balance - ($members_point_balances[$old_member_cd] - $diff) ;
							$diff_not_matched[$old_member_cd] = $todo;
							$diff_not_matched_exp[$old_member_cd] = $extended_expiration_time;
							// $this->_write_log('Card point balance diff is not ingested(残高差分未取り込み): '.$old_member_cd.'<'.$csv_row[9].'>name='.$csv_row[4].' '.$csv_row[5].'>balance(CSV/DB/diff/todo)='.$point_balance.'/'.$members_point_balances[$old_member_cd].'/'.$diff.'/'.$todo);
						}
					}
					continue;
				}
				// $this->_write_log('CSV line: '.json_encode($csv_row, JSON_UNESCAPED_UNICODE));
				
					// ':old_member_cd' => $old_member_cd,
					// ':point_balance' => $csv_row[8],
				
				// $this->_write_log('SQL: '.$member_program_update_query->compile(db: $this->_db));
				// $info = "member_program table updated: $program_id > old member_cd = $old_member_cd ".($updated ? 'カード情報更新成功':'会員データが見つかりません');
				// $this->_write_log($info);
			}
			// $info = "member_program table point_balance diff detected count (out of $line_count lines) : $program_id > $rows_detected ";
			// $this->_write_log($info);
			// $info = "among which(中に) unused(未使用) count : $program_id >  ".implode(',', $unused_detected);
			// $this->_write_log($info);
			// $info = "CSV zero balance(残高0) count : $program_id > ".implode(',', $zero_detected);
			// $this->_write_log($info);
			// $info = "others(他) count : $program_id > ".implode(',', $others_detected);
			// $info = "point balance difference matched(差分合う) count : $program_id > ".implode(',', $diff_matched);
			// $this->_write_log($info);
			// $info = "diff not matched(差分合わない) count : $program_id > ".json_encode($diff_not_matched);
			// $this->_write_log($info);
		}
		$this->_write_log("=======END ".__FUNCTION__);
		if ($migrate_date=='') {
			$migrate_date = date('Y-m-d H:i:s');
		}
		return [$diff_not_matched, $diff_not_matched_exp];
		// $this->_admin_model->log_info(__FUNCTION__, "パラメータ:migrate_date=$migrate_date 結果:success", 0);
	}

	private function _check_point_history_files($target_folder, $program_id='H00001', $target_member_cds=null){		
		$point_history_csv_files = glob($target_folder."POINT_HISTORY_*.{txt}", GLOB_BRACE);
		$target_member_data_file = $target_folder."target_members.dat";
		$info = 'checking target_member_data_file(ポイント履歴取り込み漏れ確認対象データ):'.$target_member_data_file.' サイズ:'.filesize($target_member_data_file);
		$data_stream = fopen($target_member_data_file,'r');
		while (($data_line = fgets($data_stream)) !== false) {
			$target_members_string = trim($data_line);
			$target_member_cds = json_decode($target_members_string);
			echo "有効期限不正データ対象旧会員数target members count:" . count($target_member_cds)."\n";
		}
		$info = '対象CSV count:'.count($point_history_csv_files);
		$this->_write_log($info);
		$impacted_records_count_total = 0;
		$old_kanji_members_total = [];
		$old_kanji_members_impacted_total = [];
		$cancel_records_count_total = 0;
		$cancel_members_total = [];
		foreach($point_history_csv_files as $point_history_csv_file) {
			$info = 'checking point_history_csv_file(ポイント履歴取り込み漏れ確認開始):'.$point_history_csv_file.' サイズ:'.filesize($point_history_csv_file);
			$this->_write_log($info);
			list($cancel_records_count, $cancel_members, $impacted_records_count, $old_kanji_members, $old_kanji_members_impacted) = $this->checkpoints($point_history_csv_file, $program_id, $target_member_cds);
			// addup
			$impacted_records_count_total += $impacted_records_count;
			$old_kanji_members_total = array_merge($old_kanji_members_total, $old_kanji_members);
			$old_kanji_members_impacted_total = array_merge($old_kanji_members_impacted_total, $old_kanji_members_impacted);
			$cancel_records_count_total += $cancel_records_count;
			$cancel_members_total = array_merge($cancel_members_total, $cancel_members);
			// break;
		}

		$info = "point_history checked impacted_records_count_total (旧漢字名前など原因)取り込まなかったポイント履歴記録数(総括) : $impacted_records_count_total ";
		$this->_write_log($info);
		$info = "point_history checked old_kanji_members_total (旧漢字名前など原因)取り込まなかったポイント履歴記録がある(総括)旧会員コード: count=".count($old_kanji_members_total).'	'.implode(',', $old_kanji_members_total);
		$this->_write_log($info);
		$info = "point_history checked old_kanji_members_impacted_total (旧漢字名前など原因)取り込まなかったポイント履歴記録がある(総括)旧会員の中に、結局ポイント有効期限が不正になった旧会員コード: count=".count($old_kanji_members_impacted_total).'	'.implode(',', $old_kanji_members_impacted_total);
		$this->_write_log($info);
		$info = "point_history checked cancel_records_count_total (取り消し>取消原因で)取り込まなかったポイント履歴記録数(総括) : $cancel_records_count_total ";
		$this->_write_log($info);
		$info = "point_history checked cancel_members_total (取り消し>取消原因で)取り込まなかったポイント履歴記録がある(総括)旧会員コード : count=".count($cancel_members_total).'	'.implode(',', $cancel_members_total);
		$this->_write_log($info);
	}

	private function _check_update_point_history_files($target_folder, $program_id='H00001'){		
		$point_history_csv_files = glob($target_folder."POINT_HISTORY_*.{csv}", GLOB_BRACE);
		$info = '対象CSV count:'.count($point_history_csv_files);
		$this->_write_log($info);
		$old_kanji_members_2_update_total = [];
		foreach($point_history_csv_files as $point_history_csv_file) {
			$info = 'checking update point_history_csv_file(ポイント履歴取り込み漏れ確認開始):'.$point_history_csv_file.' サイズ:'.filesize($point_history_csv_file);
			$this->_write_log($info);
			$old_kanji_members_2_update = $this->checkpoints($point_history_csv_file, $program_id);
			// addup
			// $old_kanji_members_2_update_total = array_merge($old_kanji_members_2_update_total, $old_kanji_members_2_update);
			foreach($old_kanji_members_2_update as $old_member_cd => $last_process_time) {
				if (!array_key_exists($old_member_cd, $old_kanji_members_2_update_total)) {
					$old_kanji_members_2_update_total[$old_member_cd] = $last_process_time;
				} else if ($old_kanji_members_2_update_total[$old_member_cd] < $last_process_time) {
					$old_kanji_members_2_update_total[$old_member_cd] = $last_process_time;
				}
			}
		}
		$info = "point_history checked old_kanji_members_2_update_total (旧漢字名前など原因)取り込まなかったポイント履歴記録がある(総括)旧会員コード: count=".count($old_kanji_members_2_update_total).'	'.implode(',', array_keys($old_kanji_members_2_update_total));
		$this->_write_log($info);
		echo "point_history checked old_kanji_members_2_update point & upd: ";
		foreach($old_kanji_members_2_update_total as $old_member_cd => $last_process_time) {
			// last second of last day of month
			$extended_expiration_time = date('Y-m-t 23:59:59', strtotime('+1 year', strtotime($last_process_time)));
			echo $old_member_cd.'>'.$extended_expiration_time.' \n';
		}
		echo "\n";
	}

	private function _check_diff_update_point_history_files($target_folder, $program_id='H00001', $diff_not_matched=null){		
		$point_history_csv_files = glob($target_folder."POINT_HISTORY_*.{csv}", GLOB_BRACE);
		$info = '対象CSV files count:'.count($point_history_csv_files);
		$this->_write_log($info);
		foreach($point_history_csv_files as $index=>$point_history_csv_file) {
			$info = '['.($index+1).']checking update point_history_csv_file(ポイント履歴取り込み漏れ確認開始):'.$point_history_csv_file.' サイズ:'.filesize($point_history_csv_file);
			$this->_write_log($info);
			$diff_not_matched = $this->checkpointsdiff($point_history_csv_file, $program_id, $diff_not_matched);

			// echo "point_history checked checkpointsdiff point & upd: ";
			// foreach($diff_not_matched as $old_member_cd => $point) {
				// if ($point != 0) echo $old_member_cd.'>'.$point.' \n';
			// }
			echo "\n";
		}
	}

	private function _check_member_cards($target_folder, $program_id='H00001', $target_member_cds=null){
		$card_csv_files = glob($target_folder."MEMBER_CARD_*.{txt}", GLOB_BRACE);
		$target_member_data_file = $target_folder."target_members_p.dat";
		$info = 'checking target_member_data_file(ポイント残高取り込み漏れ確認対象残高データ):'.$target_member_data_file.' サイズ:'.filesize($target_member_data_file);
		$this->_write_log($info);
		$data_stream = fopen($target_member_data_file,'r');
		while (($data_line = fgets($data_stream)) !== false) {
			$target_members_string = trim($data_line);
			$target_member_cds_points = json_decode($target_members_string, true);
			$target_member_cds_points['R61U007712'] = 0;//移行前ポイント失効、移行後(2025/02/17 19:48:00)取り引きあります: 付与136点(店舗0001:ミケーラ承認番号151686441
			$target_member_cds = [$target_member_cds_points];
			echo "残高取り込み漏れ確認対象旧会員数target members count:" . count($target_member_cds[0])."\n";
		}
		fclose($data_stream);
		$target_member_data_file2 = $target_folder."target_members_pd.dat";
		$info = 'checking target_member_data_file(ポイント残高取り込み漏れ確認対象差分データ):'.$target_member_data_file2.' サイズ:'.filesize($target_member_data_file2);
		$this->_write_log($info);
		$data_stream2 = fopen($target_member_data_file2,'r');
		while (($data_line = fgets($data_stream2)) !== false) {
			$target_members_string = trim($data_line);
			$target_member_cds_points_differences = json_decode($target_members_string, true);
			$target_member_cds []= $target_member_cds_points_differences;
			echo "残高取り込み漏れ対象旧会員数差分target members count:" . count($target_member_cds[1])."\n";
		}
		$this->_write_log($info);
		$impacted_records_count_total = 0;
		$old_kanji_members_total = [];
		$old_kanji_members_impacted_total = [];
		$cancel_records_count_total = 0;
		$cancel_members_total = [];
		foreach($card_csv_files as $card_csv_file) {
			$info = 'checking point_history_csv_file(ポイント残高取り込み漏れ確認開始):'.$card_csv_file.' サイズ:'.filesize($card_csv_file);
			$this->_write_log($info);
			$diff_not_matched_members = $this->checkbalance($card_csv_file, $program_id, $target_member_cds);
			return $diff_not_matched_members;
			// addup
			// $impacted_records_count_total += $impacted_records_count;
			// $old_kanji_members_total = array_merge($old_kanji_members_total, $old_kanji_members);
			// $old_kanji_members_impacted_total = array_merge($old_kanji_members_impacted_total, $old_kanji_members_impacted);
			// $cancel_records_count_total += $cancel_records_count;
			// $cancel_members_total = array_merge($cancel_members_total, $cancel_members);
			// break;
		}
	}			

	public function check_point_data($program_id='H00001') {
		$it = false;
		echo "旧会員修正結果checkに、ポイント残高確認...\n";
		if (!$program_id) {
			$this->_write_log("=======START ".__FUNCTION__);
			echo "会員プログラムコードは(Enter for デフォルト H00001)? ";
			$input = rtrim(fgets(STDIN));
			if ($input) {
				$program_id = $input;
			}
			echo "program_id:" . $program_id."\n";
			$it = true;
		}
		$target_member_cds = null;
		try {
			$target_folder = APPPATH . "../../files/temp/member/tmp/point_history/";
			$info = 'ポイント履歴取り込み漏れ確認フォルダー:'.$target_folder;
			$this->_write_log($info);
			if (!$program_id) $program_id = $this->_default_program_id;
			// $this->_check_update_point_history_files($target_folder, $program_id);
			// $this->_check_point_history_files($target_folder, $program_id, $target_member_cds);
			// $members_2_update = $this->_check_member_cards($target_folder, $program_id, $target_member_cds);
			// $members_2_update_points_diff = $members_2_update[0];
			// $info = 'ポイント履歴取り込み漏れ有効期限check:'.json_encode($members_2_update[1]);
			// $this->_write_log($info);
			$this->_check_diff_update_point_history_files($target_folder, $program_id);
			// $info = 'ポイント履歴取り込み漏れupdated:'.json_encode($members_2_update[0]);
			// $this->_write_log($info);
		} catch (Exception $e) {
			$this->_write_log('Error: '.$e->getMessage(). ' on line '.$e->getLine());
		}
		$this->_write_log("=======END ".__FUNCTION__);
	}

	public function succeed_balance_exp_fix($program_id='H00001'){
		$checking_members = [
"R61U007712"=>136,"R61U013827"=>6,"R61U013725"=>226,"R61U013421"=>1974,"R61U011187"=>3,"R61U010965"=>76,"R61U010758"=>15,"R61U010548"=>49,"R61U010482"=>60,"R61U008681"=>128,"R61U007382"=>65,"R61U003376"=>6,"R61U003347"=>13,"R61U001276"=>10,"R61U001122"=>15,"R61U001084"=>84,"R61U000549"=>10,"R61U000466"=>25
		];
		$it = false;
		echo "新旧会員残高・有効期限修正...\n";
		// $program_id = '';
		if (!$program_id) {
			$this->_write_log("=======START ".__FUNCTION__);
			echo "会員プログラムコードは(Enter for デフォルト $this->_default_program_id)? ";
			$input = rtrim(fgets(STDIN));
			if ($input) {
				$program_id = $input;
			} else {
				$program_id = $this->_default_program_id;
			}
			echo "program_id:" . $program_id."\n";
			$it = true;
		}
		// backup information prompt before apply update
		$member_set_clause = "old_member_cd=:old_member_cd AND program_id=:program_id";
		$old_member_cd_array = "";
		foreach (array_keys($checking_members) as $index=>$old_member_cd ) {
			if ($index>0) $old_member_cd_array .= ",";
			$old_member_cd_array .= "'".$old_member_cd."'";
		}
		$member_select_clause = "old_member_cd IN ($old_member_cd_array) AND program_id=:program_id";
		$member_program_select_sql = "SELECT * FROM t_crm_member_program WHERE $member_select_clause";
		$member_program_select_query = DB::query(Database::SELECT, $member_program_select_sql);
		$member_program_select_query->parameters(array(
			':program_id' => $program_id,
			':old_member_cd' => implode(',', array_keys($checking_members)),
		));
		$count = $this->_backup_prompt($member_program_select_query, "新旧会員残高・有効期限修正で", false);

		if ($count>=0){
			// apply update
			$rows_updated = 0;
			$expiration_time = "2026-02-28 23:59:59";
			$invalid_expiration_time = "2030-01-01";
			$member_program_update_sql = "UPDATE t_crm_member_program SET point_balance = point_balance+(:point_to_add), expiration_time = IF(expiration_time IS NULL OR expiration_time>=:invalid_expiration_time, :expiration_time ,GREATEST(expiration_time, :expiration_time)) WHERE $member_set_clause";
			$member_program_update_query = DB::query(Database::UPDATE, $member_program_update_sql);
			foreach ($checking_members as $old_member_cd => $point_to_add) {
				$member_program_update_query->parameters(array(
					':program_id' => $program_id,
					':point_to_add' => $point_to_add,
					':old_member_cd' => $old_member_cd,
					':expiration_time' => $expiration_time,
					':invalid_expiration_time' => $invalid_expiration_time,
				));
				$member_program_update_sql_prompt = $member_program_update_query->compile($this->_db);
				echo $member_program_update_sql_prompt.';'.PHP_EOL;

				// $updated = $member_program_update_query->execute($this->_db);
				// $rows_updated += $updated;
				// $info = "member_program table updated: old_member_cd: $old_member_cd >".($updated ? '有効期限'.$expiration_time.'更新成功'.$updated:'更新された会員データが見つかりません');
				// $this->_write_log($info);
			}
			$info = "member_program table updated: $program_id > ".($rows_updated ? '有効期限更新成功count '.$rows_updated:'更新された会員データが見つかりません');
			$this->_write_log($info);
		}
		if ($it) {
			$this->_write_log("=======END ".__FUNCTION__);
		}
	}

	public function succeed_exp($program_id=''){
		$it = false;
		echo "新旧会員引続修正...\n";
		if (!$program_id) {
			$this->_write_log("=======START ".__FUNCTION__);
			echo "会員プログラムコードは(Enter for デフォルト $this->_default_program_id)? ";
			$input = rtrim(fgets(STDIN));
			if ($input) {
				$program_id = $input;
			} else {
				$program_id = $this->_default_program_id;
			}
			echo "program_id:" . $program_id."\n";
			$it = true;
		}
		// backup information prompt before apply update
		$old_member_clause = "expiration_time IS NOT NULL AND old_member_flg = 1 AND join_channel= 'MI' AND old_member_cd IS NOT NULL AND member_status='98' AND program_id=:program_id";
		$new_member_clause = "expiration_time IS NULL AND old_member_flg IS NULL AND join_channel!= 'MI' AND old_member_cd IS NOT NULL AND program_id=:program_id";
		$member_program_select_sql = "SELECT * FROM t_crm_member_program WHERE $new_member_clause AND old_member_cd IN (SELECT old_member_cd FROM t_crm_member_program WHERE $old_member_clause)";
		$member_program_select_query = DB::query(Database::SELECT, $member_program_select_sql);
		$member_program_select_query->parameters(array(
			':program_id' => $program_id,
		));
		$count = $this->_backup_prompt($member_program_select_query, "旧会員ポイント有効期限引き続いて");

		if ($count>0){
			$old_member_program_select_sql = "SELECT old_member_cd, expiration_time FROM t_crm_member_program WHERE $old_member_clause";
			$old_member_program_select_query = DB::query(Database::SELECT, $old_member_program_select_sql);
			$old_member_program_select_query->parameters(array(
				':program_id' => $program_id,
			));
			$old_members_to_refer = $old_member_program_select_query->execute($this->_db)->as_array('old_member_cd', 'expiration_time');
			$info = "参照できる旧会員ポイント有効期限データ数: ".count($old_members_to_refer);
			$this->_write_log($info);
			// apply update
			$rows_updated = 0;
			$member_program_update_sql = "UPDATE t_crm_member_program SET expiration_time = :expiration_time, upd_time=:upd_time, upd_user=NULL WHERE $new_member_clause AND old_member_cd = :old_member_cd";
			$member_program_update_query = DB::query(Database::UPDATE, $member_program_update_sql);
			foreach ($old_members_to_refer as $old_member_cd => $expiration_time) {
				$member_program_update_query->parameters(array(
					':program_id' => $program_id,
					':old_member_cd' => $old_member_cd,
					':expiration_time' => $expiration_time,
					':upd_time' => date('Y-m-d H:i:s'),
				));
				$updated = $member_program_update_query->execute($this->_db);
				$rows_updated += $updated;
				$info = "member_program table updated: old_member_cd: $old_member_cd >".($updated ? '有効期限'.$expiration_time.'更新成功'.$updated:'更新された会員データが見つかりません');
				$this->_write_log($info);
			}
			$info = "member_program table updated: $program_id > ".($rows_updated ? '有効期限更新成功count '.$rows_updated:'更新された会員データが見つかりません');
			$this->_write_log($info);
		}
		if ($it) {
			$this->_write_log("=======END ".__FUNCTION__);
		}
	}

	// extend expiration time to end of month
	public function extend_exp_EOM($program_id='H00001'){
		echo "月の途中>月末修正...\n";
		// backup information prompt before apply update
		$member_program_select_sql = "SELECT * FROM t_crm_member_program WHERE expiration_time IS NOT NULL AND expiration_time!=DATE_ADD(LAST_DAY(expiration_time), INTERVAL '23:59:59' HOUR_SECOND) AND program_id=:program_id";
		$member_program_select_query = DB::query(Database::SELECT, $member_program_select_sql);
		$member_program_select_query->parameters(array(
			':program_id' => $program_id,
		));
		$count = $this->_backup_prompt($member_program_select_query, "月の途中から月末最後へ");

		if ($count>0){
			// apply update
			$rows_updated = 0;
			$member_program_update_sql = "UPDATE t_crm_member_program SET expiration_time = DATE_ADD(LAST_DAY(expiration_time), INTERVAL '23:59:59' HOUR_SECOND) WHERE expiration_time IS NOT NULL AND program_id=:program_id";
			$member_program_update_query = DB::query(Database::UPDATE, $member_program_update_sql);
			$member_program_update_query->parameters(array(
				':program_id' => $program_id,
			));
			$rows_updated = $member_program_update_query->execute($this->_db);
			$info = "member_program table updated: $program_id > ".($rows_updated ? '名様有効期限更新成功'.$rows_updated:'更新された会員データが見つかりません');
			$this->_write_log($info);
		}
	}

	public function fix_exp_time($program_id='H00001', $old_member_flg=1, $invalid_expiration_time_start='2030-01-01', $apply_default_expiration_time=false, $default_expiration_time='2025-03'){
		$this->_write_log("=======START ".__FUNCTION__);
		echo "会員プログラムコードは(Enter for デフォルト H00001)? ";
		$input = rtrim(fgets(STDIN));
		if ($input) {
			$program_id = $input;
		}
		echo "program_id:" . $program_id."\n";
		echo "1: 旧会員だけ有効期限修正 0: 新会員有効期限修正 2: 新旧会員引続修正(Enter for デフォルト 1 旧会員有効期限修正)? ";
		$input = rtrim(fgets(STDIN));
		if ($input || $input == 0) {
			if ($old_member_flg == 2){//新旧会員引続修正: 新会員で、ポイント付与されていないけど、旧会員からポイントを引き継いでいて、有効期限がNULLとなるケース
				//TODO function
				// calculate the targets
			}
			$old_member_flg = $input;
		}
		echo $old_member_flg==1?"旧会員有効期限修正...\n":"新会員有効期限修正...\n";
		echo "判断基準として不正な有効期限日付は何日以後(Enter for デフォルト 2030-01-01)? ";
		$input = rtrim(fgets(STDIN));
		if ($input) {
			$invalid_expiration_time_start = $input;
		}
		echo "有効期限が不正な日付基準は '$invalid_expiration_time_start 以後'\n";
		// extract error expiration_time data with member_id and last_process_time from t_crm_member_point
		// only extract records of members that has error expiration_time (NULL OR >2030-01-01)
		$old_member_flg_clause = $old_member_flg && $old_member_flg == 1 ? "join_channel='MI' AND old_member_flg=1" : "join_channel!='MI' AND (old_member_flg IS NULL OR old_member_flg!=1)";
		// $invalid_expiration_time_clause = "(expiration_time IS NULL OR expiration_time>=:invalid_expiration_time_start)";
		$point_sql = "SELECT Max(process_time) AS last_proc, member_id FROM `t_crm_member_point` WHERE member_id IN (SELECT member_id FROM t_crm_member_program WHERE program_id=:program_id AND (expiration_time IS NULL OR expiration_time>=:invalid_expiration_time_start) AND $old_member_flg_clause) AND program_id=:program_id AND transaction_type IN ('01','02','04') GROUP BY member_id";
		$point_query = DB::query(Database::SELECT, $point_sql);
		$point_query->parameters(array(
			':program_id' => $program_id,
			':invalid_expiration_time_start' => $invalid_expiration_time_start,
		));
		// $this->_write_log('SQL: '.$member_insert_query->compile(db: $this->_db));
		$target_members_with_last_process_time = $point_query->execute($this->_db)->as_array('member_id', 'last_proc');
		$info = "参照できる(transaction_type 01,02,04)ポイント処理時間(process_time)がある会員数: ".count($target_members_with_last_process_time);
		$this->_write_log($info);
		// update expiration_time with last_process_time in member_program
		// backup information prompt before apply update
		$member_program_select_sql = "SELECT * FROM t_crm_member_program WHERE program_id = :program_id AND (expiration_time IS NULL OR expiration_time>=:invalid_expiration_time_start) AND $old_member_flg_clause";
		$member_program_select_query = DB::query(Database::SELECT, $member_program_select_sql);
		$member_program_select_query->parameters(array(
			':program_id' => $program_id,
			':invalid_expiration_time_start' => $invalid_expiration_time_start,
		));
		$count=$this->_backup_prompt($member_program_select_query, "最後ポイント処理時間参照して");
		if($count > 0){
			// apply update
			$member_program_update_sql = "UPDATE t_crm_member_program SET expiration_time = :expiration_time WHERE program_id = :program_id AND member_id = :member_id AND (expiration_time IS NULL OR expiration_time>=:invalid_expiration_time_start) AND $old_member_flg_clause";
			$rows_updated = 0;
			$member_program_update_query = DB::query(Database::UPDATE, $member_program_update_sql);
			foreach ($target_members_with_last_process_time as $member_id => $last_process_time) {
				// last day of the month of one year later of last_process_time
				$extended_expiration_time = date('Y-m-t 23:59:59', strtotime('+1 year', strtotime($last_process_time)));
				$member_program_update_query->parameters(array(
					':program_id' => $program_id,
					':member_id' => $member_id,
					':invalid_expiration_time_start' => $invalid_expiration_time_start,
					':expiration_time' => $extended_expiration_time,
				));
				$updated = $member_program_update_query->execute($this->_db);
				$rows_updated += $updated;
				$info = "member_program table updated: $program_id > member_id = $member_id ".($updated ? '有効期限更新成功'.$extended_expiration_time:'会員データが見つかりません');
				$this->_write_log($info);
			}
			$info = "member_program table updated: $program_id > $rows_updated 名様有効期限を最後ポイント処理時間参照して更新成功";
			$this->_write_log($info);
		}
		// apply default expiration_time for members that has no valid expiration_time and no reference to point history
		echo "有効期間NULL又は不正の会員なのに、上記修正に含まれてない(ポイント処理時間参照がないため)会員にデフォルト有効期限更新しますか? Y: 更新する N: 更新しない(Enter for Y)? ";
		$input = rtrim(fgets(STDIN));
		if ($input == 'N' || $input == 'n') {
			$apply_default_expiration_time = false	;
		} else if ($input == 'Y' || $input == 'y' || $input == '') {
			$apply_default_expiration_time = true;
		}
		if ($apply_default_expiration_time) {
			echo "デフォルト有効期限として何月の月末ですか(Enter for デフォルト 2025-03)? ";
			$input = rtrim(fgets(STDIN));
			if ($input) {
				$default_expiration_time = $input;
			}
			$default_expiration_time = date('Y-m-t 23:59:59', strtotime($default_expiration_time));
			echo "デフォルト有効期限は $default_expiration_time\n";
			$member_program_select_sql = "SELECT * FROM t_crm_member_program WHERE program_id = :program_id AND (expiration_time IS NULL OR expiration_time>=:invalid_expiration_time_start) AND $old_member_flg_clause";
			$member_program_select_query = DB::query(Database::SELECT, $member_program_select_sql);
			$member_program_select_query->parameters(array(
				':program_id' => $program_id,
				':invalid_expiration_time_start' => $invalid_expiration_time_start,
			));
			$count = $this->_backup_prompt($member_program_select_query, "デフォルト有効期限で");
			if($count>0){
				$member_program_update_sql = "UPDATE t_crm_member_program SET expiration_time = :expiration_time WHERE program_id = :program_id AND (expiration_time IS NULL OR expiration_time>=:invalid_expiration_time_start) AND $old_member_flg_clause";
				$member_program_update_query = DB::query(Database::UPDATE, $member_program_update_sql);
				$member_program_update_query->parameters(array(
					':program_id' => $program_id,
					':invalid_expiration_time_start' => $invalid_expiration_time_start,
					':expiration_time' => $default_expiration_time,
				));
				$rows_updated = $member_program_update_query->execute($this->_db);
				$info = "member_program table updated: $program_id > default expiration_time = $default_expiration_time : $rows_updated 名様有効期限をデフォルト有効期限へ更新成功";
				$this->_write_log($info);
			}
		}

		//fix expiration_time to end of month
		//有効期限が月の途中になっているものは月末にする
		echo "有効期間が月の途中になっている会員は、有効期限をその月末に更新しますか? Y: 更新する N: 更新しない(Enter for Y)? ";
		$input = rtrim(fgets(STDIN));
		if ($input == 'N' || $input == 'n') {

		} else if ($input == 'Y' || $input == 'y' || $input == '') {
			$this->extend_exp_EOM($program_id);
		}
		$this->_write_log("=======END ".__FUNCTION__);
	}

	public function migrate($program_id='', $fast_mode=1, $unused_only=false, $bot_id=0, $migrate_date='')
	{
		$this->_write_log("=======START ".__FUNCTION__);
		try {
			$target_folder = APPPATH . "../../files/temp/member/";
			if (!$program_id) $program_id = $this->_default_program_id;
			$member_csv_files = glob($target_folder."MEMBER_*.{csv}", GLOB_BRACE);
			$imported_members = null;
			foreach($member_csv_files as $member_csv_file) {
				if (strpos($member_csv_file, 'MEMBER_CARD') !== false) {
					continue;
				}
				$info = 'ingesting member_csv_file(会員情報取り込み開始):'.$member_csv_file.' サイズ:'.filesize($member_csv_file);
				$this->_write_log($info);
				$imported_members = $this->migratemembers($member_csv_file, $program_id, $fast_mode, $unused_only);
			}
			$card_csv_files = glob($target_folder."MEMBER_CARD_*.{csv}", GLOB_BRACE);
			foreach($card_csv_files as $card_csv_file) {
				$info = 'ingesting card_csv_file(会員カード取り込み開始):'.$card_csv_file.' サイズ:'.filesize($card_csv_file);
				$this->_write_log($info);
				$this->migratecards($card_csv_file, $program_id, $unused_only);
			}
			$point_history_csv_files = glob($target_folder."POINT_HISTORY_*.{csv,txt}", GLOB_BRACE);
			foreach($point_history_csv_files as $point_history_csv_file) {
				$info = 'ingesting point_history_csv_file(ポイント履歴取り込み開始):'.$point_history_csv_file.' サイズ:'.filesize($point_history_csv_file);
				$this->_write_log($info);
				$this->migratepoints($point_history_csv_file, $program_id, $imported_members);
			}
		} catch (Exception $e) {
			$this->_write_log('Error: '.$e->getMessage(). ' on line '.$e->getLine());
			$this->_admin_model->log_info(__FUNCTION__, "パラメータ:migrate_date=$migrate_date 結果:failed".PHP_EOL.$e->getMessage(), $bot_id);
		}
		$this->_write_log("=======END ".__FUNCTION__);
	}

	/**
	 * csv_file files/temp/member/MEMBER.csv
	 * @param mixed $bot_id
	 * @param mixed $migrate_date
	 * @return array
	 */
	public function migratemembers($csv_file='', $program_id='T00002', $fast_mode =0, $unused_only=false, $bot_id = 0, $migrate_date='')
	{
		$this->_write_log("=======START ".__FUNCTION__);
		//read member csv file
		if (!$csv_file) $csv_file = APPPATH . "../../files/temp/member/MEMBER_debug.csv";//MEMBER_20241126095254.csv
		$csv_stream = fopen($csv_file,'r');
		
		$rows_inserted = 0;
		$imported_members = [];
		$checkedBOM = false;
		$isJIS = '';
		if ($csv_stream) {
			$line_count = 0;
			$time_start = time();
			$member_insert_sql = "INSERT INTO t_crm_member (email, first_name, last_name, first_name_furigana, last_name_furigana, post_cd, `address`, phone, birthday, gender) VALUE(:email, :first_name, :last_name, :first_name_furigana, :last_name_furigana, :post_cd, :address, :phone, :birthday, :gender)";
			$member_insert_query = DB::query(Database::INSERT, $member_insert_sql);
			// $member_insert_query = DB::insert($this->_t_member, $this->_member_insert_columns);
			$member_program_insert_sql = "INSERT INTO t_crm_member_program (member_id, program_id, member_cd, join_channel, join_shop_cd, join_time, `rank`, member_status, dm_allow_flg, dm_deliver_status, mail_allow_flg, mail_deliver_status, old_member_flg, old_member_cd, memo_data) VALUE(:member_id, :program_id, :member_cd, :join_channel, :join_shop_cd, :join_time, :rank, :member_status, :dm_allow_flg, :dm_deliver_status, :mail_allow_flg, :mail_deliver_status, :old_member_flg, :old_member_cd, :memo_data)";
			$member_program_insert_query = DB::query(Database::INSERT, $member_program_insert_sql);
			$join_channel = 'MI'; //migration
			$default_member_status = '01'; //新規
			$rows_updated = 0;
			while (($csv_line = fgets($csv_stream)) !== false) {
  
				$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
				$csv_row = str_getcsv($csv_line);	//	, "\t"
			  
				if ( $csv_row && $line_count == 0 && $this->_isCSVHeaderLine($csv_row, true) ) {
				  $this->_write_log('_isCSVHeaderLine(先頭行は各列のタイトルです): <'.$csv_line.'>');
				  continue;
				}
				$line_count ++;						

				// カード状態 check if card is active
				if (!$unused_only && $csv_row[30] != '使用中') {
					$this->_write_log('Card is not active(カード状態使用中ではない): '.$csv_row[0].'<'.$csv_row[30].'>');
					continue;
				} else if ($unused_only && $csv_row[30] != '未使用') {
					$this->_write_log('Card is not inactive(カード状態未使用だけMode): '.$csv_row[0].'<'.$csv_row[30].'>');
					continue;
				}

				// 「全角カタカナ」を「全角ひらがな」に変換します。
				// 「半角カタカナ」を「全角ひらがな」に変換します。
				$first_name_furigana = $csv_row[4]==NULL?NULL:mb_convert_kana($csv_row[4], 'Hc');
				$last_name_furigana = $csv_row[2]==NULL?NULL:mb_convert_kana($csv_row[2], 'Hc');
				//会員種別 check if member ID starts with R21
				$rank = 1; //デフォルトは一般会員
				if (strpos($csv_row[0], 'R21') === 0) {
					$rank = 2;
				}
				// $this->_write_log('CSV line: '.json_encode($csv_row, flags: JSON_UNESCAPED_UNICODE));
				$email = $csv_row[20] ? $csv_row[20] : ($csv_row[22] ? $csv_row[22] : NULL);	//PCメールアドレス優先
				$genders = array('その他'=>0, '男性'=>1, '女性'=>2, '' => 3);
				$address = ['prefecture'=>$csv_row[6],'city'=>$csv_row[7],'street'=>$csv_row[8],'building'=>''];
				$address_json = json_encode($address, JSON_UNESCAPED_UNICODE);
				$mobile = $csv_row[11] ? $csv_row[11] : ($csv_row[10] ? $csv_row[10] : NULL);
				$join_shop_cd = $this->_parseShopCode($csv_row[14]);
				$mail_allow_flg = 0;
				if ($csv_row[19] == 'PC・携帯に送信を希望する') {
					$mail_allow_flg = 1;
				} else if ($csv_row[19] == 'メール送信を希望しない') {
					$mail_allow_flg = 0;
				}
				$mail_deliver_status = 0;//デフォルト　停止中
				if ($csv_row[21] == 'PCメール送信可') {
					$mail_deliver_status += 2;
				} else if ($csv_row[21] == 'PCメール送信停止中') {
					$mail_deliver_status += 0;
				}
				if ($csv_row[23] == '携帯メール送信可') {
					$mail_deliver_status += 1;
				} else if ($csv_row[23] == '携帯メール送信停止中') {
					$mail_deliver_status += 0;
				}
				//memo1~10
				$memo = ['memo1' => $csv_row[31], 'memo2' => $csv_row[32], 'memo3' => $csv_row[33], 'memo4' => $csv_row[34], 'memo5' => $csv_row[35], 'memo6' => $csv_row[36], 'memo7' => $csv_row[37], 'memo8' => $csv_row[38], 'memo9' => $csv_row[39], 'memo10' => $csv_row[40]];
				$memo_json = json_encode($memo, JSON_UNESCAPED_UNICODE);
				$member_insert_query->parameters(array(
					':email' => $email,	//PCメールアドレス優先
					':first_name' => $this->_trim_full_width_name($csv_row[3]),
					':last_name' => $this->_trim_full_width_name($csv_row[1]),
					':first_name_furigana' => $first_name_furigana,
					':last_name_furigana' => $last_name_furigana,
					':post_cd' => $csv_row[5]??NULL,
					':address' => $address_json,
					':phone' => $mobile,	//携帯電話優先
					':birthday' => $csv_row[12]??NULL,
					':gender' => key_exists($csv_row[13], $genders)?$genders[$csv_row[13]]:0,
				));
				// $this->_write_log('SQL: '.$member_insert_query->compile(db: $this->_db));
				$member_inserted = $member_insert_query->execute($this->_db); // member_id
				$member_inserted_id = 0;
				if (!$member_inserted) {
					$this->_write_log('Failed to insert member: '.$csv_row[0]);
					continue;
				} else {
					$member_inserted_id = $member_inserted[0];
					$info = "member table inserted: member_id = $member_inserted_id, old member_cd = $csv_row[0] ";
					$this->_write_log($info);
				}
				$member_program_insert_query->parameters(array(
					':member_id' => $member_inserted_id,
					':program_id' => $program_id,
					':member_cd' => $fast_mode == 1 ? $this->_generate_member_cd_advance($program_id) : $this->_generate_member_cd($program_id),	//TODO performance speed up
					':join_channel' => $join_channel,
					':join_shop_cd' => $join_shop_cd, //入会店舗コード
					':join_time' => $csv_row[16], //カード登録日
					':rank' => $rank,
					':member_status' => $default_member_status,
					':dm_allow_flg' => $csv_row[17] == 'DM発行無'?0:($csv_row[17] == 'DM発行有'?1:0),
					':dm_deliver_status' => $csv_row[18] == '住所不明'?1:0,
					':mail_allow_flg' => $mail_allow_flg, //希望しない0 default
					':mail_deliver_status' => $mail_deliver_status,
					':old_member_flg' => 1,
					':old_member_cd' => $csv_row[0],
					':memo_data' => $memo_json,
				));
				// $this->_write_log('SQL: '.$member_program_insert_query->compile(db: $this->_db));
				$member_program_inserted = $member_program_insert_query->execute($this->_db); // member_id
				if (!$member_program_inserted) {
					$this->_write_log('Failed to insert member_program: '.$csv_row[0]);
					continue;
				} else if (is_array($member_program_inserted) && count($member_program_inserted) == 2) {
					$rows_updated += $member_program_inserted[1];
				}
				$info = "member_program table inserted: member_id = $member_inserted_id, old member_cd = $csv_row[0] ";
				$imported_members[] = $csv_row[0];
				$this->_write_log($info);
				// break;//TODO debug
			}
			$info = "member_program table inserted count (out of $line_count lines) : $program_id > $rows_updated ";
			$this->_write_log($info);
		}
		$this->_write_log("=======END ".__FUNCTION__);
		if ($migrate_date=='') {
			$migrate_date = date('Y-m-d H:i:s');
		}
		$this->_admin_model->log_info(__FUNCTION__, "パラメータ:migrate_date=$migrate_date 結果:success", 0);
		return $imported_members;
	}

	/**
	 * csv_file files/temp/member/MEMBER_CARD.csv
	 * @param mixed $bot_id
	 * @param mixed $migrate_date
	 * @return void
	 */
	public function migratecards($csv_file='', $program_id='T00002', $unused_only=false, $bot_id = 0, $migrate_date='')
	{
		$this->_write_log("=======START ".__FUNCTION__);
		//read member csv file
		if (!$csv_file) $csv_file = APPPATH . "../../files/temp/member/MEMBER_CARD_debug.csv";//MEMBER_CARD_20241126102625.csv
		$csv_stream = fopen($csv_file,'r');
		
		$rows_updated = 0;

		$checkedBOM = false;
		$isJIS = '';
		if ($csv_stream) {
			$line_count = 0;
			$time_start = time();
			$member_program_update_sql = "UPDATE t_crm_member_program SET point_balance = :point_balance, expiration_time = :expiration_time WHERE program_id = :program_id AND old_member_cd = :old_member_cd AND old_member_flg = 1 AND join_channel = 'MI'";
			$member_program_update_query = DB::query(Database::UPDATE, $member_program_update_sql);
			while (($csv_line = fgets($csv_stream)) !== false) {
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($csv_line, 'SJIS-mac') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($csv_line, 'SJIS-win') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($csv_line, 'SJIS') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$checkedBOM = true;
				} else if ($isJIS) {
					$csv_line = mb_convert_encoding($csv_line, 'UTF-8', $isJIS);
				}
				$csv_row = str_getcsv($csv_line);	//	, "\t"

				if ( $csv_row && $line_count == 0 && $this->_isCSVHeaderLine($csv_row, true) ) {
					$this->_write_log('_isCSVHeaderLine(先頭行は各列のタイトルです): <'.$csv_line.'>');
					continue;
				}
				$line_count ++;					

				// カード状態 check if card is active
				if (!$unused_only && $csv_row[9] != '使用中') {
					$this->_write_log('Card is not active(カード状態使用中ではない): '.$csv_row[3].'<'.$csv_row[9].'>');
					continue;
				} else if ($unused_only && $csv_row[9] != '未使用') {
					$this->_write_log('Card is not inactive(カード状態未使用だけMode): '.$csv_row[3].'<'.$csv_row[9].'>');
					continue;
				}
				// expiration_time = 2104/06, parse from yyyy/MM to date 
				$expiration_time = $csv_row[13];
				$expiration_time = date_parse_from_format('Y/m', $expiration_time);
				$expiration_time = $expiration_time['year'].'-'.$expiration_time['month'].'-01';
				// last day of month
				$expiration_time = date('Y-m-t 23:59:59', strtotime($expiration_time));
				// $this->_write_log('CSV line: '.json_encode($csv_row, JSON_UNESCAPED_UNICODE));
				$member_program_update_query->parameters(array(
					':program_id' => $program_id,
					':old_member_cd' => $csv_row[3],
					':point_balance' => $csv_row[8],
					':expiration_time' => $expiration_time,
				));
				// $this->_write_log('SQL: '.$member_program_update_query->compile(db: $this->_db));
				$updated = $member_program_update_query->execute($this->_db); // member_id
				$rows_updated += $updated;
				$info = "member_program table updated: $program_id > old member_cd = $csv_row[3] ".($updated ? 'カード情報更新成功':'会員データが見つかりません');
				$this->_write_log($info);
			}
			$info = "member_program table updated count (out of $line_count lines) : $program_id > $rows_updated ";
			$this->_write_log($info);
		}
		$this->_write_log("=======END ".__FUNCTION__);
		if ($migrate_date=='') {
			$migrate_date = date('Y-m-d H:i:s');
		}
		$this->_admin_model->log_info(__FUNCTION__, "パラメータ:migrate_date=$migrate_date 結果:success", 0);
	}

	/**
	 * csv_file files/temp/member/POINT_HISTORY_RPR.csv, POINT_HISTORY_ミケーラ.csv
	 * @param mixed $bot_id
	 * @param mixed $migrate_date
	 * @return void
	 */
	public function migratepoints($csv_file="", $program_id='T00002', $imported_members=null, $bot_id = 0, $migrate_date='')
	{
		$this->_write_log("=======START ".__FUNCTION__);
		//read member csv file
		if (!$csv_file) {
			$csv_file = APPPATH . "../../files/temp/member/POINT_HISTORY_debug.txt";//POINT_HISTORY_RPR20241126100243.csv POINT_HISTORY_ミケーラ20241126095945.txt
		}
		$csv_stream = fopen($csv_file,'r');
		
		$rows_inserted = 0;

		$checkedBOM = false;
		$isJIS = '';
		if ($csv_stream) {
			$line_count = 0;
			$time_start = time();
			$point_history_insert_sql = "INSERT INTO  t_crm_member_point SET process_time = :process_time, shop_cd = :shop_cd, transaction_type = :transaction_type, point_sales_amount = :point_sales_amount, `point` = :point, program_id = :program_id, point_data = :point_data, member_id = (SELECT member_id FROM t_crm_member_program WHERE program_id = :program_id AND old_member_cd = :old_member_cd AND old_member_flg = 1 AND join_channel = 'MI')";
			$point_history_insert_query = DB::query(Database::INSERT, $point_history_insert_sql);
			$rows_updated = 0;
			while (($csv_line = fgets($csv_stream)) !== false) {
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($csv_line, 'SJIS-mac') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($csv_line, 'SJIS-win') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($csv_line, 'SJIS') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$checkedBOM = true;
				} else if ($isJIS) {
					$csv_line = mb_convert_encoding($csv_line, 'UTF-8', $isJIS);
				}
				$csv_row = str_getcsv($csv_line);	//	, "\t"

				if ( $csv_row && $line_count == 0 && $this->_isCSVHeaderLine($csv_row, true) ) {
					$this->_write_log('_isCSVHeaderLine(先頭行は各列のタイトルです): <'.$csv_line.'>');
					continue;
				}
				switch ($csv_row[11]) {
					case 'ポイント-付与':
						$transaction_type = '01';
						break;
					case 'ポイント-加算':
						$transaction_type = '02';
						break;
					case 'ポイント-減算':
						$transaction_type = '03';
						break;
					case 'ポイント-交換':
						$transaction_type = '04';
						break;
					case 'ポイント-失効':
						$transaction_type = '05';
						break;
					case 'ポイント-カード移行':
						$transaction_type = '06';
						break;
					case 'ポイント-取り消し':
						$transaction_type = '07';
						break;
					case 'ポイント-取消':
						$transaction_type = '07';
						break;
					default:
						$this->_write_log('transaction_type is not unkown(取引内容): '.$csv_row[11]);
						continue 2;
				}
				$line_count ++;					
				//TODO process
				//2022/11/04 13:58:38
				$process_time = date('Y-m-d H:i:s', strtotime($csv_row[0]));
				$old_member_cd = $csv_row[4];

				// increment mode
				if ($imported_members && count($imported_members)> 0 && !in_array($old_member_cd, $imported_members)) {
					$this->_write_log('Member not new imported(増量Modeで、既存会員情報が取り込まれていません): '.$old_member_cd);
					continue;
				}

				$shop_cd = $this->_parseShopCode($csv_row[7]);
				$point_data = ['old' => ['approval_no' => $csv_row[1], 'sub_no' => $csv_row[2], 'card_master_name' => $csv_row[3], 'TID' => $csv_row[9], 'terminal_cycle_no' => $csv_row[10]]];
				$point_data_json = json_encode($point_data, JSON_UNESCAPED_UNICODE);
				// $this->_write_log('CSV line: '.json_encode($csv_row, JSON_UNESCAPED_UNICODE));
				$point_history_insert_query->parameters(array(
					':program_id' => $program_id,
					':old_member_cd' => $old_member_cd,
					':shop_cd' => $shop_cd, //店舗コード
					':transaction_type' => $transaction_type,
					':point_sales_amount' => $csv_row[12],
					':point' => $csv_row[13],
					':point_data' => $point_data_json,
					':process_time' => $process_time,
				));
				// $this->_write_log('SQL: '.$point_history_insert_query->compile(db: $this->_db));
				try {
					$point_history_inserted = $point_history_insert_query->execute($this->_db); // member_id
					// $this->_write_log('inserted: '.json_encode($point_history_inserted, JSON_UNESCAPED_UNICODE));
					if (!$point_history_inserted) {
						$this->_write_log('Failed to insert point_history: '.$old_member_cd);
						continue;
					} else if (is_array($point_history_inserted) && count($point_history_inserted) == 2) {
						$rows_updated += $point_history_inserted[1];
					}
					$info = "point_history table inserted: recode no = $point_history_inserted[0], old member_cd = $old_member_cd ";
					$this->_write_log($info);
					// break;//TODO debug
				} catch (mysqli_sql_exception $e) {
					if ($e->getCode() == 1062) {
						$this->_write_log('Duplicate entry(1062重複エントリ): '.$old_member_cd.' '.$e->getMessage());
						continue;
					} else if ($e->getCode() == 1048 && strpos($e->getMessage(), "Column 'member_id' cannot be null") !== false) {
						$this->_write_log('会員情報はt_crm_memberに取り込みされていないので、point_historyに挿入できません: '.$csv_row[4]);
						continue;
					} else {
						throw $e;
					}
				}				
			}
			$info = "point_history table inserted count (out of $line_count lines) : $program_id > $rows_updated ";
			$this->_write_log($info);
		}
		$this->_write_log("=======END ".__FUNCTION__);
		if ($migrate_date=='') {
			$migrate_date = date('Y-m-d H:i:s');
		}
		$this->_admin_model->log_info(__FUNCTION__, "パラメータ:migrate_date=$migrate_date 結果:success", 0);
	}

	private function _generate_member_cd_advance($program_id) {
		if ($this->_last_member_no == 0) {
			return $this->_generate_member_cd($program_id);
		} else {
			$next_number = $this->_last_member_no + 1;
			// 上限チェック（8桁を超える場合）
            if ($next_number > 99999999) {
                throw new Exception('member_cd_overflow');
            }
            $member_cd = sprintf('%08d', $next_number);
			$this->_last_member_no = $next_number;
            return $member_cd;
		}
	}

	private function _generate_member_cd($program_id) {
        try {
            // 現在の最大値を取得
            $query = DB::select([DB::expr('MAX(member_cd)'), 'max_cd'])
                ->from('t_crm_member_program')
                ->where('program_id', '=', $program_id)
                ->where('member_cd', 'IS NOT', null)
                ->where('member_cd', 'REGEXP', '^[0-9]{8}$');  // 8桁の数字のみ
            $result = $query->execute($this->_db)->current();
            // 最大値が存在しない場合は初期値、存在する場合は+1
            $next_number = empty($result['max_cd']) ? 1 : ((int)$result['max_cd'] + 1);
            // 上限チェック（8桁を超える場合）
            if ($next_number > 99999999) {
                throw new Exception('member_cd_overflow');
            }
            $member_cd = sprintf('%08d', $next_number);
			$this->_last_member_no = $next_number;
            return $member_cd;
        } catch (Database_Exception $e) {
            throw new Exception('member_cd_generation_failed');
        }
    }

	private function _parseTransactionType($transaction_type_string): mixed {
		switch ($transaction_type_string) {
			case 'ポイント-付与':
				return '01';
			case 'ポイント-加算':
				return '02';
			case 'ポイント-減算':
				return '03';
			case 'ポイント-交換':
				return '04';
			case 'ポイント-失効':
				return '05';
			case 'ポイント-カード移行':
				return '06';
			case 'ポイント-取消':
				return '07';
			default:
				return NULL;
		}
	}

	private function _parseShopCode($shop_code): mixed {
		$shop_code_int = intval($shop_code);
		$shop_cd = str_pad($shop_code_int, 4, '0', STR_PAD_LEFT);
		return $shop_cd;
	}

	private function _isCSVHeaderLine(&$data, $trim) {
		if ($trim)
			foreach ($data as $key => $value) {
				$data[$key] = trim($value);
			}
		return $data[0] == "会員番号" || $data[0] == "カードマスタID" || $data[0] == "処理日";
	}

	private function _trim_full_width_name($str) {
		$str = preg_replace('/\A[\pZ\pC]+|[\pZ\pC]+\z/u', '', $str);
		$str = preg_replace('/[\pZ\pC\s]*(P|RPC|LOR|RPC)[\pZ\pC]*\z/u', '', $str);
		return $str;
	}
}